import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { User, LoginCredentials, RegisterData } from '@/types/api';

// Create a context for the auth state
interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Create a provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = () => {
      const storedToken = localStorage.getItem('token');
      const storedUser = localStorage.getItem('user');

      if (storedToken && storedUser) {
        try {
          setToken(storedToken);

          // Set the token in the API instance
          authAPI.setAuthToken(storedToken);

          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        } catch (error) {
          console.error('Failed to parse stored user:', error);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
        }
      }

      setIsLoading(false);
    };

    initAuth();
  }, []);

  // Login function
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    setIsLoading(true);

    try {
      const response = await authAPI.login(credentials);

      // Debug: Log the response structure
      console.log('Login response:', response);

      // Handle different response structures
      let newToken: string;
      let userData: User;

      // Check if response.data has the expected structure
      if (response.data && response.data.data) {
        // Standard structure: response.data.data contains token and user
        if (response.data.data.token && response.data.data.user) {
          newToken = response.data.data.token;
          userData = response.data.data.user;
        }
        // Alternative structure: response.data contains token and user directly
        else if (response.data.token && response.data.user) {
          newToken = response.data.token;
          userData = response.data.user;
        }
        // Another possible structure: response.data.data is the token, and user is elsewhere
        else if (typeof response.data.data === 'string' && response.data.user) {
          newToken = response.data.data;
          userData = response.data.user;
        } else {
          throw new Error(
            'Invalid response format: missing token or user data'
          );
        }
      }
      // Fallback structure: response.data contains token and user directly
      else if (response.data && response.data.token && response.data.user) {
        newToken = response.data.token;
        userData = response.data.user;
      } else {
        // Log the error for debugging
        console.error('Unexpected response structure:', response);
        throw new Error('Invalid response format: missing token or user data');
      }

      // Set the token in the API instance
      authAPI.setAuthToken(newToken);

      // Save to state
      setToken(newToken);
      setUser(userData);

      // Save to localStorage
      localStorage.setItem('token', newToken);
      localStorage.setItem('user', JSON.stringify(userData));

      toast({
        title: 'Login Successful',
        description: `Welcome back, ${userData.fullName}!`,
      });

      // Redirect based on user role
      if (
        ['admin', 'super-admin', 'verifier', 'course-coordinator'].includes(
          userData.role
        )
      ) {
        navigate('/admin/dashboard');
      } else {
        navigate('/candidate/dashboard');
      }

      return true;
    } catch (error: any) {
      console.error('Login error:', error);

      const errorMessage =
        error.response?.data?.message ||
        'Invalid email or password. Please try again.';

      toast({
        title: 'Login Failed',
        description: errorMessage,
        variant: 'destructive',
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (data: RegisterData): Promise<boolean> => {
    setIsLoading(true);

    try {
      const response = await authAPI.register(data);

      // Handle different response structures
      let newToken: string;
      let userData: User;

      // Check if response.data has the expected structure
      if (response.data && response.data.data) {
        // Standard structure: response.data.data contains token and user
        if (response.data.data.token && response.data.data.user) {
          newToken = response.data.data.token;
          userData = response.data.data.user;
        }
        // Alternative structure: response.data contains token and user directly
        else if (response.data.token && response.data.user) {
          newToken = response.data.token;
          userData = response.data.user;
        } else {
          throw new Error(
            'Invalid response format: missing token or user data'
          );
        }
      }
      // Fallback structure: response.data contains token and user directly
      else if (response.data && response.data.token && response.data.user) {
        newToken = response.data.token;
        userData = response.data.user;
      } else {
        // Log the error for debugging
        console.error('Unexpected response structure:', response);
        throw new Error('Invalid response format: missing token or user data');
      }

      // Set the token in the API instance
      authAPI.setAuthToken(newToken);

      // Save to state
      setToken(newToken);
      setUser(userData);

      // Save to localStorage
      localStorage.setItem('token', newToken);
      localStorage.setItem('user', JSON.stringify(userData));

      toast({
        title: 'Registration Successful',
        description: `Welcome to GyaanRaksha Samyog Portal, ${userData.fullName}!`,
      });

      // Redirect to candidate dashboard
      navigate('/candidate/dashboard');

      return true;
    } catch (error: any) {
      console.error('Registration error:', error);

      const errorMessage =
        error.response?.data?.message ||
        'Registration failed. Please try again.';

      toast({
        title: 'Registration Failed',
        description: errorMessage,
        variant: 'destructive',
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = useCallback(() => {
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Clear token from API instance
    authAPI.clearAuthToken();

    // Update state
    setToken(null);
    setUser(null);

    // Redirect to login page
    navigate('/login');

    toast({
      title: 'Logged Out',
      description: 'You have been successfully logged out.',
    });
  }, [navigate, toast]);

  // Update user function
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        isLoading,
        isAuthenticated: !!user,
        login,
        register,
        logout,
        updateUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Create a hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
