{"name": "tiny-inflate", "version": "1.0.3", "description": "A tiny inflate implementation", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/devongovett/tiny-inflate.git"}, "keywords": ["inflate", "zlib", "gzip", "zip"], "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/devongovett/tiny-inflate/issues"}, "homepage": "https://github.com/devongovett/tiny-inflate", "devDependencies": {"mocha": "^2.1.0"}}