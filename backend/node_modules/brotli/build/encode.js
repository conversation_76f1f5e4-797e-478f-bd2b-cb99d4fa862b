var b={},aa=require("../decompress"),ba=require("base64-js");b.readBinary=function(){var a=ba.toByteArray(require("../build/mem.js"));return aa(a)};b||(b=eval("(function() { try { return Module || {} } catch(e) { return {} } })()"));var g={},m;for(m in b)b.hasOwnProperty(m)&&(g[m]=b[m]);var t="object"===typeof window,u="function"===typeof importScripts,v="object"===typeof process&&"function"===typeof require&&!t&&!u,x=!t&&!v&&!u;
if(v){b.print||(b.print=function(a){process.stdout.write(a+"\n")});b.printErr||(b.printErr=function(a){process.stderr.write(a+"\n")});var ca=require("fs"),da=require("path");b.read=function(a,c){a=da.normalize(a);var d=ca.readFileSync(a);d||a==da.resolve(a)||(a=path.join(__dirname,"..","src",a),d=ca.readFileSync(a));d&&!c&&(d=d.toString());return d};b.readBinary=function(a){a=b.read(a,!0);a.buffer||(a=new Uint8Array(a));assert(a.buffer);return a};b.load=function(a){ea(read(a))};b.thisProgram||(b.thisProgram=
1<process.argv.length?process.argv[1].replace(/\\/g,"/"):"unknown-program");b.arguments=process.argv.slice(2);"undefined"!==typeof module&&(module.exports=b);process.on("uncaughtException",function(a){if(!(a instanceof y))throw a;});b.inspect=function(){return"[Emscripten Module object]"}}else if(x)b.print||(b.print=print),"undefined"!=typeof printErr&&(b.printErr=printErr),b.read="undefined"!=typeof read?read:function(){throw"no read() available (jsc?)";},b.readBinary=function(a){if("function"===
typeof readbuffer)return new Uint8Array(readbuffer(a));a=read(a,"binary");assert("object"===typeof a);return a},"undefined"!=typeof scriptArgs?b.arguments=scriptArgs:"undefined"!=typeof arguments&&(b.arguments=arguments),eval("if (typeof gc === 'function' && gc.toString().indexOf('[native code]') > 0) var gc = undefined");else if(t||u)b.read=function(a){var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},"undefined"!=typeof arguments&&(b.arguments=arguments),"undefined"!==
typeof console?(b.print||(b.print=function(a){console.log(a)}),b.printErr||(b.printErr=function(a){console.log(a)})):b.print||(b.print=function(){}),u&&(b.load=importScripts),"undefined"===typeof b.setWindowTitle&&(b.setWindowTitle=function(a){document.title=a});else throw"Unknown runtime environment. Where are we?";function ea(a){eval.call(null,a)}!b.load&&b.read&&(b.load=function(a){ea(b.read(a))});b.print||(b.print=function(){});b.printErr||(b.printErr=b.print);b.arguments||(b.arguments=[]);
b.thisProgram||(b.thisProgram="./this.program");b.print=b.print;b.A=b.printErr;b.preRun=[];b.postRun=[];for(m in g)g.hasOwnProperty(m)&&(b[m]=g[m]);
var A={P:function(a){fa=a},N:function(){return fa},G:function(){return z},F:function(a){z=a},D:function(a){switch(a){case "i1":case "i8":return 1;case "i16":return 2;case "i32":return 4;case "i64":return 8;case "float":return 4;case "double":return 8;default:return"*"===a[a.length-1]?A.o:"i"===a[0]?(a=parseInt(a.substr(1)),assert(0===a%8),a/8):0}},M:function(a){return Math.max(A.D(a),A.o)},R:16,ea:function(a,c){"double"===c||"i64"===c?a&7&&(assert(4===(a&7)),a+=4):assert(0===(a&3));return a},Y:function(a,
c,d){return d||"i64"!=a&&"double"!=a?a?Math.min(c||(a?A.M(a):0),A.o):Math.min(c,8):8},r:function(a,c,d){return d&&d.length?(d.splice||(d=Array.prototype.slice.call(d)),d.splice(0,0,c),b["dynCall_"+a].apply(null,d)):b["dynCall_"+a].call(null,c)},k:[],H:function(a){for(var c=0;c<A.k.length;c++)if(!A.k[c])return A.k[c]=a,2*(1+c);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS.";},O:function(a){A.k[(a-2)/2]=null},e:function(a){A.e.B||(A.e.B={});A.e.B[a]||
(A.e.B[a]=1,b.A(a))},u:{},$:function(a,c){assert(c);A.u[c]||(A.u[c]={});var d=A.u[c];d[a]||(d[a]=function(){return A.r(c,a,arguments)});return d[a]},Z:function(){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work";},n:function(a){var c=z;z=z+a|0;z=z+15&-16;return c},Q:function(a){var c=B;B=B+a|0;B=B+15&-16;return c},g:function(a){var c=C;C=C+a|0;C=C+15&-16;if(a=C>=D)E("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+
D+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which adjusts the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 "),a=!0;return a?(C=c,0):c},p:function(a,c){return Math.ceil(a/(c?c:16))*(c?c:16)},da:function(a,c,d){return d?+(a>>>0)+4294967296*+(c>>>0):+(a>>>0)+4294967296*+(c|0)},C:8,o:4,S:0};A.addFunction=A.H;
A.removeFunction=A.O;var F=!1,G,H,fa;function assert(a,c){a||E("Assertion failed: "+c)}(function(){var a={stackSave:function(){A.G()},stackRestore:function(){A.F()},arrayToC:function(a){for(var c=A.n(a.length),d=c,k=0;k<a.length;k++)I[d++>>0]=a[k];return c},stringToC:function(a){var c=0;null!==a&&void 0!==a&&0!==a&&(c=A.n((a.length<<2)+1),ga(a,c));return c}},c=/^function\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/,d;for(d in a)a.hasOwnProperty(d)&&a[d].toString().match(c).slice(1)})();
function ha(a){var c;c="i32";"*"===c.charAt(c.length-1)&&(c="i32");switch(c){case "i1":return I[a>>0];case "i8":return I[a>>0];case "i16":return J[a>>1];case "i32":return K[a>>2];case "i64":return K[a>>2];case "float":return L[a>>2];case "double":return N[a>>3];default:E("invalid type for setValue: "+c)}return null}
function O(a,c,d){var e,h,l;"number"===typeof a?(h=!0,l=a):(h=!1,l=a.length);var k="string"===typeof c?c:null;d=4==d?e:[ia,A.n,A.Q,A.g][void 0===d?2:d](Math.max(l,k?1:c.length));if(h){e=d;assert(0==(d&3));for(a=d+(l&-4);e<a;e+=4)K[e>>2]=0;for(a=d+l;e<a;)I[e++>>0]=0;return d}if("i8"===k)return a.subarray||a.slice?P.set(a,d):P.set(new Uint8Array(a),d),d;e=0;for(var f,M;e<l;){var r=a[e];"function"===typeof r&&(r=A.aa(r));h=k||c[e];if(0===h)e++;else{"i64"==h&&(h="i32");var w=d+e,q=h,q=q||"i8";"*"===q.charAt(q.length-
1)&&(q="i32");switch(q){case "i1":I[w>>0]=r;break;case "i8":I[w>>0]=r;break;case "i16":J[w>>1]=r;break;case "i32":K[w>>2]=r;break;case "i64":H=[r>>>0,(G=r,1<=+ja(G)?0<G?(ka(+la(G/4294967296),4294967295)|0)>>>0:~~+ma((G-+(~~G>>>0))/4294967296)>>>0:0)];K[w>>2]=H[0];K[w+4>>2]=H[1];break;case "float":L[w>>2]=r;break;case "double":N[w>>3]=r;break;default:E("invalid type for setValue: "+q)}M!==h&&(f=A.D(h),M=h);e+=f}}return d}
function Q(a){var c;if(0===c||!a)return"";for(var d=0,e,h=0;;){e=P[a+h>>0];d|=e;if(0==e&&!c)break;h++;if(c&&h==c)break}c||(c=h);e="";if(128>d){for(;0<c;)d=String.fromCharCode.apply(String,P.subarray(a,a+Math.min(c,1024))),e=e?e+d:d,a+=1024,c-=1024;return e}return b.UTF8ToString(a)}
function oa(a){function c(d,e,h){e=e||Infinity;var l="",k=[],p;if("N"===a[f]){f++;"K"===a[f]&&f++;for(p=[];"E"!==a[f];)if("S"===a[f]){f++;var n=a.indexOf("_",f);p.push(r[a.substring(f,n)||0]||"?");f=n+1}else if("C"===a[f])p.push(p[p.length-1]),f+=2;else{var n=parseInt(a.substr(f)),q=n.toString().length;if(!n||!q){f--;break}var na=a.substr(f+q,n);p.push(na);r.push(na);f+=q+n}f++;p=p.join("::");e--;if(0===e)return d?[p]:p}else if(("K"===a[f]||w&&"L"===a[f])&&f++,n=parseInt(a.substr(f)))q=n.toString().length,
p=a.substr(f+q,n),f+=q+n;w=!1;"I"===a[f]?(f++,n=c(!0),q=c(!0,1,!0),l+=q[0]+" "+p+"<"+n.join(", ")+">"):l=p;a:for(;f<a.length&&0<e--;)if(p=a[f++],p in M)k.push(M[p]);else switch(p){case "P":k.push(c(!0,1,!0)[0]+"*");break;case "R":k.push(c(!0,1,!0)[0]+"&");break;case "L":f++;n=a.indexOf("E",f)-f;k.push(a.substr(f,n));f+=n+2;break;case "A":n=parseInt(a.substr(f));f+=n.toString().length;if("_"!==a[f])throw"?";f++;k.push(c(!0,1,!0)[0]+" ["+n+"]");break;case "E":break a;default:l+="?"+p;break a}h||1!==
k.length||"void"!==k[0]||(k=[]);return d?(l&&k.push(l+"?"),k):l+("("+k.join(", ")+")")}var d=!!b.___cxa_demangle;if(d)try{var e=ia(a.length);ga(a.substr(1),e);var h=ia(4),l=b.___cxa_demangle(e,0,0,h);if(0===ha(h)&&l)return Q(l)}catch(k){}finally{e&&pa(e),h&&pa(h),l&&pa(l)}var f=3,M={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},
r=[],w=!0,e=a;try{if("Object._main"==a||"_main"==a)return"main()";"number"===typeof a&&(a=Q(a));if("_"!==a[0]||"_"!==a[1]||"Z"!==a[2])return a;switch(a[3]){case "n":return"operator new()";case "d":return"operator delete()"}e=c()}catch(q){e+="?"}0<=e.indexOf("?")&&!d&&A.e("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling");return e}
function qa(){return ra().replace(/__Z[\w\d_]+/g,function(a){var c=oa(a);return a===c?a:a+" ["+c+"]"})}function ra(){var a=Error();if(!a.stack){try{throw Error(0);}catch(c){a=c}if(!a.stack)return"(no stack trace available)"}return a.stack.toString()}function sa(){var a=C;0<a%4096&&(a+=4096-a%4096);return a}for(var I,P,J,ta,K,ua,L,N,va=0,B=0,wa=0,z=0,xa=0,ya=0,C=0,za=b.TOTAL_STACK||5242880,D=b.TOTAL_MEMORY||318767104,R=65536;R<D||R<2*za;)R=16777216>R?2*R:R+16777216;R!==D&&(D=R);
assert("undefined"!==typeof Int32Array&&"undefined"!==typeof Float64Array&&!!(new Int32Array(1)).subarray&&!!(new Int32Array(1)).set,"JS engine does not provide full typed array support");var buffer;buffer=new ArrayBuffer(D);I=new Int8Array(buffer);J=new Int16Array(buffer);K=new Int32Array(buffer);P=new Uint8Array(buffer);ta=new Uint16Array(buffer);ua=new Uint32Array(buffer);L=new Float32Array(buffer);N=new Float64Array(buffer);K[0]=255;assert(255===P[0]&&0===P[3],"Typed arrays 2 must be run on a little-endian system");
b.HEAP=void 0;b.buffer=buffer;b.HEAP8=I;b.HEAP16=J;b.HEAP32=K;b.HEAPU8=P;b.HEAPU16=ta;b.HEAPU32=ua;b.HEAPF32=L;b.HEAPF64=N;function S(a){for(;0<a.length;){var c=a.shift();if("function"==typeof c)c();else{var d=c.X;"number"===typeof d?void 0===c.q?A.r("v",d):A.r("vi",d,[c.q]):d(void 0===c.q?null:c.q)}}}var Aa=[],Ba=[],Ca=[],Da=[],Ea=[],T=!1;function Fa(){var a=b.preRun.shift();Aa.unshift(a)}
function Ga(a,c){for(var d=0,e=0;e<a.length;++e){var h=a.charCodeAt(e);55296<=h&&57343>=h&&(h=65536+((h&1023)<<10)|a.charCodeAt(++e)&1023);127>=h?++d:d=2047>=h?d+2:65535>=h?d+3:2097151>=h?d+4:67108863>=h?d+5:d+6}d=Array(d+1);var l=d.length,e=0;if(0<l){for(var h=e,l=e+l-1,k=0;k<a.length;++k){var f=a.charCodeAt(k);55296<=f&&57343>=f&&(f=65536+((f&1023)<<10)|a.charCodeAt(++k)&1023);if(127>=f){if(e>=l)break;d[e++]=f}else{if(2047>=f){if(e+1>=l)break;d[e++]=192|f>>6}else{if(65535>=f){if(e+2>=l)break;d[e++]=
224|f>>12}else{if(2097151>=f){if(e+3>=l)break;d[e++]=240|f>>18}else{if(67108863>=f){if(e+4>=l)break;d[e++]=248|f>>24}else{if(e+5>=l)break;d[e++]=252|f>>30;d[e++]=128|f>>24&63}d[e++]=128|f>>18&63}d[e++]=128|f>>12&63}d[e++]=128|f>>6&63}d[e++]=128|f&63}}d[e]=0;e=e-h}else e=0;c&&(d.length=e);return d}function ga(a,c){for(var d=Ga(a,void 0),e=0;e<d.length;)I[c+e>>0]=d[e],e+=1}
Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(a,c){var d=a&65535,e=c&65535;return d*e+((a>>>16)*e+d*(c>>>16)<<16)|0});Math.ba=Math.imul;Math.clz32||(Math.clz32=function(a){a=a>>>0;for(var c=0;32>c;c++)if(a&1<<31-c)return c;return 32});Math.V=Math.clz32;var ja=Math.abs,ma=Math.ceil,la=Math.floor,ka=Math.min,U=0,Ha=null,V=null;b.preloadedImages={};b.preloadedAudios={};var W=null,va=8,B=va+407936;Ba.push();var W="encode.js.mem",Ia=A.p(O(12,"i8",2),8);assert(0==Ia%8);b._i64Subtract=Ja;
function Ka(a){b.___errno_location&&(K[b.___errno_location()>>2]=a);return a}b._memset=La;b._bitshift64Lshr=Ma;b._bitshift64Shl=Na;b._memcpy=Oa;b._i64Add=Pa;function X(a){X.L||(C=sa(),X.L=!0,assert(A.g),X.K=A.g,A.g=function(){E("cannot dynamically allocate, sbrk now has control")});var c=C;return 0==a||X.K(a)?c:4294967295}b._memmove=Qa;function Ra(a){b.exit(a)}wa=z=A.p(B);xa=wa+za;ya=C=A.p(xa);assert(ya<D,"TOTAL_MEMORY not big enough for stack");
var Sa=O([8,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,7,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,
1,0,3,0,1,0,2,0,1,0],"i8",3);b.I={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:Infinity};
b.J={abort:E,assert:assert,invoke_vii:function(a,c,d){try{b.dynCall_vii(a,c,d)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Y.setThrew(1,0)}},invoke_iii:function(a,c,d){try{return b.dynCall_iii(a,c,d)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Y.setThrew(1,0)}},__exit:Ra,_pthread_self:function(){return 0},_abort:function(){b.abort()},___setErrNo:Ka,_sysconf:function(a){switch(a){case 30:return 4096;case 85:return R/4096;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;
case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;
case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1E3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"===typeof navigator?navigator.hardwareConcurrency||1:1}Ka(22);return-1},
_sbrk:X,_time:function(a){var c=Date.now()/1E3|0;a&&(K[a>>2]=c);return c},_emscripten_memcpy_big:function(a,c,d){P.set(P.subarray(c,c+d),a);return a},_llvm_trap:function(){E("trap!")},_exit:function(a){Ra(a)},___assert_fail:function(a,c,d,e){F=!0;throw"Assertion failed: "+Q(a)+", at: "+[c?Q(c):"unknown filename",d,e?Q(e):"unknown function"]+" at "+qa();},STACKTOP:z,STACK_MAX:xa,tempDoublePtr:Ia,ABORT:F,cttz_i8:Sa};// EMSCRIPTEN_START_ASM

var Y=(function(global,env,buffer) {
"use asm";var a=new global.Int8Array(buffer);var b=new global.Int16Array(buffer);var c=new global.Int32Array(buffer);var d=new global.Uint8Array(buffer);var e=new global.Uint16Array(buffer);var f=new global.Uint32Array(buffer);var g=new global.Float32Array(buffer);var h=new global.Float64Array(buffer);var i=env.STACKTOP|0;var j=env.STACK_MAX|0;var k=env.tempDoublePtr|0;var l=env.ABORT|0;var m=env.cttz_i8|0;var n=0;var o=0;var p=0;var q=0;var r=global.NaN,s=global.Infinity;var t=0,u=0,v=0,w=0,x=0.0,y=0,z=0,A=0,B=0.0;var C=0;var D=0;var E=0;var F=0;var G=0;var H=0;var I=0;var J=0;var K=0;var L=0;var M=global.Math.floor;var N=global.Math.abs;var O=global.Math.sqrt;var P=global.Math.pow;var Q=global.Math.cos;var R=global.Math.sin;var S=global.Math.tan;var T=global.Math.acos;var U=global.Math.asin;var V=global.Math.atan;var W=global.Math.atan2;var X=global.Math.exp;var Y=global.Math.log;var Z=global.Math.ceil;var _=global.Math.imul;var $=global.Math.min;var aa=global.Math.clz32;var ba=env.abort;var ca=env.assert;var da=env.invoke_vii;var ea=env.invoke_iii;var fa=env.__exit;var ga=env._pthread_self;var ha=env._abort;var ia=env.___setErrNo;var ja=env._sysconf;var ka=env._sbrk;var la=env._time;var ma=env._emscripten_memcpy_big;var na=env._llvm_trap;var oa=env._exit;var pa=env.___assert_fail;var qa=0.0;
// EMSCRIPTEN_START_FUNCS
function $a(e,f,g,h,j,k,l,m){e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0;G=i;i=i+16|0;F=G;c[F>>2]=0;c[F+4>>2]=0;c[F+8>>2]=0;c[F+12>>2]=0;E=0;D=0;while(1){if(!g)break;n=c[f+(D<<2)>>2]|0;if(!n)o=E;else{if(E>>>0<4)c[F+(E<<2)>>2]=D;o=E+1|0;g=g-n|0}E=o;D=D+1|0}if(E>>>0<2){n=c[l>>2]|0;p=m+(n>>>3)|0;g=d[p>>0]|0;n=cc(1,0,n&7|0)|0;o=C;n=g|n;g=p;a[g>>0]=n;a[g+1>>0]=n>>8;a[g+2>>0]=n>>16;a[g+3>>0]=n>>24;p=p+4|0;a[p>>0]=o;a[p+1>>0]=o>>8;a[p+2>>0]=o>>16;a[p+3>>0]=o>>24;p=(c[l>>2]|0)+4|0;c[l>>2]=p;o=c[F>>2]|0;g=m+(p>>>3)|0;n=d[g>>0]|0;m=bc(o|0,0,h|0)|0;if(!((m|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(h>>>0>=57)pa(406267,406218,55,406251);D=cc(o|0,0,p&7|0)|0;F=C;D=n|D;m=g;E=m;a[E>>0]=D;a[E+1>>0]=D>>8;a[E+2>>0]=D>>16;a[E+3>>0]=D>>24;m=m+4|0;a[m>>0]=F;a[m+1>>0]=F>>8;a[m+2>>0]=F>>16;a[m+3>>0]=F>>24;c[l>>2]=(c[l>>2]|0)+h;a[j+o>>0]=0;b[k+(o<<1)>>1]=0;i=G;return}ac(j|0,0,D|0)|0;y=e+8|0;z=sa[c[e>>2]&1](c[y>>2]|0,D<<4|8)|0;A=z;if(!z)oa(1);B=1;while(1){g=D;n=A;a:while(1){do{if(!g)break a;g=g+-1|0;o=c[f+(g<<2)>>2]|0}while((o|0)==0);p=g&65535;if(o>>>0<B>>>0){c[n>>2]=B;b[n+4>>1]=-1;b[n+6>>1]=p}else{c[n>>2]=o;b[n+4>>1]=-1;b[n+6>>1]=p}n=n+8|0}x=n-A|0;w=x>>3;v=w+1|0;b:do if(w>>>0<13){s=1;while(1){if(s>>>0>=w>>>0)break b;r=z+(s<<3)|0;q=c[r>>2]|0;r=c[r+4>>2]|0;p=s;while(1){g=p;p=p+-1|0;o=z+(p<<3)|0;if(!(cb(q,c[o>>2]|0)|0))break;t=c[o+4>>2]|0;u=z+(g<<3)|0;c[u>>2]=c[o>>2];c[u+4>>2]=t;if(!p){g=0;break}}u=z+(g<<3)|0;c[u>>2]=q;c[u+4>>2]=r;s=s+1|0}}else{t=w>>>0<57?2:0;while(1){if((t|0)>=6)break b;g=c[19428+(t<<2)>>2]|0;u=g;while(1){if(u>>>0>=w>>>0)break;p=z+(u<<3)|0;o=c[p>>2]|0;p=c[p+4>>2]|0;s=u;while(1){if(s>>>0<g>>>0)break;q=s-g|0;r=z+(q<<3)|0;if(!(cb(o,c[r>>2]|0)|0))break;I=r;H=c[I+4>>2]|0;r=z+(s<<3)|0;c[r>>2]=c[I>>2];c[r+4>>2]=H;s=q}I=z+(s<<3)|0;c[I>>2]=o;c[I+4>>2]=p;u=u+1|0}t=t+1|0}}while(0);u=n;o=n;c[o>>2]=-1;c[o+4>>2]=-1;o=u+8|0;c[o>>2]=-1;c[o+4>>2]=-1;o=0;p=v;u=u+16|0;t=w;while(1){if((t|0)<=1)break;n=c[z+(o<<3)>>2]|0;g=c[z+(p<<3)>>2]|0;if(n>>>0>g>>>0){s=p+1|0;g=c[z+(s<<3)>>2]|0;r=o;q=p}else{r=o+1|0;n=c[z+(r<<3)>>2]|0;s=p;q=o}if(n>>>0>g>>>0){o=r;p=s+1|0;n=s}else{g=n;o=r+1|0;p=s;n=r}c[u+-8>>2]=(c[z+(q<<3)>>2]|0)+g;b[u+-4>>1]=q;b[u+-2>>1]=n;I=u;c[I>>2]=-1;c[I+4>>2]=-1;u=u+8|0;t=t+-1|0}if(Gb((x>>2)+-1|0,z,j,14)|0)break;B=B<<1}ra[c[e+4>>2]&1](c[y>>2]|0,z);Jb(j,D,k);x=c[l>>2]|0;v=m+(x>>>3)|0;w=d[v>>0]|0;x=x&7;if(E>>>0>=5){I=cc(1431655764,255,x|0)|0;n=C;I=w|I;g=v;t=g;a[t>>0]=I;a[t+1>>0]=I>>8;a[t+2>>0]=I>>16;a[t+3>>0]=I>>24;g=g+4|0;a[g>>0]=n;a[g+1>>0]=n>>8;a[g+2>>0]=n>>16;a[g+3>>0]=n>>24;g=(c[l>>2]|0)+40|0;c[l>>2]=g;n=0;t=8;c:while(1){while(1){if(n>>>0>=D>>>0){g=114;break c}u=a[j+n>>0]|0;s=1;o=n;while(1){o=o+1|0;if(o>>>0>=D>>>0)break;if((a[j+o>>0]|0)!=u<<24>>24)break;s=s+1|0}n=n+s|0;if(u<<24>>24)break;r=c[11564+(s<<2)>>2]|0;p=8+(s<<3)|0;o=c[p>>2]|0;p=c[p+4>>2]|0;q=m+(g>>>3)|0;I=bc(o|0,p|0,r|0)|0;if(!((I|0)==0&(C|0)==0)){g=99;break c}I=d[q>>0]|0;e=cc(o|0,p|0,g&7|0)|0;H=C;e=I|e;I=q;k=I;a[k>>0]=e;a[k+1>>0]=e>>8;a[k+2>>0]=e>>16;a[k+3>>0]=e>>24;I=I+4|0;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;I=(c[l>>2]|0)+r|0;c[l>>2]=I;g=I}if(t<<24>>24==u<<24>>24)o=s;else{p=u&255;o=a[404394+p>>0]|0;p=c[14380+(p<<2)>>2]|0;q=m+(g>>>3)|0;I=bc(p|0,0,o&255|0)|0;if(!((I|0)==0&(C|0)==0)){g=103;break}H=d[q>>0]|0;k=cc(p|0,0,g&7|0)|0;I=C;k=H|k;g=q;H=g;a[H>>0]=k;a[H+1>>0]=k>>8;a[H+2>>0]=k>>16;a[H+3>>0]=k>>24;g=g+4|0;a[g>>0]=I;a[g+1>>0]=I>>8;a[g+2>>0]=I>>16;a[g+3>>0]=I>>24;g=(c[l>>2]|0)+(o&255)|0;c[l>>2]=g;o=s+-1|0}if(o>>>0>=3){q=o+-3|0;o=c[14452+(q<<2)>>2]|0;q=5640+(q<<3)|0;p=c[q>>2]|0;q=c[q+4>>2]|0;r=m+(g>>>3)|0;I=bc(p|0,q|0,o|0)|0;if(!((I|0)==0&(C|0)==0)){g=112;break}I=d[r>>0]|0;H=cc(p|0,q|0,g&7|0)|0;t=C;H=I|H;g=r;I=g;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;g=g+4|0;a[g>>0]=t;a[g+1>>0]=t>>8;a[g+2>>0]=t>>16;a[g+3>>0]=t>>24;g=(c[l>>2]|0)+o|0;c[l>>2]=g;t=u;continue}t=u&255;s=404394+t|0;t=14380+(t<<2)|0;while(1){if(!o){t=u;continue c}p=a[s>>0]|0;q=c[t>>2]|0;r=m+(g>>>3)|0;I=bc(q|0,0,p&255|0)|0;if(!((I|0)==0&(C|0)==0)){g=109;break c}I=d[r>>0]|0;e=cc(q|0,0,g&7|0)|0;H=C;e=I|e;I=r;k=I;a[k>>0]=e;a[k+1>>0]=e>>8;a[k+2>>0]=e>>16;a[k+3>>0]=e>>24;I=I+4|0;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;I=(c[l>>2]|0)+(p&255)|0;c[l>>2]=I;g=I;o=o+-1|0}}if((g|0)==99)pa(406196,406218,54,406251);else if((g|0)==103)pa(406196,406218,54,406251);else if((g|0)==109)pa(406196,406218,54,406251);else if((g|0)==112)pa(406196,406218,54,406251);else if((g|0)==114){i=G;return}}I=cc(1,0,x|0)|0;n=C;I=w|I;g=v;o=g;a[o>>0]=I;a[o+1>>0]=I>>8;a[o+2>>0]=I>>16;a[o+3>>0]=I>>24;g=g+4|0;a[g>>0]=n;a[g+1>>0]=n>>8;a[g+2>>0]=n>>16;a[g+3>>0]=n>>24;g=(c[l>>2]|0)+2|0;c[l>>2]=g;n=E+-1|0;o=m+(g>>>3)|0;if(!((n&-4|0)==0&0==0))pa(406196,406218,54,406251);t=d[o>>0]|0;I=cc(n|0,0,g&7|0)|0;p=C;I=t|I;t=o;r=t;a[r>>0]=I;a[r+1>>0]=I>>8;a[r+2>>0]=I>>16;a[r+3>>0]=I>>24;t=t+4|0;a[t>>0]=p;a[t+1>>0]=p>>8;a[t+2>>0]=p>>16;a[t+3>>0]=p>>24;t=(c[l>>2]|0)+2|0;c[l>>2]=t;p=E+-1|0;r=0;while(1){if((r|0)==(E|0))break;q=F+(r<<2)|0;g=r;while(1){s=g+1|0;if((g|0)==(p|0))break;g=F+(s<<2)|0;n=c[g>>2]|0;o=c[q>>2]|0;if((d[j+n>>0]|0)>=(d[j+o>>0]|0)){g=s;continue}c[g>>2]=o;c[q>>2]=n;g=s}r=r+1|0}switch(E|0){case 2:{g=c[F>>2]|0;n=m+(t>>>3)|0;o=d[n>>0]|0;if(!((bc(g|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(h>>>0>=57)pa(406267,406218,55,406251);I=cc(g|0,0,t&7|0)|0;g=C;I=o|I;o=n;n=o;a[n>>0]=I;a[n+1>>0]=I>>8;a[n+2>>0]=I>>16;a[n+3>>0]=I>>24;o=o+4|0;a[o>>0]=g;a[o+1>>0]=g>>8;a[o+2>>0]=g>>16;a[o+3>>0]=g>>24;o=(c[l>>2]|0)+h|0;c[l>>2]=o;n=c[F+4>>2]|0;g=m+(o>>>3)|0;if(!((bc(n|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);I=d[g>>0]|0;F=cc(n|0,0,o&7|0)|0;H=C;F=I|F;I=g;m=I;a[m>>0]=F;a[m+1>>0]=F>>8;a[m+2>>0]=F>>16;a[m+3>>0]=F>>24;I=I+4|0;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;c[l>>2]=(c[l>>2]|0)+h;i=G;return}case 3:{g=c[F>>2]|0;n=m+(t>>>3)|0;o=d[n>>0]|0;if(!((bc(g|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(h>>>0>=57)pa(406267,406218,55,406251);g=cc(g|0,0,t&7|0)|0;I=C;o=o|g;g=n;n=g;a[n>>0]=o;a[n+1>>0]=o>>8;a[n+2>>0]=o>>16;a[n+3>>0]=o>>24;g=g+4|0;a[g>>0]=I;a[g+1>>0]=I>>8;a[g+2>>0]=I>>16;a[g+3>>0]=I>>24;g=(c[l>>2]|0)+h|0;c[l>>2]=g;n=c[F+4>>2]|0;o=m+(g>>>3)|0;if(!((bc(n|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);H=d[o>>0]|0;I=cc(n|0,0,g&7|0)|0;n=C;I=H|I;g=o;a[g>>0]=I;a[g+1>>0]=I>>8;a[g+2>>0]=I>>16;a[g+3>>0]=I>>24;o=o+4|0;a[o>>0]=n;a[o+1>>0]=n>>8;a[o+2>>0]=n>>16;a[o+3>>0]=n>>24;o=(c[l>>2]|0)+h|0;c[l>>2]=o;n=c[F+8>>2]|0;g=m+(o>>>3)|0;if(!((bc(n|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);I=d[g>>0]|0;F=cc(n|0,0,o&7|0)|0;H=C;F=I|F;I=g;m=I;a[m>>0]=F;a[m+1>>0]=F>>8;a[m+2>>0]=F>>16;a[m+3>>0]=F>>24;I=I+4|0;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;c[l>>2]=(c[l>>2]|0)+h;i=G;return}default:{p=c[F>>2]|0;g=m+(t>>>3)|0;n=d[g>>0]|0;if(!((bc(p|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(h>>>0>=57)pa(406267,406218,55,406251);I=cc(p|0,0,t&7|0)|0;o=C;I=n|I;n=g;a[n>>0]=I;a[n+1>>0]=I>>8;a[n+2>>0]=I>>16;a[n+3>>0]=I>>24;g=g+4|0;a[g>>0]=o;a[g+1>>0]=o>>8;a[g+2>>0]=o>>16;a[g+3>>0]=o>>24;g=(c[l>>2]|0)+h|0;c[l>>2]=g;n=c[F+4>>2]|0;o=m+(g>>>3)|0;if(!((bc(n|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);H=d[o>>0]|0;I=cc(n|0,0,g&7|0)|0;n=C;I=H|I;g=o;o=g;a[o>>0]=I;a[o+1>>0]=I>>8;a[o+2>>0]=I>>16;a[o+3>>0]=I>>24;g=g+4|0;a[g>>0]=n;a[g+1>>0]=n>>8;a[g+2>>0]=n>>16;a[g+3>>0]=n>>24;g=(c[l>>2]|0)+h|0;c[l>>2]=g;n=c[F+8>>2]|0;o=m+(g>>>3)|0;if(!((bc(n|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);H=d[o>>0]|0;I=cc(n|0,0,g&7|0)|0;g=C;I=H|I;n=o;a[n>>0]=I;a[n+1>>0]=I>>8;a[n+2>>0]=I>>16;a[n+3>>0]=I>>24;o=o+4|0;a[o>>0]=g;a[o+1>>0]=g>>8;a[o+2>>0]=g>>16;a[o+3>>0]=g>>24;o=(c[l>>2]|0)+h|0;c[l>>2]=o;g=c[F+12>>2]|0;n=m+(o>>>3)|0;if(!((bc(g|0,0,h|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);H=d[n>>0]|0;E=cc(g|0,0,o&7|0)|0;I=C;E=H|E;H=n;F=H;a[F>>0]=E;a[F+1>>0]=E>>8;a[F+2>>0]=E>>16;a[F+3>>0]=E>>24;H=H+4|0;a[H>>0]=I;a[H+1>>0]=I>>8;a[H+2>>0]=I>>16;a[H+3>>0]=I>>24;H=(c[l>>2]|0)+h|0;c[l>>2]=H;I=m+(H>>>3)|0;h=d[I>>0]|0;m=cc((a[j+p>>0]|0)==1|0,0,H&7|0)|0;H=C;m=h|m;h=I;a[h>>0]=m;a[h+1>>0]=m>>8;a[h+2>>0]=m>>16;a[h+3>>0]=m>>24;I=I+4|0;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;c[l>>2]=(c[l>>2]|0)+1;i=G;return}}}function ab(b,e,f,g,h,j,k,l,m,n,o,p,q,r,s,t){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;t=t|0;var u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0;T=i;i=i+2704|0;S=T+1800|0;Q=T+900|0;R=T;db(l,g,s,t);P=b+8|0;y=sa[c[b>>2]&1](c[P>>2]|0,11272)|0;if(!y)oa(1);M=c[r>>2]|0;N=c[r+8>>2]|0;g=c[r+12>>2]|0;O=c[r+4>>2]|0;c[S>>2]=256;c[S+4>>2]=M;c[S+8>>2]=N;c[S+12>>2]=g;c[S+16>>2]=O;c[S+20>>2]=1;c[S+24>>2]=0;c[S+880>>2]=0;if(!O)g=0;else g=c[g>>2]|0;c[S+884>>2]=g;c[S+888>>2]=0;N=S+892|0;c[N>>2]=0;O=S+896|0;c[O>>2]=0;K=c[r+24>>2]|0;L=c[r+32>>2]|0;g=c[r+36>>2]|0;M=c[r+28>>2]|0;c[Q>>2]=704;c[Q+4>>2]=K;c[Q+8>>2]=L;c[Q+12>>2]=g;c[Q+16>>2]=M;c[Q+20>>2]=1;c[Q+24>>2]=0;c[Q+880>>2]=0;if(!M)g=0;else g=c[g>>2]|0;c[Q+884>>2]=g;c[Q+888>>2]=0;L=Q+892|0;c[L>>2]=0;M=Q+896|0;c[M>>2]=0;I=c[r+48>>2]|0;J=c[r+56>>2]|0;g=c[r+60>>2]|0;K=c[r+52>>2]|0;c[R>>2]=m+16+(48<<n);c[R+4>>2]=I;c[R+8>>2]=J;c[R+12>>2]=g;c[R+16>>2]=K;c[R+20>>2]=1;c[R+24>>2]=0;c[R+880>>2]=0;if(!K)g=0;else g=c[g>>2]|0;c[R+884>>2]=g;c[R+888>>2]=0;J=R+892|0;c[J>>2]=0;K=R+896|0;c[K>>2]=0;eb(S,y,s,t);eb(Q,y,s,t);eb(R,y,s,t);g=c[s>>2]|0;u=t+(g>>>3)|0;v=d[u>>0]|0;if(!((n&-4|0)==0&0==0))pa(406196,406218,54,406251);w=cc(n|0,0,g&7|0)|0;g=C;v=v|w;w=u;u=w;a[u>>0]=v;a[u+1>>0]=v>>8;a[u+2>>0]=v>>16;a[u+3>>0]=v>>24;w=w+4|0;a[w>>0]=g;a[w+1>>0]=g>>8;a[w+2>>0]=g>>16;a[w+3>>0]=g>>24;w=(c[s>>2]|0)+2|0;c[s>>2]=w;g=m>>>n;u=t+(w>>>3)|0;v=d[u>>0]|0;if(!((g&-16|0)==0&0==0))pa(406196,406218,54,406251);I=cc(g|0,0,w&7|0)|0;g=C;I=v|I;n=u;a[n>>0]=I;a[n+1>>0]=I>>8;a[n+2>>0]=I>>16;a[n+3>>0]=I>>24;u=u+4|0;a[u>>0]=g;a[u+1>>0]=g>>8;a[u+2>>0]=g>>16;a[u+3>>0]=g>>24;u=(c[s>>2]|0)+4|0;c[s>>2]=u;g=(o&-4|0)==0&0==0;n=0;while(1){if(n>>>0>=(c[r>>2]|0)>>>0)break;v=t+(u>>>3)|0;w=d[v>>0]|0;if(!g){x=16;break}F=cc(o|0,0,u&7|0)|0;H=C;F=w|F;I=v;G=I;a[G>>0]=F;a[G+1>>0]=F>>8;a[G+2>>0]=F>>16;a[G+3>>0]=F>>24;I=I+4|0;a[I>>0]=H;a[I+1>>0]=H>>8;a[I+2>>0]=H>>16;a[I+3>>0]=H>>24;I=(c[s>>2]|0)+2|0;c[s>>2]=I;u=I;n=n+1|0}if((x|0)==16)pa(406196,406218,54,406251);I=r+76|0;g=c[I>>2]|0;v=r+92|0;u=c[v>>2]|0;if(!g)fb(u,6,y,s,t);else gb(b,c[r+72>>2]|0,g,u,y,s,t);H=r+84|0;g=c[H>>2]|0;m=r+108|0;u=c[m>>2]|0;if(!g)fb(u,2,y,s,t);else gb(b,c[r+80>>2]|0,g,u,y,s,t);w=c[r+88>>2]|0;v=c[v>>2]|0;n=c[S>>2]|0;g=_(v,n)|0;u=sa[c[b>>2]&1](c[P>>2]|0,g)|0;if(!u)oa(1);c[N>>2]=u;g=sa[c[b>>2]&1](c[P>>2]|0,g<<1)|0;if(!g)oa(1);c[O>>2]=g;g=w;u=0;while(1){if((u|0)==(v|0))break;G=_(u,n)|0;kb(g+(u*1040|0)|0,n,y,(c[N>>2]|0)+G|0,(c[O>>2]|0)+(G<<1)|0,s,t);u=u+1|0}v=c[r+96>>2]|0;w=c[r+100>>2]|0;n=c[Q>>2]|0;g=_(w,n)|0;u=sa[c[b>>2]&1](c[P>>2]|0,g)|0;if(!u)oa(1);c[L>>2]=u;g=sa[c[b>>2]&1](c[P>>2]|0,g<<1)|0;if(!g)oa(1);c[M>>2]=g;g=v;u=0;while(1){if((u|0)==(w|0))break;G=_(u,n)|0;kb(g+(u*2832|0)|0,n,y,(c[L>>2]|0)+G|0,(c[M>>2]|0)+(G<<1)|0,s,t);u=u+1|0}v=c[r+104>>2]|0;w=c[m>>2]|0;n=c[R>>2]|0;g=_(w,n)|0;u=sa[c[b>>2]&1](c[P>>2]|0,g)|0;if(!u)oa(1);c[J>>2]=u;g=sa[c[b>>2]&1](c[P>>2]|0,g<<1)|0;if(!g)oa(1);c[K>>2]=g;g=v;u=0;while(1){if((u|0)==(w|0))break;G=_(u,n)|0;kb(g+(u*2096|0)|0,n,y,(c[J>>2]|0)+G|0,(c[K>>2]|0)+(G<<1)|0,s,t);u=u+1|0}G=b+4|0;ra[c[G>>2]&1](c[P>>2]|0,y);F=r+80|0;E=r+72|0;u=j;g=k;D=0;a:while(1){if(D>>>0>=q>>>0){x=87;break}b=c[p+(D<<4)>>2]|0;n=c[p+(D<<4)+4>>2]|0;B=c[p+(D<<4)+8>>2]|0;k=c[p+(D<<4)+12>>2]|0;j=k&65535;A=k>>>16;z=k&65535;hb(Q,z,s,t);r=n&16777215;n=r^n>>>24;do if(b>>>0<6)w=b;else{if(b>>>0<130){y=b+-2|0;w=((aa(y|0)|0)^31)+-1|0;w=(w<<1)+(y>>>w)+2|0;break}if(b>>>0<2114){w=((aa(b+-66|0)|0)^31)+10|0;break}if(b>>>0<6210){w=21;break}w=b>>>0<22594?22:23}while(0);do if(n>>>0<10)v=n+65534|0;else{if(n>>>0<134){y=n+-6|0;v=((aa(y|0)|0)^31)+-1|0;v=(v<<1)+(y>>>v)+4|0;break}if(n>>>0>=2118){v=23;break}v=((aa(n+-70|0)|0)^31)+12|0}while(0);w=w&65535;x=c[17268+(w<<2)>>2]|0;w=b-(c[18788+(w<<2)>>2]|0)|0;m=v&65535;n=cc(n-(c[17364+(m<<2)>>2]|0)|0,0,x|0)|0;v=C;w=n|w;n=x+(c[17460+(m<<2)>>2]|0)|0;m=c[s>>2]|0;x=t+(m>>>3)|0;y=d[x>>0]|0;U=bc(w|0,v|0,n|0)|0;if(!((U|0)==0&(C|0)==0)){x=61;break}if(n>>>0>=57){x=63;break}w=cc(w|0,v|0,m&7|0)|0;m=C;w=y|w;U=x;y=U;a[y>>0]=w;a[y+1>>0]=w>>8;a[y+2>>0]=w>>16;a[y+3>>0]=w>>24;U=U+4|0;a[U>>0]=m;a[U+1>>0]=m>>8;a[U+2>>0]=m>>16;a[U+3>>0]=m>>24;c[s>>2]=(c[s>>2]|0)+n;b:do if(!(c[I>>2]|0)){v=f;w=b;while(1){if(!w)break b;hb(S,d[e+(v&h)>>0]|0,s,t);v=v+1|0;w=w+-1|0}}else{v=f;w=b;while(1){if(!w)break b;switch(o|0){case 0:{g=u&63;break}case 1:{g=(u&255)>>>2;break}case 2:{g=a[406969+(u&255)>>0]|a[406969+(g&255|256)>>0];break}case 3:{g=((d[407481+(u&255)>>0]|0)<<3)+(d[407481+(g&255)>>0]|0)&255;break}default:g=0}U=a[e+(v&h)>>0]|0;ib(S,U&255,g&255,c[E>>2]|0,s,t,6);g=u;u=U;v=v+1|0;w=w+-1|0}}while(0);y=f+b+r|0;do if(r){g=a[e+(y+-2&h)>>0]|0;u=a[e+(y+-1&h)>>0]|0;if((j&65535)<=127)break;m=B>>>24;x=B&16777215;if(!(c[H>>2]|0))hb(R,A,s,t);else{v=k&7;switch(z>>>6|0){case 7:case 4:case 2:case 0:{v=v>>>0<3?v:3;break}default:v=3}ib(R,A,v,c[F>>2]|0,s,t,2)}v=c[s>>2]|0;w=t+(v>>>3)|0;n=d[w>>0]|0;U=bc(x|0,0,m|0)|0;if(!((U|0)==0&(C|0)==0)){x=82;break a}if(B>>>0>=956301312){x=84;break a}z=cc(x|0,0,v&7|0)|0;B=C;z=n|z;U=w;A=U;a[A>>0]=z;a[A+1>>0]=z>>8;a[A+2>>0]=z>>16;a[A+3>>0]=z>>24;U=U+4|0;a[U>>0]=B;a[U+1>>0]=B>>8;a[U+2>>0]=B>>16;a[U+3>>0]=B>>24;c[s>>2]=(c[s>>2]|0)+m}while(0);f=y;D=D+1|0}if((x|0)==61)pa(406196,406218,54,406251);else if((x|0)==63)pa(406267,406218,55,406251);else if((x|0)==82)pa(406196,406218,54,406251);else if((x|0)==84)pa(406267,406218,55,406251);else if((x|0)==87){ra[c[G>>2]&1](c[P>>2]|0,c[J>>2]|0);c[J>>2]=0;ra[c[G>>2]&1](c[P>>2]|0,c[K>>2]|0);c[K>>2]=0;ra[c[G>>2]&1](c[P>>2]|0,c[L>>2]|0);c[L>>2]=0;ra[c[G>>2]&1](c[P>>2]|0,c[M>>2]|0);c[M>>2]=0;ra[c[G>>2]&1](c[P>>2]|0,c[N>>2]|0);c[N>>2]=0;ra[c[G>>2]&1](c[P>>2]|0,c[O>>2]|0);c[O>>2]=0;if(!l){i=T;return}U=(c[s>>2]|0)+7|0;c[s>>2]=U&-8;a[t+(U>>>3)>>0]=0;i=T;return}}function bb(b,e,f,g,h,j,k){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;t=i;i=i+32|0;r=t+8|0;q=t+16|0;l=t;s=f&g;f=k+((c[j>>2]|0)>>>3)|0;n=d[f>>0]|0;m=f;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;f=f+4|0;a[f>>0]=0;a[f+1>>0]=0;a[f+2>>0]=0;a[f+3>>0]=0;c[j>>2]=(c[j>>2]|0)+1;mb(h,r,q,l);f=c[l>>2]|0;l=c[l+4>>2]|0;m=c[j>>2]|0;n=k+(m>>>3)|0;o=d[n>>0]|0;if(!(l>>>0<0|(l|0)==0&f>>>0<4))pa(406196,406218,54,406251);f=cc(f|0,l|0,m&7|0)|0;l=C;f=o|f;p=n;o=p;a[o>>0]=f;a[o+1>>0]=f>>8;a[o+2>>0]=f>>16;a[o+3>>0]=f>>24;p=p+4|0;a[p>>0]=l;a[p+1>>0]=l>>8;a[p+2>>0]=l>>16;a[p+3>>0]=l>>24;p=(c[j>>2]|0)+2|0;c[j>>2]=p;o=c[q>>2]|0;l=r;f=c[l>>2]|0;l=c[l+4>>2]|0;m=k+(p>>>3)|0;n=d[m>>0]|0;r=bc(f|0,l|0,o|0)|0;if(!((r|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(o>>>0>=57)pa(406267,406218,55,406251);r=cc(f|0,l|0,p&7|0)|0;f=C;r=n|r;p=m;q=p;a[q>>0]=r;a[q+1>>0]=r>>8;a[q+2>>0]=r>>16;a[q+3>>0]=r>>24;p=p+4|0;a[p>>0]=f;a[p+1>>0]=f>>8;a[p+2>>0]=f>>16;a[p+3>>0]=f>>24;p=(c[j>>2]|0)+o|0;c[j>>2]=p;f=k+(p>>>3)|0;q=d[f>>0]|0;p=cc(1,0,p&7|0)|0;r=C;p=q|p;q=f;a[q>>0]=p;a[q+1>>0]=p>>8;a[q+2>>0]=p>>16;a[q+3>>0]=p>>24;f=f+4|0;a[f>>0]=r;a[f+1>>0]=r>>8;a[f+2>>0]=r>>16;a[f+3>>0]=r>>24;c[j>>2]=(c[j>>2]|0)+1;f=(c[j>>2]|0)+7|0;c[j>>2]=f&-8;a[k+(f>>>3)>>0]=0;f=g+1|0;if((s+h|0)>>>0>f>>>0){f=f-s|0;dc(k+((c[j>>2]|0)>>>3)|0,e+s|0,f|0)|0;l=(c[j>>2]|0)+(f<<3)|0;c[j>>2]=l;h=h-f|0;f=0}else{l=c[j>>2]|0;f=s}dc(k+(l>>>3)|0,e+f|0,h|0)|0;f=(c[j>>2]|0)+(h<<3)|0;c[j>>2]=f;if(f&7)pa(405180,406218,82,405195);a[k+(f>>>3)>>0]=0;if(!b){i=t;return}e=c[j>>2]|0;g=k+(e>>>3)|0;s=d[g>>0]|0;e=cc(1,0,e&7|0)|0;b=C;e=s|e;s=g;a[s>>0]=e;a[s+1>>0]=e>>8;a[s+2>>0]=e>>16;a[s+3>>0]=e>>24;g=g+4|0;a[g>>0]=b;a[g+1>>0]=b>>8;a[g+2>>0]=b>>16;a[g+3>>0]=b>>24;g=(c[j>>2]|0)+1|0;c[j>>2]=g;b=k+(g>>>3)|0;s=d[b>>0]|0;g=cc(1,0,g&7|0)|0;e=C;g=s|g;s=b;a[s>>0]=g;a[s+1>>0]=g>>8;a[s+2>>0]=g>>16;a[s+3>>0]=g>>24;b=b+4|0;a[b>>0]=e;a[b+1>>0]=e>>8;a[b+2>>0]=e>>16;a[b+3>>0]=e>>24;b=(c[j>>2]|0)+8|0;c[j>>2]=b&-8;a[k+(b>>>3)>>0]=0;i=t;return}function cb(a,b){a=a|0;b=b|0;return a>>>0<b>>>0|0}function db(b,e,f,g){b=b|0;e=e|0;f=f|0;g=g|0;var h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;p=i;i=i+32|0;o=p+8|0;n=p+16|0;j=p;h=g+((c[f>>2]|0)>>>3)|0;l=d[h>>0]|0;k=cc(b&1|0,0,c[f>>2]&7|0)|0;m=C;k=l|k;l=h;a[l>>0]=k;a[l+1>>0]=k>>8;a[l+2>>0]=k>>16;a[l+3>>0]=k>>24;h=h+4|0;a[h>>0]=m;a[h+1>>0]=m>>8;a[h+2>>0]=m>>16;a[h+3>>0]=m>>24;h=(c[f>>2]|0)+1|0;c[f>>2]=h;if(b){m=g+(h>>>3)|0;k=d[m>>0]|0;l=m;a[l>>0]=k;a[l+1>>0]=k>>8;a[l+2>>0]=k>>16;a[l+3>>0]=k>>24;m=m+4|0;a[m>>0]=0;a[m+1>>0]=0;a[m+2>>0]=0;a[m+3>>0]=0;c[f>>2]=(c[f>>2]|0)+1}mb(e,o,n,j);e=j;h=c[e>>2]|0;e=c[e+4>>2]|0;j=c[f>>2]|0;k=g+(j>>>3)|0;l=d[k>>0]|0;if(!(e>>>0<0|(e|0)==0&h>>>0<4))pa(406196,406218,54,406251);h=cc(h|0,e|0,j&7|0)|0;e=C;h=l|h;m=k;l=m;a[l>>0]=h;a[l+1>>0]=h>>8;a[l+2>>0]=h>>16;a[l+3>>0]=h>>24;m=m+4|0;a[m>>0]=e;a[m+1>>0]=e>>8;a[m+2>>0]=e>>16;a[m+3>>0]=e>>24;m=(c[f>>2]|0)+2|0;c[f>>2]=m;l=c[n>>2]|0;e=o;h=c[e>>2]|0;e=c[e+4>>2]|0;j=g+(m>>>3)|0;k=d[j>>0]|0;o=bc(h|0,e|0,l|0)|0;if(!((o|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(l>>>0>=57)pa(406267,406218,55,406251);m=cc(h|0,e|0,m&7|0)|0;o=C;m=k|m;h=j;n=h;a[n>>0]=m;a[n+1>>0]=m>>8;a[n+2>>0]=m>>16;a[n+3>>0]=m>>24;h=h+4|0;a[h>>0]=o;a[h+1>>0]=o>>8;a[h+2>>0]=o>>16;a[h+3>>0]=o>>24;h=(c[f>>2]|0)+l|0;c[f>>2]=h;if(b){i=p;return}g=g+(h>>>3)|0;o=d[g>>0]|0;b=g;a[b>>0]=o;a[b+1>>0]=o>>8;a[b+2>>0]=o>>16;a[b+3>>0]=o>>24;g=g+4|0;a[g>>0]=0;a[g+1>>0]=0;a[g+2>>0]=0;a[g+3>>0]=0;c[f>>2]=(c[f>>2]|0)+1;i=p;return}function eb(b,f,g,h){b=b|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;v=i;i=i+1136|0;u=v+104|0;t=v;j=c[b+8>>2]|0;m=c[b+12>>2]|0;r=c[b+16>>2]|0;s=c[b+4>>2]|0;ac(u|0,0,(s<<2)+8|0)|0;k=t;l=k+104|0;do{c[k>>2]=0;k=k+4|0}while((k|0)<(l|0));p=0;q=1;k=0;while(1){if((p|0)==(r|0))break;o=d[j+p>>0]|0;if((o|0)==(q+1|0))k=1;else k=(o|0)==(k|0)?0:o+2|0;if(p){n=u+(k<<2)|0;c[n>>2]=(c[n>>2]|0)+1}k=c[m+(p<<2)>>2]|0;if(k>>>0>176)n=k>>>0>752?20:14;else n=k>>>0>40?7:0;while(1){if(n>>>0>=25)break;l=n+1|0;if(k>>>0<(c[17556+(l<<3)>>2]|0)>>>0)break;else n=l}k=t+(n<<2)|0;c[k>>2]=(c[k>>2]|0)+1;k=q;p=p+1|0;q=o}nb(s+-1|0,g,h);if(s>>>0<=1){i=v;return}r=b+20|0;kb(u,s+2|0,f,b+28|0,b+286|0,g,h);kb(t,26,f,b+802|0,b+828|0,g,h);k=c[m>>2]|0;u=d[j>>0]|0;c[b+24>>2]=c[r>>2];c[r>>2]=u;if(k>>>0>176)l=k>>>0>752?20:14;else l=k>>>0>40?7:0;while(1){if(l>>>0>=25)break;j=l+1|0;if(k>>>0<(c[17556+(j<<3)>>2]|0)>>>0)break;else l=j}p=c[17556+(l<<3)+4>>2]|0;o=k-(c[17556+(l<<3)>>2]|0)|0;n=a[b+802+l>>0]|0;j=e[b+828+(l<<1)>>1]|0;k=c[g>>2]|0;l=h+(k>>>3)|0;m=d[l>>0]|0;b=bc(j|0,0,n&255|0)|0;if(!((b|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((n&255)>=57)pa(406267,406218,55,406251);b=cc(j|0,0,k&7|0)|0;j=C;b=m|b;k=l;a[k>>0]=b;a[k+1>>0]=b>>8;a[k+2>>0]=b>>16;a[k+3>>0]=b>>24;l=l+4|0;a[l>>0]=j;a[l+1>>0]=j>>8;a[l+2>>0]=j>>16;a[l+3>>0]=j>>24;l=(c[g>>2]|0)+(n&255)|0;c[g>>2]=l;j=h+(l>>>3)|0;k=d[j>>0]|0;h=bc(o|0,0,p|0)|0;if(!((h|0)==0&(C|0)==0))pa(406196,406218,54,406251);f=cc(o|0,0,l&7|0)|0;b=C;f=k|f;h=j;u=h;a[u>>0]=f;a[u+1>>0]=f>>8;a[u+2>>0]=f>>16;a[u+3>>0]=f>>24;h=h+4|0;a[h>>0]=b;a[h+1>>0]=b>>8;a[h+2>>0]=b>>16;a[h+3>>0]=b>>24;c[g>>2]=(c[g>>2]|0)+p;i=v;return}function fb(b,f,g,h,j){b=b|0;f=f|0;g=g|0;h=h|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;x=i;i=i+1904|0;p=x;w=x+1632|0;v=x+1088|0;nb(b+-1|0,h,j);if(b>>>0<=1){i=x;return}t=f+-1|0;u=(1<<t)+-1|0;o=t+b|0;ac(p|0,0,o<<2|0)|0;n=c[h>>2]|0;k=j+(n>>>3)|0;m=d[k>>0]|0;n=cc(1,0,n&7|0)|0;l=C;n=m|n;m=k;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;k=k+4|0;a[k>>0]=l;a[k+1>>0]=l>>8;a[k+2>>0]=l>>16;a[k+3>>0]=l>>24;k=(c[h>>2]|0)+1|0;c[h>>2]=k;l=f+-2|0;m=j+(k>>>3)|0;n=d[m>>0]|0;if(!((l&-16|0)==0&0==0))pa(406196,406218,54,406251);q=cc(l|0,0,k&7|0)|0;s=C;q=n|q;k=m;r=k;a[r>>0]=q;a[r+1>>0]=q>>8;a[r+2>>0]=q>>16;a[r+3>>0]=q>>24;k=k+4|0;a[k>>0]=s;a[k+1>>0]=s>>8;a[k+2>>0]=s>>16;a[k+3>>0]=s>>24;c[h>>2]=(c[h>>2]|0)+4;c[p+(t<<2)>>2]=b;c[p>>2]=1;k=f;while(1){if(k>>>0>=o>>>0)break;c[p+(k<<2)>>2]=1;k=k+1|0}kb(p,o,g,w,v,h,j);p=w+t|0;g=v+(t<<1)|0;q=bc(u|0,0,t|0)|0;q=(q|0)==0&(C|0)==0;r=t>>>0<57;s=0;while(1){if(s>>>0>=b>>>0){k=22;break}l=(s|0)==0?0:s+f+-1|0;k=a[w+l>>0]|0;l=e[v+(l<<1)>>1]|0;m=c[h>>2]|0;n=j+(m>>>3)|0;o=d[n>>0]|0;y=bc(l|0,0,k&255|0)|0;if(!((y|0)==0&(C|0)==0)){k=10;break}if((k&255)>=57){k=12;break}l=cc(l|0,0,m&7|0)|0;m=C;o=o|l;l=n;n=l;a[n>>0]=o;a[n+1>>0]=o>>8;a[n+2>>0]=o>>16;a[n+3>>0]=o>>24;l=l+4|0;a[l>>0]=m;a[l+1>>0]=m>>8;a[l+2>>0]=m>>16;a[l+3>>0]=m>>24;k=(c[h>>2]|0)+(k&255)|0;c[h>>2]=k;l=a[p>>0]|0;m=e[g>>1]|0;n=j+(k>>>3)|0;o=d[n>>0]|0;y=bc(m|0,0,l&255|0)|0;if(!((y|0)==0&(C|0)==0)){k=14;break}if((l&255)>=57){k=16;break}k=cc(m|0,0,k&7|0)|0;m=C;o=o|k;k=n;y=k;a[y>>0]=o;a[y+1>>0]=o>>8;a[y+2>>0]=o>>16;a[y+3>>0]=o>>24;k=k+4|0;a[k>>0]=m;a[k+1>>0]=m>>8;a[k+2>>0]=m>>16;a[k+3>>0]=m>>24;k=(c[h>>2]|0)+(l&255)|0;c[h>>2]=k;l=j+(k>>>3)|0;m=d[l>>0]|0;if(!q){k=18;break}if(!r){k=20;break}y=cc(u|0,0,k&7|0)|0;o=C;m=m|y;y=l;n=y;a[n>>0]=m;a[n+1>>0]=m>>8;a[n+2>>0]=m>>16;a[n+3>>0]=m>>24;y=y+4|0;a[y>>0]=o;a[y+1>>0]=o>>8;a[y+2>>0]=o>>16;a[y+3>>0]=o>>24;c[h>>2]=(c[h>>2]|0)+t;s=s+1|0}if((k|0)==10)pa(406196,406218,54,406251);else if((k|0)==12)pa(406267,406218,55,406251);else if((k|0)==14)pa(406196,406218,54,406251);else if((k|0)==16)pa(406267,406218,55,406251);else if((k|0)==18)pa(406196,406218,54,406251);else if((k|0)==20)pa(406267,406218,55,406251);else if((k|0)==22){w=c[h>>2]|0;y=j+(w>>>3)|0;b=d[y>>0]|0;w=cc(1,0,w&7|0)|0;j=C;w=b|w;b=y;a[b>>0]=w;a[b+1>>0]=w>>8;a[b+2>>0]=w>>16;a[b+3>>0]=w>>24;y=y+4|0;a[y>>0]=j;a[y+1>>0]=j>>8;a[y+2>>0]=j>>16;a[y+3>>0]=j>>24;c[h>>2]=(c[h>>2]|0)+1;i=x;return}}function gb(b,f,g,h,j,k,l){b=b|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;B=i;i=i+2160|0;r=B+1088|0;v=B;A=B+1888|0;z=B+1344|0;nb(h+-1|0,k,l);if((h|0)==1){i=B;return}x=b+8|0;y=sa[c[b>>2]&1](c[x>>2]|0,g<<2)|0;if(!y)oa(1);a:do if(!g)w=y;else{m=c[f>>2]|0;n=1;while(1){if((n|0)==(g|0))break;u=c[f+(n<<2)>>2]|0;m=u>>>0>m>>>0?u:m;n=n+1|0}if(m>>>0<256)o=0;else pa(405316,404333,616,405333);while(1){if(o>>>0>m>>>0)break;a[r+o>>0]=o;o=o+1|0}p=m+1|0;q=0;b:while(1){if(q>>>0>=g>>>0){w=y;break a}m=c[f+(q<<2)>>2]&255;o=0;while(1){if(o>>>0>=p>>>0)break b;if((a[r+o>>0]|0)==m<<24>>24)break;o=o+1|0}c[y+(q<<2)>>2]=o;n=a[r+o>>0]|0;m=o;while(1){if(!m)break;u=m+-1|0;a[r+m>>0]=a[r+u>>0]|0;m=u}a[r>>0]=n;q=q+1|0}pa(405354,404333,624,405333)}while(0);c[r>>2]=0;m=0;o=0;while(1){if(m>>>0>=g>>>0)break;while(1){if(m>>>0>=g>>>0){n=0;break}if(!(c[w+(m<<2)>>2]|0)){n=0;break}u=m+1|0;c[r>>2]=u;m=u}while(1){if(m>>>0>=g>>>0)break;if(c[w+(m<<2)>>2]|0)break;u=m+1|0;c[r>>2]=u;m=u;n=n+1|0}o=n>>>0>o>>>0?n:o}if(!o)u=0;else{u=(aa(o|0)|0)^31;u=u>>>0<6?u:6}c[r>>2]=0;p=2<<u;q=u+((512<<u)+-512)|0;f=p+-1|0;o=0;s=0;while(1){if(o>>>0>=g>>>0)break;if(s>>>0>o>>>0){t=38;break}m=c[w+(o<<2)>>2]|0;c:do if(!m){m=o;n=1;while(1){m=m+1|0;if(m>>>0>=g>>>0)break;if(c[w+(m<<2)>>2]|0)break;n=n+1|0}c[r>>2]=o+n;m=s;while(1){if(!n)break c;if(n>>>0<p>>>0)break;c[w+(m<<2)>>2]=q;m=m+1|0;n=n-f|0}s=(aa(n|0)|0)^31;c[w+(m<<2)>>2]=s|n-(1<<s)<<9;m=m+1|0}else{c[w+(s<<2)>>2]=m+u;c[r>>2]=(c[r>>2]|0)+1;m=s+1|0}while(0);o=c[r>>2]|0;s=m}if((t|0)==38)pa(405282,404333,656,405297);ac(v|0,0,1088)|0;m=0;while(1){if((m|0)==(s|0))break;t=v+((c[w+(m<<2)>>2]&511)<<2)|0;c[t>>2]=(c[t>>2]|0)+1;m=m+1|0}t=(u|0)!=0;f=c[k>>2]|0;m=l+(f>>>3)|0;g=d[m>>0]|0;f=cc(t&1|0,0,f&7|0)|0;r=C;f=g|f;g=m;a[g>>0]=f;a[g+1>>0]=f>>8;a[g+2>>0]=f>>16;a[g+3>>0]=f>>24;m=m+4|0;a[m>>0]=r;a[m+1>>0]=r>>8;a[m+2>>0]=r>>16;a[m+3>>0]=r>>24;m=(c[k>>2]|0)+1|0;c[k>>2]=m;do if(t){n=u+-1|0;o=l+(m>>>3)|0;p=d[o>>0]|0;if((n&-16|0)==0&0==0){f=cc(n|0,0,m&7|0)|0;r=C;f=p|f;t=o;g=t;a[g>>0]=f;a[g+1>>0]=f>>8;a[g+2>>0]=f>>16;a[g+3>>0]=f>>24;t=t+4|0;a[t>>0]=r;a[t+1>>0]=r>>8;a[t+2>>0]=r>>16;a[t+3>>0]=r>>24;c[k>>2]=(c[k>>2]|0)+4;break}else pa(406196,406218,54,406251)}while(0);kb(v,u+h|0,j,A,z,k,l);r=0;while(1){if(r>>>0>=s>>>0){t=70;break}g=c[w+(r<<2)>>2]|0;f=g&511;g=g>>>9;m=a[A+f>>0]|0;n=e[z+(f<<1)>>1]|0;o=c[k>>2]|0;p=l+(o>>>3)|0;q=d[p>>0]|0;j=bc(n|0,0,m&255|0)|0;if(!((j|0)==0&(C|0)==0)){t=60;break}if((m&255)>=57){t=62;break}t=cc(n|0,0,o&7|0)|0;h=C;t=q|t;j=p;v=j;a[v>>0]=t;a[v+1>>0]=t>>8;a[v+2>>0]=t>>16;a[v+3>>0]=t>>24;j=j+4|0;a[j>>0]=h;a[j+1>>0]=h>>8;a[j+2>>0]=h>>16;a[j+3>>0]=h>>24;m=(c[k>>2]|0)+(m&255)|0;c[k>>2]=m;if(!((f|0)==0|f>>>0>u>>>0)){n=l+(m>>>3)|0;o=d[n>>0]|0;j=bc(g|0,0,f|0)|0;if(!((j|0)==0&(C|0)==0)){t=65;break}if(f>>>0>=57){t=67;break}t=cc(g|0,0,m&7|0)|0;h=C;t=o|t;j=n;v=j;a[v>>0]=t;a[v+1>>0]=t>>8;a[v+2>>0]=t>>16;a[v+3>>0]=t>>24;j=j+4|0;a[j>>0]=h;a[j+1>>0]=h>>8;a[j+2>>0]=h>>16;a[j+3>>0]=h>>24;c[k>>2]=(c[k>>2]|0)+f}r=r+1|0}if((t|0)==60)pa(406196,406218,54,406251);else if((t|0)==62)pa(406267,406218,55,406251);else if((t|0)==65)pa(406196,406218,54,406251);else if((t|0)==67)pa(406267,406218,55,406251);else if((t|0)==70){w=c[k>>2]|0;l=l+(w>>>3)|0;z=d[l>>0]|0;w=cc(1,0,w&7|0)|0;A=C;w=z|w;z=l;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;l=l+4|0;a[l>>0]=A;a[l+1>>0]=A>>8;a[l+2>>0]=A>>16;a[l+3>>0]=A>>24;c[k>>2]=(c[k>>2]|0)+1;ra[c[b+4>>2]&1](c[x>>2]|0,y);i=B;return}}function hb(b,f,g,h){b=b|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;s=b+884|0;i=c[s>>2]|0;do if(!i){p=b+880|0;j=(c[p>>2]|0)+1|0;c[p>>2]=j;p=c[(c[b+12>>2]|0)+(j<<2)>>2]|0;j=a[(c[b+8>>2]|0)+j>>0]|0;c[s>>2]=p;j=j&255;q=b+888|0;c[q>>2]=_(j,c[b>>2]|0)|0;k=b+20|0;l=c[k>>2]|0;i=b+24|0;if((j|0)==(l+1|0))m=1;else m=(j|0)==(c[i>>2]|0)?0:j+2|0;c[i>>2]=l;c[k>>2]=j;n=a[b+28+m>>0]|0;i=e[b+286+(m<<1)>>1]|0;j=c[g>>2]|0;k=h+(j>>>3)|0;l=d[k>>0]|0;o=bc(i|0,0,n&255|0)|0;if(!((o|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((n&255)>=57)pa(406267,406218,55,406251);j=cc(i|0,0,j&7|0)|0;m=C;j=l|j;o=k;l=o;a[l>>0]=j;a[l+1>>0]=j>>8;a[l+2>>0]=j>>16;a[l+3>>0]=j>>24;o=o+4|0;a[o>>0]=m;a[o+1>>0]=m>>8;a[o+2>>0]=m>>16;a[o+3>>0]=m>>24;c[g>>2]=(c[g>>2]|0)+(n&255);if(p>>>0>176)j=p>>>0>752?20:14;else j=p>>>0>40?7:0;while(1){if(j>>>0>=25)break;i=j+1|0;if(p>>>0<(c[17556+(i<<3)>>2]|0)>>>0)break;else j=i}o=c[17556+(j<<3)+4>>2]|0;n=p-(c[17556+(j<<3)>>2]|0)|0;m=a[b+802+j>>0]|0;i=e[b+828+(j<<1)>>1]|0;j=c[g>>2]|0;k=h+(j>>>3)|0;l=d[k>>0]|0;p=bc(i|0,0,m&255|0)|0;if(!((p|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((m&255)>=57)pa(406267,406218,55,406251);p=cc(i|0,0,j&7|0)|0;i=C;p=l|p;j=k;k=j;a[k>>0]=p;a[k+1>>0]=p>>8;a[k+2>>0]=p>>16;a[k+3>>0]=p>>24;j=j+4|0;a[j>>0]=i;a[j+1>>0]=i>>8;a[j+2>>0]=i>>16;a[j+3>>0]=i>>24;j=(c[g>>2]|0)+(m&255)|0;c[g>>2]=j;k=h+(j>>>3)|0;i=d[k>>0]|0;p=bc(n|0,0,o|0)|0;if((p|0)==0&(C|0)==0){n=cc(n|0,0,j&7|0)|0;t=C;n=i|n;r=k;p=r;a[p>>0]=n;a[p+1>>0]=n>>8;a[p+2>>0]=n>>16;a[p+3>>0]=n>>24;r=r+4|0;a[r>>0]=t;a[r+1>>0]=t>>8;a[r+2>>0]=t>>16;a[r+3>>0]=t>>24;c[g>>2]=(c[g>>2]|0)+o;r=q;t=c[s>>2]|0;break}else pa(406196,406218,54,406251)}else{r=b+888|0;t=i}while(0);c[s>>2]=t+-1;k=(c[r>>2]|0)+f|0;m=a[(c[b+892>>2]|0)+k>>0]|0;k=e[(c[b+896>>2]|0)+(k<<1)>>1]|0;l=c[g>>2]|0;i=h+(l>>>3)|0;j=d[i>>0]|0;h=bc(k|0,0,m&255|0)|0;if(!((h|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((m&255)<57){t=cc(k|0,0,l&7|0)|0;b=C;t=j|t;h=i;f=h;a[f>>0]=t;a[f+1>>0]=t>>8;a[f+2>>0]=t>>16;a[f+3>>0]=t>>24;h=h+4|0;a[h>>0]=b;a[h+1>>0]=b>>8;a[h+2>>0]=b>>16;a[h+3>>0]=b>>24;c[g>>2]=(c[g>>2]|0)+(m&255);return}else pa(406267,406218,55,406251)}function ib(b,f,g,h,i,j,k){b=b|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;u=b+884|0;l=c[u>>2]|0;do if(!l){r=b+880|0;n=(c[r>>2]|0)+1|0;c[r>>2]=n;r=c[(c[b+12>>2]|0)+(n<<2)>>2]|0;n=a[(c[b+8>>2]|0)+n>>0]|0;c[u>>2]=r;n=n&255;s=b+888|0;c[s>>2]=n<<k;k=b+20|0;m=c[k>>2]|0;l=b+24|0;if((n|0)==(m+1|0))o=1;else o=(n|0)==(c[l>>2]|0)?0:n+2|0;c[l>>2]=m;c[k>>2]=n;p=a[b+28+o>>0]|0;l=e[b+286+(o<<1)>>1]|0;k=c[i>>2]|0;m=j+(k>>>3)|0;n=d[m>>0]|0;q=bc(l|0,0,p&255|0)|0;if(!((q|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((p&255)>=57)pa(406267,406218,55,406251);k=cc(l|0,0,k&7|0)|0;o=C;k=n|k;q=m;n=q;a[n>>0]=k;a[n+1>>0]=k>>8;a[n+2>>0]=k>>16;a[n+3>>0]=k>>24;q=q+4|0;a[q>>0]=o;a[q+1>>0]=o>>8;a[q+2>>0]=o>>16;a[q+3>>0]=o>>24;c[i>>2]=(c[i>>2]|0)+(p&255);if(r>>>0>176)k=r>>>0>752?20:14;else k=r>>>0>40?7:0;while(1){if(k>>>0>=25)break;l=k+1|0;if(r>>>0<(c[17556+(l<<3)>>2]|0)>>>0)break;else k=l}q=c[17556+(k<<3)+4>>2]|0;p=r-(c[17556+(k<<3)>>2]|0)|0;o=a[b+802+k>>0]|0;l=e[b+828+(k<<1)>>1]|0;k=c[i>>2]|0;m=j+(k>>>3)|0;n=d[m>>0]|0;r=bc(l|0,0,o&255|0)|0;if(!((r|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((o&255)>=57)pa(406267,406218,55,406251);r=cc(l|0,0,k&7|0)|0;k=C;r=n|r;l=m;m=l;a[m>>0]=r;a[m+1>>0]=r>>8;a[m+2>>0]=r>>16;a[m+3>>0]=r>>24;l=l+4|0;a[l>>0]=k;a[l+1>>0]=k>>8;a[l+2>>0]=k>>16;a[l+3>>0]=k>>24;l=(c[i>>2]|0)+(o&255)|0;c[i>>2]=l;k=j+(l>>>3)|0;m=d[k>>0]|0;r=bc(p|0,0,q|0)|0;if((r|0)==0&(C|0)==0){p=cc(p|0,0,l&7|0)|0;v=C;p=m|p;t=k;r=t;a[r>>0]=p;a[r+1>>0]=p>>8;a[r+2>>0]=p>>16;a[r+3>>0]=p>>24;t=t+4|0;a[t>>0]=v;a[t+1>>0]=v>>8;a[t+2>>0]=v>>16;a[t+3>>0]=v>>24;c[i>>2]=(c[i>>2]|0)+q;t=s;v=c[u>>2]|0;break}else pa(406196,406218,54,406251)}else{t=b+888|0;v=l}while(0);c[u>>2]=v+-1;m=(_(c[h+((c[t>>2]|0)+g<<2)>>2]|0,c[b>>2]|0)|0)+f|0;o=a[(c[b+892>>2]|0)+m>>0]|0;m=e[(c[b+896>>2]|0)+(m<<1)>>1]|0;n=c[i>>2]|0;l=j+(n>>>3)|0;k=d[l>>0]|0;j=bc(m|0,0,o&255|0)|0;if(!((j|0)==0&(C|0)==0))pa(406196,406218,54,406251);if((o&255)<57){h=cc(m|0,0,n&7|0)|0;b=C;h=k|h;j=l;f=j;a[f>>0]=h;a[f+1>>0]=h>>8;a[f+2>>0]=h>>16;a[f+3>>0]=h>>24;j=j+4|0;a[j>>0]=b;a[j+1>>0]=b>>8;a[j+2>>0]=b>>16;a[j+3>>0]=b>>24;c[i>>2]=(c[i>>2]|0)+(o&255);return}else pa(406267,406218,55,406251)}function jb(a,b,e,f,g,h,i,j){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;r=i+2816|0;s=h+1024|0;t=j+2080|0;q=0;while(1){if((q|0)==(g|0))break;o=c[f+(q<<4)>>2]|0;k=c[f+(q<<4)+4>>2]|0;l=c[f+(q<<4)+12>>2]|0;p=l&65535;n=l>>>16;l=i+((l&65535)<<2)|0;c[l>>2]=(c[l>>2]|0)+1;c[r>>2]=(c[r>>2]|0)+1;l=b;m=o;while(1){if(!m)break;u=h+((d[a+(l&e)>>0]|0)<<2)|0;c[u>>2]=(c[u>>2]|0)+1;c[s>>2]=(c[s>>2]|0)+1;l=l+1|0;m=m+-1|0}k=k&16777215;if((k|0)!=0&(p&65535)>127){u=j+(n<<2)|0;c[u>>2]=(c[u>>2]|0)+1;c[t>>2]=(c[t>>2]|0)+1}b=b+o+k|0;q=q+1|0}return}function kb(e,f,g,h,j,k,l){e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0;s=i;i=i+16|0;q=s;c[q>>2]=0;c[q+4>>2]=0;c[q+8>>2]=0;c[q+12>>2]=0;p=0;n=0;while(1){if(n>>>0>=f>>>0)break;if(!(c[e+(n<<2)>>2]|0))m=p;else{if(p>>>0>=4){if(p>>>0>4)break}else c[q+(p<<2)>>2]=n;m=p+1|0}p=m;n=n+1|0}r=0;m=f+-1|0;while(1){if(!m)break;r=r+1|0;m=m>>>1}if(p>>>0<2){n=c[k>>2]|0;e=l+(n>>>3)|0;m=d[e>>0]|0;n=cc(1,0,n&7|0)|0;f=C;n=m|n;m=e;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;e=e+4|0;a[e>>0]=f;a[e+1>>0]=f>>8;a[e+2>>0]=f>>16;a[e+3>>0]=f>>24;e=(c[k>>2]|0)+4|0;c[k>>2]=e;f=c[q>>2]|0;m=l+(e>>>3)|0;n=d[m>>0]|0;l=bc(f|0,0,r|0)|0;if(!((l|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(r>>>0>=57)pa(406267,406218,55,406251);g=cc(f|0,0,e&7|0)|0;p=C;g=n|g;l=m;o=l;a[o>>0]=g;a[o+1>>0]=g>>8;a[o+2>>0]=g>>16;a[o+3>>0]=g>>24;l=l+4|0;a[l>>0]=p;a[l+1>>0]=p>>8;a[l+2>>0]=p>>16;a[l+3>>0]=p>>24;c[k>>2]=(c[k>>2]|0)+r;a[h+(c[q>>2]|0)>>0]=0;b[j+(c[q>>2]<<1)>>1]=0;i=s;return}ac(h|0,0,f|0)|0;Hb(e,f,15,g,h);Jb(h,f,j);if(p>>>0>=5){_a(h,f,g,k,l);i=s;return}m=l+((c[k>>2]|0)>>>3)|0;e=d[m>>0]|0;f=cc(1,0,c[k>>2]&7|0)|0;n=C;f=e|f;e=m;a[e>>0]=f;a[e+1>>0]=f>>8;a[e+2>>0]=f>>16;a[e+3>>0]=f>>24;m=m+4|0;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;m=(c[k>>2]|0)+2|0;c[k>>2]=m;n=p+-1|0;e=l+(m>>>3)|0;f=d[e>>0]|0;if(!((n&-4|0)==0&0==0))pa(406196,406218,54,406251);g=cc(n|0,0,m&7|0)|0;j=C;g=f|g;f=e;o=f;a[o>>0]=g;a[o+1>>0]=g>>8;a[o+2>>0]=g>>16;a[o+3>>0]=g>>24;f=f+4|0;a[f>>0]=j;a[f+1>>0]=j>>8;a[f+2>>0]=j>>16;a[f+3>>0]=j>>24;c[k>>2]=(c[k>>2]|0)+2;f=p+-1|0;j=0;while(1){if((j|0)==(p|0))break;g=q+(j<<2)|0;m=j;while(1){o=m+1|0;if((m|0)==(f|0))break;m=q+(o<<2)|0;n=c[m>>2]|0;e=c[g>>2]|0;if((d[h+n>>0]|0)>=(d[h+e>>0]|0)){m=o;continue}c[m>>2]=e;c[g>>2]=n;m=o}j=j+1|0}switch(p|0){case 2:{m=c[q>>2]|0;n=c[k>>2]|0;e=l+(n>>>3)|0;f=d[e>>0]|0;if(!((bc(m|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(r>>>0>=57)pa(406267,406218,55,406251);n=cc(m|0,0,n&7|0)|0;m=C;n=f|n;f=e;e=f;a[e>>0]=n;a[e+1>>0]=n>>8;a[e+2>>0]=n>>16;a[e+3>>0]=n>>24;f=f+4|0;a[f>>0]=m;a[f+1>>0]=m>>8;a[f+2>>0]=m>>16;a[f+3>>0]=m>>24;f=(c[k>>2]|0)+r|0;c[k>>2]=f;e=c[q+4>>2]|0;m=l+(f>>>3)|0;n=d[m>>0]|0;if(!((bc(e|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);p=cc(e|0,0,f&7|0)|0;q=C;p=n|p;l=m;h=l;a[h>>0]=p;a[h+1>>0]=p>>8;a[h+2>>0]=p>>16;a[h+3>>0]=p>>24;l=l+4|0;a[l>>0]=q;a[l+1>>0]=q>>8;a[l+2>>0]=q>>16;a[l+3>>0]=q>>24;c[k>>2]=(c[k>>2]|0)+r;i=s;return}case 3:{m=c[q>>2]|0;n=c[k>>2]|0;e=l+(n>>>3)|0;f=d[e>>0]|0;if(!((bc(m|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(r>>>0>=57)pa(406267,406218,55,406251);m=cc(m|0,0,n&7|0)|0;n=C;f=f|m;m=e;e=m;a[e>>0]=f;a[e+1>>0]=f>>8;a[e+2>>0]=f>>16;a[e+3>>0]=f>>24;m=m+4|0;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;m=(c[k>>2]|0)+r|0;c[k>>2]=m;n=c[q+4>>2]|0;e=l+(m>>>3)|0;f=d[e>>0]|0;if(!((bc(n|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);n=cc(n|0,0,m&7|0)|0;m=C;n=f|n;f=e;e=f;a[e>>0]=n;a[e+1>>0]=n>>8;a[e+2>>0]=n>>16;a[e+3>>0]=n>>24;f=f+4|0;a[f>>0]=m;a[f+1>>0]=m>>8;a[f+2>>0]=m>>16;a[f+3>>0]=m>>24;f=(c[k>>2]|0)+r|0;c[k>>2]=f;e=c[q+8>>2]|0;m=l+(f>>>3)|0;n=d[m>>0]|0;if(!((bc(e|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);p=cc(e|0,0,f&7|0)|0;q=C;p=n|p;l=m;h=l;a[h>>0]=p;a[h+1>>0]=p>>8;a[h+2>>0]=p>>16;a[h+3>>0]=p>>24;l=l+4|0;a[l>>0]=q;a[l+1>>0]=q>>8;a[l+2>>0]=q>>16;a[l+3>>0]=q>>24;c[k>>2]=(c[k>>2]|0)+r;i=s;return}default:{m=c[q>>2]|0;n=c[k>>2]|0;e=l+(n>>>3)|0;f=d[e>>0]|0;if(!((bc(m|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(r>>>0>=57)pa(406267,406218,55,406251);m=cc(m|0,0,n&7|0)|0;n=C;f=f|m;m=e;e=m;a[e>>0]=f;a[e+1>>0]=f>>8;a[e+2>>0]=f>>16;a[e+3>>0]=f>>24;m=m+4|0;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;m=(c[k>>2]|0)+r|0;c[k>>2]=m;n=c[q+4>>2]|0;e=l+(m>>>3)|0;f=d[e>>0]|0;if(!((bc(n|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);m=cc(n|0,0,m&7|0)|0;n=C;f=f|m;m=e;e=m;a[e>>0]=f;a[e+1>>0]=f>>8;a[e+2>>0]=f>>16;a[e+3>>0]=f>>24;m=m+4|0;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;m=(c[k>>2]|0)+r|0;c[k>>2]=m;n=c[q+8>>2]|0;e=l+(m>>>3)|0;f=d[e>>0]|0;if(!((bc(n|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);m=cc(n|0,0,m&7|0)|0;n=C;f=f|m;m=e;e=m;a[e>>0]=f;a[e+1>>0]=f>>8;a[e+2>>0]=f>>16;a[e+3>>0]=f>>24;m=m+4|0;a[m>>0]=n;a[m+1>>0]=n>>8;a[m+2>>0]=n>>16;a[m+3>>0]=n>>24;m=(c[k>>2]|0)+r|0;c[k>>2]=m;n=c[q+12>>2]|0;e=l+(m>>>3)|0;f=d[e>>0]|0;if(!((bc(n|0,0,r|0)|0)==0&(C|0)==0))pa(406196,406218,54,406251);g=cc(n|0,0,m&7|0)|0;p=C;g=f|g;o=e;j=o;a[j>>0]=g;a[j+1>>0]=g>>8;a[j+2>>0]=g>>16;a[j+3>>0]=g>>24;o=o+4|0;a[o>>0]=p;a[o+1>>0]=p>>8;a[o+2>>0]=p>>16;a[o+3>>0]=p>>24;o=(c[k>>2]|0)+r|0;c[k>>2]=o;r=l+(o>>>3)|0;p=d[r>>0]|0;h=cc((a[h+(c[q>>2]|0)>>0]|0)==1|0,0,o&7|0)|0;l=C;h=p|h;q=r;a[q>>0]=h;a[q+1>>0]=h>>8;a[q+2>>0]=h>>16;a[q+3>>0]=h>>24;r=r+4|0;a[r>>0]=l;a[r+1>>0]=l>>8;a[r+2>>0]=l>>16;a[r+3>>0]=l>>24;c[k>>2]=(c[k>>2]|0)+1;i=s;return}}}function lb(b,f,g,h,i,j,k,l,m,n,o,p,q){b=b|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;var r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0;E=0;a:while(1){if(E>>>0>=i>>>0){f=42;break}y=c[h+(E<<4)>>2]|0;w=c[h+(E<<4)+4>>2]|0;D=c[h+(E<<4)+8>>2]|0;s=c[h+(E<<4)+12>>2]|0;A=s&65535;B=s>>>16;s=s&65535;r=a[l+s>>0]|0;s=e[m+(s<<1)>>1]|0;t=c[p>>2]|0;u=q+(t>>>3)|0;v=d[u>>0]|0;z=bc(s|0,0,r&255|0)|0;if(!((z|0)==0&(C|0)==0)){f=4;break}if((r&255)>=57){f=6;break}x=cc(s|0,0,t&7|0)|0;z=C;v=v|x;x=u;t=x;a[t>>0]=v;a[t+1>>0]=v>>8;a[t+2>>0]=v>>16;a[t+3>>0]=v>>24;x=x+4|0;a[x>>0]=z;a[x+1>>0]=z>>8;a[x+2>>0]=z>>16;a[x+3>>0]=z>>24;x=(c[p>>2]|0)+(r&255)|0;c[p>>2]=x;z=w&16777215;t=z^w>>>24;do if(y>>>0>=6){if(y>>>0<130){w=y+-2|0;s=((aa(w|0)|0)^31)+-1|0;s=(s<<1)+(w>>>s)+2|0;break}if(y>>>0<2114){s=((aa(y+-66|0)|0)^31)+10|0;break}if(y>>>0<6210)s=21;else s=y>>>0<22594?22:23}else s=y;while(0);do if(t>>>0>=10){if(t>>>0<134){w=t+-6|0;r=((aa(w|0)|0)^31)+-1|0;r=(r<<1)+(w>>>r)+4|0;break}if(t>>>0<2118)r=((aa(t+-70|0)|0)^31)+12|0;else r=23}else r=t+65534|0;while(0);s=s&65535;v=c[17268+(s<<2)>>2]|0;s=y-(c[18788+(s<<2)>>2]|0)|0;u=r&65535;t=cc(t-(c[17364+(u<<2)>>2]|0)|0,0,v|0)|0;r=C;s=t|s;t=v+(c[17460+(u<<2)>>2]|0)|0;u=q+(x>>>3)|0;v=d[u>>0]|0;w=bc(s|0,r|0,t|0)|0;if(!((w|0)==0&(C|0)==0)){f=21;break}if(t>>>0>=57){f=23;break}s=cc(s|0,r|0,x&7|0)|0;w=C;s=v|s;x=u;v=x;a[v>>0]=s;a[v+1>>0]=s>>8;a[v+2>>0]=s>>16;a[v+3>>0]=s>>24;x=x+4|0;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;x=(c[p>>2]|0)+t|0;c[p>>2]=x;v=y;while(1){if(!v)break;s=d[b+(f&g)>>0]|0;r=a[j+s>>0]|0;s=e[k+(s<<1)>>1]|0;t=q+(x>>>3)|0;u=d[t>>0]|0;y=bc(s|0,0,r&255|0)|0;if(!((y|0)==0&(C|0)==0)){f=27;break a}if((r&255)>=57){f=29;break a}s=cc(s|0,0,x&7|0)|0;w=C;s=u|s;y=t;u=y;a[u>>0]=s;a[u+1>>0]=s>>8;a[u+2>>0]=s>>16;a[u+3>>0]=s>>24;y=y+4|0;a[y>>0]=w;a[y+1>>0]=w>>8;a[y+2>>0]=w>>16;a[y+3>>0]=w>>24;y=(c[p>>2]|0)+(r&255)|0;c[p>>2]=y;x=y;f=f+1|0;v=v+-1|0}f=f+z|0;if((z|0)!=0&(A&65535)>127){v=D>>>24;w=D&16777215;u=a[n+B>>0]|0;r=e[o+(B<<1)>>1]|0;s=q+(x>>>3)|0;t=d[s>>0]|0;B=bc(r|0,0,u&255|0)|0;if(!((B|0)==0&(C|0)==0)){f=33;break}if((u&255)>=57){f=35;break}r=cc(r|0,0,x&7|0)|0;B=C;t=t|r;r=s;s=r;a[s>>0]=t;a[s+1>>0]=t>>8;a[s+2>>0]=t>>16;a[s+3>>0]=t>>24;r=r+4|0;a[r>>0]=B;a[r+1>>0]=B>>8;a[r+2>>0]=B>>16;a[r+3>>0]=B>>24;r=(c[p>>2]|0)+(u&255)|0;c[p>>2]=r;s=q+(r>>>3)|0;t=d[s>>0]|0;B=bc(w|0,0,v|0)|0;if(!((B|0)==0&(C|0)==0)){f=37;break}if(D>>>0>=956301312){f=39;break}z=cc(w|0,0,r&7|0)|0;B=C;z=t|z;D=s;A=D;a[A>>0]=z;a[A+1>>0]=z>>8;a[A+2>>0]=z>>16;a[A+3>>0]=z>>24;D=D+4|0;a[D>>0]=B;a[D+1>>0]=B>>8;a[D+2>>0]=B>>16;a[D+3>>0]=B>>24;c[p>>2]=(c[p>>2]|0)+v}E=E+1|0}switch(f|0){case 4:{pa(406196,406218,54,406251);break}case 6:{pa(406267,406218,55,406251);break}case 21:{pa(406196,406218,54,406251);break}case 23:{pa(406267,406218,55,406251);break}case 27:{pa(406196,406218,54,406251);break}case 29:{pa(406267,406218,55,406251);break}case 33:{pa(406196,406218,54,406251);break}case 35:{pa(406267,406218,55,406251);break}case 37:{pa(406196,406218,54,406251);break}case 39:{pa(406267,406218,55,406251);break}case 42:return}}function mb(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0;if((a|0)!=1){i=(aa(a+-1|0)|0)^31;j=i+1|0;if(!a)pa(405225,404333,86,405236);else{g=j>>>0<16?4:(i+4|0)>>>2;k=j}if(a>>>0<16777217){h=g;l=k}else pa(405253,404333,87,405236);if(l>>>0<25)f=h;else pa(405273,404333,88,405236)}else f=4;c[e>>2]=f+-4;c[e+4>>2]=0;c[d>>2]=f<<2;d=b;c[d>>2]=a+-1;c[d+4>>2]=0;return}function nb(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;if(!b){k=f+((c[e>>2]|0)>>>3)|0;b=d[k>>0]|0;f=k;a[f>>0]=b;a[f+1>>0]=b>>8;a[f+2>>0]=b>>16;a[f+3>>0]=b>>24;k=k+4|0;a[k>>0]=0;a[k+1>>0]=0;a[k+2>>0]=0;a[k+3>>0]=0;k=(c[e>>2]|0)+1|0;c[e>>2]=k;return}k=(aa(b|0)|0)^31;j=c[e>>2]|0;g=f+(j>>>3)|0;i=d[g>>0]|0;j=cc(1,0,j&7|0)|0;h=C;j=i|j;i=g;a[i>>0]=j;a[i+1>>0]=j>>8;a[i+2>>0]=j>>16;a[i+3>>0]=j>>24;g=g+4|0;a[g>>0]=h;a[g+1>>0]=h>>8;a[g+2>>0]=h>>16;a[g+3>>0]=h>>24;g=(c[e>>2]|0)+1|0;c[e>>2]=g;h=f+(g>>>3)|0;i=d[h>>0]|0;if(!((k&56|0)==0&0==0))pa(406196,406218,54,406251);l=cc(k|0,0,g&7|0)|0;g=C;l=i|l;j=h;i=j;a[i>>0]=l;a[i+1>>0]=l>>8;a[i+2>>0]=l>>16;a[i+3>>0]=l>>24;j=j+4|0;a[j>>0]=g;a[j+1>>0]=g>>8;a[j+2>>0]=g>>16;a[j+3>>0]=g>>24;j=(c[e>>2]|0)+3|0;c[e>>2]=j;i=b-(1<<k)|0;g=f+(j>>>3)|0;h=d[g>>0]|0;f=bc(i|0,0,k|0)|0;if(!((f|0)==0&(C|0)==0))pa(406196,406218,54,406251);if(k>>>0>=57)pa(406267,406218,55,406251);j=cc(i|0,0,j&7|0)|0;f=C;j=h|j;l=g;b=l;a[b>>0]=j;a[b+1>>0]=j>>8;a[b+2>>0]=j>>16;a[b+3>>0]=j>>24;l=l+4|0;a[l>>0]=f;a[l+1>>0]=f>>8;a[l+2>>0]=f>>16;a[l+3>>0]=f>>24;l=(c[e>>2]|0)+k|0;c[e>>2]=l;return}function ob(a,b,d,e,f,j,k){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;j=j|0;k=k|0;var l=0.0,m=0.0,n=0.0,o=0.0,p=0.0,q=0.0,r=0.0,s=0.0,t=0,u=0,v=0,w=0;w=i;i=i+1040|0;t=w;if((d|0)==(e|0)){i=w;return}u=e>>>0<d>>>0;v=u?d:e;u=u?e:d;d=c[b+(u<<2)>>2]|0;e=c[b+(v<<2)>>2]|0;b=d+e|0;s=+(d>>>0);if(d>>>0<256)r=+g[19516+(d<<2)>>2];else r=+Xb(s);q=+(e>>>0);if(e>>>0<256)p=+g[19516+(e<<2)>>2];else p=+Xb(q);n=+(b>>>0);if(b>>>0<256)m=+g[19516+(b<<2)>>2];else m=+Xb(n);l=+h[a+(u*1040|0)+1032>>3];o=+h[a+(v*1040|0)+1032>>3];n=(s*r+q*p-n*m)*.5-l-o;if(c[a+(u*1040|0)+1024>>2]|0){d=c[a+(v*1040|0)+1024>>2]|0;if(d){if(c[k>>2]|0){l=+h[j+16>>3];if(l<0.0)m=0.0;else m=l}else m=1.e+99;dc(t|0,a+(u*1040|0)|0,1040)|0;b=t+1024|0;c[b>>2]=(c[b>>2]|0)+d;d=0;while(1){if((d|0)==256)break;b=t+(d<<2)|0;c[b>>2]=(c[b>>2]|0)+(c[a+(v*1040|0)+(d<<2)>>2]|0);d=d+1|0}l=+Wa(t);if(!(l<m-n)){i=w;return}}}else l=o;m=n+l;d=c[k>>2]|0;do if(d){n=+h[j+16>>3];if(n!=m){if(!(n>m))break}else if(((c[j+4>>2]|0)-(c[j>>2]|0)|0)>>>0<=(v-u|0)>>>0)break;if(d>>>0<f>>>0){f=j+(d*24|0)|0;c[f>>2]=c[j>>2];c[f+4>>2]=c[j+4>>2];c[f+8>>2]=c[j+8>>2];c[f+12>>2]=c[j+12>>2];c[f+16>>2]=c[j+16>>2];c[f+20>>2]=c[j+20>>2];c[k>>2]=(c[k>>2]|0)+1}c[j>>2]=u;c[j+4>>2]=v;h[j+8>>3]=l;h[j+16>>3]=m;i=w;return}while(0);if(d>>>0>=f>>>0){i=w;return}c[j+(d*24|0)>>2]=u;c[j+(d*24|0)+4>>2]=v;h[j+(d*24|0)+8>>3]=l;h[j+(d*24|0)+16>>3]=m;c[k>>2]=(c[k>>2]|0)+1;i=w;return}function pb(a,b,d,e,f,g,j,k,l){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0.0,s=0.0,t=0,u=0,v=0.0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;F=i;i=i+32|0;E=F+24|0;D=F;c[E>>2]=0;m=g+-1|0;o=0;while(1){if((o|0)==(g|0))break;n=e+(o<<2)|0;q=o;while(1){p=q+1|0;if((q|0)==(m|0))break;ob(a,b,c[n>>2]|0,c[e+(p<<2)>>2]|0,l,f,E);q=p}o=o+1|0}x=f+16|0;y=f+4|0;z=f+8|0;m=g;v=0.0;w=1;a:while(1){u=m;while(1){m=m+-1|0;if(u>>>0<=w>>>0)break a;if(+h[x>>3]>=v){m=u;v=1.e+99;w=k;continue a}t=c[f>>2]|0;g=c[y>>2]|0;n=a+(t*1040|0)+1024|0;c[n>>2]=(c[n>>2]|0)+(c[a+(g*1040|0)+1024>>2]|0);n=0;while(1){if((n|0)==256)break;q=a+(t*1040|0)+(n<<2)|0;c[q>>2]=(c[q>>2]|0)+(c[a+(g*1040|0)+(n<<2)>>2]|0);n=n+1|0}h[a+(t*1040|0)+1032>>3]=+h[z>>3];o=b+(t<<2)|0;c[o>>2]=(c[o>>2]|0)+(c[b+(g<<2)>>2]|0);o=0;while(1){if((o|0)==(j|0)){o=0;break}n=d+(o<<2)|0;if((c[n>>2]|0)==(g|0))c[n>>2]=t;o=o+1|0}while(1){if(u>>>0<=o>>>0)break;n=e+(o<<2)|0;if((c[n>>2]|0)==(g|0)){A=22;break}o=o+1|0}if((A|0)==22){A=0;fc(n|0,e+(o+1<<2)|0,(u-o<<2)+-4|0)|0}p=c[E>>2]|0;n=0;q=0;while(1){if((q|0)==(p|0))break;o=f+(q*24|0)|0;if(((c[o>>2]|0)!=(t|0)?(B=c[f+(q*24|0)+4>>2]|0,(B|0)!=(t|0)):0)?(C=c[o>>2]|0,!((C|0)==(g|0)|(B|0)==(g|0))):0){r=+h[x>>3];s=+h[f+(q*24|0)+16>>3];if(r!=s)if(r>s)A=32;else A=33;else if(((c[y>>2]|0)-(c[f>>2]|0)|0)>>>0>(B-C|0)>>>0)A=32;else A=33;if((A|0)==32){A=0;c[D>>2]=c[f>>2];c[D+4>>2]=c[f+4>>2];c[D+8>>2]=c[f+8>>2];c[D+12>>2]=c[f+12>>2];c[D+16>>2]=c[f+16>>2];c[D+20>>2]=c[f+20>>2];c[f>>2]=c[o>>2];c[f+4>>2]=c[o+4>>2];c[f+8>>2]=c[o+8>>2];c[f+12>>2]=c[o+12>>2];c[f+16>>2]=c[o+16>>2];c[f+20>>2]=c[o+20>>2];o=f+(n*24|0)|0;c[o>>2]=c[D>>2];c[o+4>>2]=c[D+4>>2];c[o+8>>2]=c[D+8>>2];c[o+12>>2]=c[D+12>>2];c[o+16>>2]=c[D+16>>2];c[o+20>>2]=c[D+20>>2]}else if((A|0)==33){A=0;G=f+(n*24|0)|0;c[G>>2]=c[o>>2];c[G+4>>2]=c[o+4>>2];c[G+8>>2]=c[o+8>>2];c[G+12>>2]=c[o+12>>2];c[G+16>>2]=c[o+16>>2];c[G+20>>2]=c[o+20>>2]}n=n+1|0}q=q+1|0}c[E>>2]=n;n=0;while(1){if((n|0)==(m|0))break;ob(a,b,t,c[e+(n<<2)>>2]|0,l,f,E);n=n+1|0}u=u+-1|0}}i=F;return u|0}function qb(a,b,d,e,f,j,k){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;j=j|0;k=k|0;var l=0.0,m=0.0,n=0.0,o=0.0,p=0.0,q=0.0,r=0.0,s=0.0,t=0,u=0,v=0,w=0;w=i;i=i+2832|0;t=w;if((d|0)==(e|0)){i=w;return}u=e>>>0<d>>>0;v=u?d:e;u=u?e:d;d=c[b+(u<<2)>>2]|0;e=c[b+(v<<2)>>2]|0;b=d+e|0;s=+(d>>>0);if(d>>>0<256)r=+g[19516+(d<<2)>>2];else r=+Xb(s);q=+(e>>>0);if(e>>>0<256)p=+g[19516+(e<<2)>>2];else p=+Xb(q);n=+(b>>>0);if(b>>>0<256)m=+g[19516+(b<<2)>>2];else m=+Xb(n);l=+h[a+(u*2832|0)+2824>>3];o=+h[a+(v*2832|0)+2824>>3];n=(s*r+q*p-n*m)*.5-l-o;if(c[a+(u*2832|0)+2816>>2]|0){d=c[a+(v*2832|0)+2816>>2]|0;if(d){if(c[k>>2]|0){l=+h[j+16>>3];if(l<0.0)m=0.0;else m=l}else m=1.e+99;dc(t|0,a+(u*2832|0)|0,2832)|0;b=t+2816|0;c[b>>2]=(c[b>>2]|0)+d;d=0;while(1){if((d|0)==704)break;b=t+(d<<2)|0;c[b>>2]=(c[b>>2]|0)+(c[a+(v*2832|0)+(d<<2)>>2]|0);d=d+1|0}l=+Xa(t);if(!(l<m-n)){i=w;return}}}else l=o;m=n+l;d=c[k>>2]|0;do if(d){n=+h[j+16>>3];if(n!=m){if(!(n>m))break}else if(((c[j+4>>2]|0)-(c[j>>2]|0)|0)>>>0<=(v-u|0)>>>0)break;if(d>>>0<f>>>0){f=j+(d*24|0)|0;c[f>>2]=c[j>>2];c[f+4>>2]=c[j+4>>2];c[f+8>>2]=c[j+8>>2];c[f+12>>2]=c[j+12>>2];c[f+16>>2]=c[j+16>>2];c[f+20>>2]=c[j+20>>2];c[k>>2]=(c[k>>2]|0)+1}c[j>>2]=u;c[j+4>>2]=v;h[j+8>>3]=l;h[j+16>>3]=m;i=w;return}while(0);if(d>>>0>=f>>>0){i=w;return}c[j+(d*24|0)>>2]=u;c[j+(d*24|0)+4>>2]=v;h[j+(d*24|0)+8>>3]=l;h[j+(d*24|0)+16>>3]=m;c[k>>2]=(c[k>>2]|0)+1;i=w;return}function rb(a,b,d,e,f,g,j,k,l){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0.0,s=0.0,t=0,u=0,v=0.0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;F=i;i=i+32|0;E=F+24|0;D=F;c[E>>2]=0;m=g+-1|0;o=0;while(1){if((o|0)==(g|0))break;n=e+(o<<2)|0;q=o;while(1){p=q+1|0;if((q|0)==(m|0))break;qb(a,b,c[n>>2]|0,c[e+(p<<2)>>2]|0,l,f,E);q=p}o=o+1|0}x=f+16|0;y=f+4|0;z=f+8|0;m=g;v=0.0;w=1;a:while(1){u=m;while(1){m=m+-1|0;if(u>>>0<=w>>>0)break a;if(+h[x>>3]>=v){m=u;v=1.e+99;w=k;continue a}t=c[f>>2]|0;g=c[y>>2]|0;n=a+(t*2832|0)+2816|0;c[n>>2]=(c[n>>2]|0)+(c[a+(g*2832|0)+2816>>2]|0);n=0;while(1){if((n|0)==704)break;q=a+(t*2832|0)+(n<<2)|0;c[q>>2]=(c[q>>2]|0)+(c[a+(g*2832|0)+(n<<2)>>2]|0);n=n+1|0}h[a+(t*2832|0)+2824>>3]=+h[z>>3];o=b+(t<<2)|0;c[o>>2]=(c[o>>2]|0)+(c[b+(g<<2)>>2]|0);o=0;while(1){if((o|0)==(j|0)){o=0;break}n=d+(o<<2)|0;if((c[n>>2]|0)==(g|0))c[n>>2]=t;o=o+1|0}while(1){if(u>>>0<=o>>>0)break;n=e+(o<<2)|0;if((c[n>>2]|0)==(g|0)){A=22;break}o=o+1|0}if((A|0)==22){A=0;fc(n|0,e+(o+1<<2)|0,(u-o<<2)+-4|0)|0}p=c[E>>2]|0;n=0;q=0;while(1){if((q|0)==(p|0))break;o=f+(q*24|0)|0;if(((c[o>>2]|0)!=(t|0)?(B=c[f+(q*24|0)+4>>2]|0,(B|0)!=(t|0)):0)?(C=c[o>>2]|0,!((C|0)==(g|0)|(B|0)==(g|0))):0){r=+h[x>>3];s=+h[f+(q*24|0)+16>>3];if(r!=s)if(r>s)A=32;else A=33;else if(((c[y>>2]|0)-(c[f>>2]|0)|0)>>>0>(B-C|0)>>>0)A=32;else A=33;if((A|0)==32){A=0;c[D>>2]=c[f>>2];c[D+4>>2]=c[f+4>>2];c[D+8>>2]=c[f+8>>2];c[D+12>>2]=c[f+12>>2];c[D+16>>2]=c[f+16>>2];c[D+20>>2]=c[f+20>>2];c[f>>2]=c[o>>2];c[f+4>>2]=c[o+4>>2];c[f+8>>2]=c[o+8>>2];c[f+12>>2]=c[o+12>>2];c[f+16>>2]=c[o+16>>2];c[f+20>>2]=c[o+20>>2];o=f+(n*24|0)|0;c[o>>2]=c[D>>2];c[o+4>>2]=c[D+4>>2];c[o+8>>2]=c[D+8>>2];c[o+12>>2]=c[D+12>>2];c[o+16>>2]=c[D+16>>2];c[o+20>>2]=c[D+20>>2]}else if((A|0)==33){A=0;G=f+(n*24|0)|0;c[G>>2]=c[o>>2];c[G+4>>2]=c[o+4>>2];c[G+8>>2]=c[o+8>>2];c[G+12>>2]=c[o+12>>2];c[G+16>>2]=c[o+16>>2];c[G+20>>2]=c[o+20>>2]}n=n+1|0}q=q+1|0}c[E>>2]=n;n=0;while(1){if((n|0)==(m|0))break;qb(a,b,t,c[e+(n<<2)>>2]|0,l,f,E);n=n+1|0}u=u+-1|0}}i=F;return u|0}function sb(a,b,d,e,f,j,k){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;j=j|0;k=k|0;var l=0.0,m=0.0,n=0.0,o=0.0,p=0.0,q=0.0,r=0.0,s=0.0,t=0,u=0,v=0,w=0;w=i;i=i+2096|0;t=w;if((d|0)==(e|0)){i=w;return}u=e>>>0<d>>>0;v=u?d:e;u=u?e:d;d=c[b+(u<<2)>>2]|0;e=c[b+(v<<2)>>2]|0;b=d+e|0;s=+(d>>>0);if(d>>>0<256)r=+g[19516+(d<<2)>>2];else r=+Xb(s);q=+(e>>>0);if(e>>>0<256)p=+g[19516+(e<<2)>>2];else p=+Xb(q);n=+(b>>>0);if(b>>>0<256)m=+g[19516+(b<<2)>>2];else m=+Xb(n);l=+h[a+(u*2096|0)+2088>>3];o=+h[a+(v*2096|0)+2088>>3];n=(s*r+q*p-n*m)*.5-l-o;if(c[a+(u*2096|0)+2080>>2]|0){d=c[a+(v*2096|0)+2080>>2]|0;if(d){if(c[k>>2]|0){l=+h[j+16>>3];if(l<0.0)m=0.0;else m=l}else m=1.e+99;dc(t|0,a+(u*2096|0)|0,2096)|0;b=t+2080|0;c[b>>2]=(c[b>>2]|0)+d;d=0;while(1){if((d|0)==520)break;b=t+(d<<2)|0;c[b>>2]=(c[b>>2]|0)+(c[a+(v*2096|0)+(d<<2)>>2]|0);d=d+1|0}l=+Ya(t);if(!(l<m-n)){i=w;return}}}else l=o;m=n+l;d=c[k>>2]|0;do if(d){n=+h[j+16>>3];if(n!=m){if(!(n>m))break}else if(((c[j+4>>2]|0)-(c[j>>2]|0)|0)>>>0<=(v-u|0)>>>0)break;if(d>>>0<f>>>0){f=j+(d*24|0)|0;c[f>>2]=c[j>>2];c[f+4>>2]=c[j+4>>2];c[f+8>>2]=c[j+8>>2];c[f+12>>2]=c[j+12>>2];c[f+16>>2]=c[j+16>>2];c[f+20>>2]=c[j+20>>2];c[k>>2]=(c[k>>2]|0)+1}c[j>>2]=u;c[j+4>>2]=v;h[j+8>>3]=l;h[j+16>>3]=m;i=w;return}while(0);if(d>>>0>=f>>>0){i=w;return}c[j+(d*24|0)>>2]=u;c[j+(d*24|0)+4>>2]=v;h[j+(d*24|0)+8>>3]=l;h[j+(d*24|0)+16>>3]=m;c[k>>2]=(c[k>>2]|0)+1;i=w;return}function tb(a,b,d,e,f,g,j,k,l){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0.0,s=0.0,t=0,u=0,v=0.0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;F=i;i=i+32|0;E=F+24|0;D=F;c[E>>2]=0;m=g+-1|0;o=0;while(1){if((o|0)==(g|0))break;n=e+(o<<2)|0;q=o;while(1){p=q+1|0;if((q|0)==(m|0))break;sb(a,b,c[n>>2]|0,c[e+(p<<2)>>2]|0,l,f,E);q=p}o=o+1|0}x=f+16|0;y=f+4|0;z=f+8|0;m=g;v=0.0;w=1;a:while(1){u=m;while(1){m=m+-1|0;if(u>>>0<=w>>>0)break a;if(+h[x>>3]>=v){m=u;v=1.e+99;w=k;continue a}t=c[f>>2]|0;g=c[y>>2]|0;n=a+(t*2096|0)+2080|0;c[n>>2]=(c[n>>2]|0)+(c[a+(g*2096|0)+2080>>2]|0);n=0;while(1){if((n|0)==520)break;q=a+(t*2096|0)+(n<<2)|0;c[q>>2]=(c[q>>2]|0)+(c[a+(g*2096|0)+(n<<2)>>2]|0);n=n+1|0}h[a+(t*2096|0)+2088>>3]=+h[z>>3];o=b+(t<<2)|0;c[o>>2]=(c[o>>2]|0)+(c[b+(g<<2)>>2]|0);o=0;while(1){if((o|0)==(j|0)){o=0;break}n=d+(o<<2)|0;if((c[n>>2]|0)==(g|0))c[n>>2]=t;o=o+1|0}while(1){if(u>>>0<=o>>>0)break;n=e+(o<<2)|0;if((c[n>>2]|0)==(g|0)){A=22;break}o=o+1|0}if((A|0)==22){A=0;fc(n|0,e+(o+1<<2)|0,(u-o<<2)+-4|0)|0}p=c[E>>2]|0;n=0;q=0;while(1){if((q|0)==(p|0))break;o=f+(q*24|0)|0;if(((c[o>>2]|0)!=(t|0)?(B=c[f+(q*24|0)+4>>2]|0,(B|0)!=(t|0)):0)?(C=c[o>>2]|0,!((C|0)==(g|0)|(B|0)==(g|0))):0){r=+h[x>>3];s=+h[f+(q*24|0)+16>>3];if(r!=s)if(r>s)A=32;else A=33;else if(((c[y>>2]|0)-(c[f>>2]|0)|0)>>>0>(B-C|0)>>>0)A=32;else A=33;if((A|0)==32){A=0;c[D>>2]=c[f>>2];c[D+4>>2]=c[f+4>>2];c[D+8>>2]=c[f+8>>2];c[D+12>>2]=c[f+12>>2];c[D+16>>2]=c[f+16>>2];c[D+20>>2]=c[f+20>>2];c[f>>2]=c[o>>2];c[f+4>>2]=c[o+4>>2];c[f+8>>2]=c[o+8>>2];c[f+12>>2]=c[o+12>>2];c[f+16>>2]=c[o+16>>2];c[f+20>>2]=c[o+20>>2];o=f+(n*24|0)|0;c[o>>2]=c[D>>2];c[o+4>>2]=c[D+4>>2];c[o+8>>2]=c[D+8>>2];c[o+12>>2]=c[D+12>>2];c[o+16>>2]=c[D+16>>2];c[o+20>>2]=c[D+20>>2]}else if((A|0)==33){A=0;G=f+(n*24|0)|0;c[G>>2]=c[o>>2];c[G+4>>2]=c[o+4>>2];c[G+8>>2]=c[o+8>>2];c[G+12>>2]=c[o+12>>2];c[G+16>>2]=c[o+16>>2];c[G+20>>2]=c[o+20>>2]}n=n+1|0}q=q+1|0}c[E>>2]=n;n=0;while(1){if((n|0)==(m|0))break;sb(a,b,t,c[e+(n<<2)>>2]|0,l,f,E);n=n+1|0}u=u+-1|0}}i=F;return u|0}function ub(b,f,h,j,k,l,m,n,o,p,q,r){b=b|0;f=f|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;var s=0,t=0,u=0,v=0,w=0,x=0,y=0.0,z=0.0,A=0,B=0,D=0.0,E=0.0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0;xa=i;i=i+2304|0;ta=xa+512|0;sa=xa;va=xa+2048|0;ua=xa+1536|0;w=f;A=h>>>0<98304?h:98304;B=c[q>>2]|0;ra=64-((aa(l|0)|0)^31)|0;if(!l)pa(405532,405401,464,405439);if(l>>>0>=2147483649)pa(405605,405401,465,405439);s=l+-1|0;if(s&l)pa(405630,405401,467,405439);qa=bc(16777215,-1,ra|0)|0;if((s|0)!=(qa|0))pa(405667,405401,469,405439);if(!h){if(!j)pa(405466,405401,472,405439);b=r+(B>>>3)|0;k=d[b>>0]|0;va=cc(1,0,B&7|0)|0;wa=C;va=k|va;k=b;a[k>>0]=va;a[k+1>>0]=va>>8;a[k+2>>0]=va>>16;a[k+3>>0]=va>>24;b=b+4|0;a[b>>0]=wa;a[b+1>>0]=wa>>8;a[b+2>>0]=wa>>16;a[b+3>>0]=wa>>24;b=(c[q>>2]|0)+1|0;c[q>>2]=b;wa=r+(b>>>3)|0;r=d[wa>>0]|0;b=cc(1,0,b&7|0)|0;k=C;b=r|b;r=wa;a[r>>0]=b;a[r+1>>0]=b>>8;a[r+2>>0]=b>>16;a[r+3>>0]=b>>24;wa=wa+4|0;a[wa>>0]=k;a[wa+1>>0]=k>>8;a[wa+2>>0]=k>>16;a[wa+3>>0]=k>>24;c[q>>2]=(c[q>>2]|0)+8&-8;i=xa;return}vb(A,0,q,r);x=r+((c[q>>2]|0)>>>3)|0;qa=d[x>>0]|0;s=x;a[s>>0]=qa;a[s+1>>0]=qa>>8;a[s+2>>0]=qa>>16;a[s+3>>0]=qa>>24;x=x+4|0;a[x>>0]=0;a[x+1>>0]=0;a[x+2>>0]=0;a[x+3>>0]=0;c[q>>2]=(c[q>>2]|0)+13;x=wb(b,f,A,va,ua,q,r)|0;s=0;while(1){l=c[o>>2]|0;if((s|7)>>>0>=l>>>0)break;ma=c[q>>2]|0;qa=r+(ma>>>3)|0;na=d[qa>>0]|0;ma=cc(d[p+(s>>>3)>>0]|0,0,ma&7|0)|0;oa=C;ma=na|ma;na=qa;a[na>>0]=ma;a[na+1>>0]=ma>>8;a[na+2>>0]=ma>>16;a[na+3>>0]=ma>>24;qa=qa+4|0;a[qa>>0]=oa;a[qa+1>>0]=oa>>8;a[qa+2>>0]=oa>>16;a[qa+3>>0]=oa>>24;c[q>>2]=(c[q>>2]|0)+8;s=s+8|0}v=l&7;s=d[p+(l>>>3)>>0]|0;l=c[q>>2]|0;t=r+(l>>>3)|0;u=d[t>>0]|0;qa=bc(s|0,0,v|0)|0;if(!((qa|0)==0&(C|0)==0))pa(406196,406218,54,406251);ca=cc(s|0,0,l&7|0)|0;$=C;ca=u|ca;_=t;ba=_;a[ba>>0]=ca;a[ba+1>>0]=ca>>8;a[ba+2>>0]=ca>>16;a[ba+3>>0]=ca>>24;_=_+4|0;a[_>>0]=$;a[_+1>>0]=$>>8;a[_+2>>0]=$>>16;a[_+3>>0]=$>>24;c[q>>2]=(c[q>>2]|0)+v;_=f;$=m+61|0;ba=n+122|0;ca=sa+84|0;da=m+64|0;ea=n+128|0;fa=sa+256|0;ga=m+39|0;ha=n+78|0;ia=sa+188|0;ja=sa+256|0;ka=m+62|0;la=n+124|0;ma=sa+88|0;na=m+63|0;oa=n+126|0;qa=sa+92|0;l=h;s=w;u=A;t=x;a:while(1){X=B+3|0;Y=t>>>0>980;T=s;V=u;v=s;W=u;b:while(1){dc(sa|0,17764,512)|0;U=T;t=U+V|0;Z=t;c:do if(V>>>0>15){w=V+-5|0;S=l+-16|0;S=U+(w>>>0<S>>>0?w:S)|0;w=U+1|0;h=w;u=h;h=h+4|0;u=d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;M=-1;while(1){u=lc(u|0,h|0,-1124073472,1979815)|0;u=bc(u|0,C|0,ra|0)|0;K=v;if(K>>>0>=w>>>0){wa=24;break a}G=0-M|0;Q=w;F=32;while(1){H=Q;h=F;F=F+1|0;R=Q;P=R;R=R+4|0;R=lc(d[P>>0]|d[P+1>>0]<<8|d[P+2>>0]<<16|d[P+3>>0]<<24|0,d[R>>0]|d[R+1>>0]<<8|d[R+2>>0]<<16|d[R+3>>0]<<24|0,-1124073472,1979815)|0;R=bc(R|0,C|0,ra|0)|0;if((u|0)!=(R|0)){wa=26;break a}x=Q+(h>>>5)|0;if(x>>>0>S>>>0)break c;A=x;h=A;A=A+4|0;A=lc(d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0,d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0,-1124073472,1979815)|0;A=bc(A|0,C|0,ra|0)|0;h=Q+G|0;if((d[Q>>0]|d[Q+1>>0]<<8|d[Q+2>>0]<<16|d[Q+3>>0]<<24|0)==(d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)?((G|0)<0?(a[Q+4>>0]|0)==(a[Q+(4-M)>>0]|0):0):0){wa=30;break}u=k+(u<<2)|0;w=c[u>>2]|0;h=f+w|0;if((w|0)<0){wa=32;break a}if(h>>>0>=Q>>>0){wa=34;break a}c[u>>2]=H-_;if((d[Q>>0]|d[Q+1>>0]<<8|d[Q+2>>0]<<16|d[Q+3>>0]<<24|0)!=(d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)){u=A;Q=x;continue}if((a[Q+4>>0]|0)==(a[f+(w+4)>>0]|0))break;else{u=A;Q=x}}if((wa|0)==30){wa=0;c[k+(u<<2)>>2]=H-_}x=h;R=U+(V+-4)|0;u=0;w=Q+5|0;while(1){if(w>>>0>R>>>0){P=u;u=w;break}P=h+(u+5)|0;if((d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24|0)!=(d[P>>0]|d[P+1>>0]<<8|d[P+2>>0]<<16|d[P+3>>0]<<24|0)){P=u;u=w;break}u=u+4|0;w=w+4|0}while(1){if(u>>>0>=t>>>0)break;if((a[h+(P+5)>>0]|0)!=(a[u>>0]|0))break;P=P+1|0;u=u+1|0}L=P+5|0;I=H-x|0;J=H-v|0;N=Q+L|0;O=N;d:do if(L){x=L;A=Q;while(1){u=a[A>>0]|0;w=a[h>>0]|0;if(u<<24>>24!=w<<24>>24)break;x=x+-1|0;if(!x)break d;else{A=A+1|0;h=h+1|0}}if(u<<24>>24!=w<<24>>24){wa=48;break a}}while(0);do if(J>>>0>=6210){if(!(((v-s|0)*50|0)>>>0>J>>>0|Y^1)){wa=84;break b}if(J>>>0<22594){u=a[ka>>0]|0;v=e[la>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;H=bc(v|0,0,u&255|0)|0;if(!((H|0)==0&(C|0)==0)){wa=87;break a}if((u&255)>=57){wa=89;break a}H=cc(v|0,0,h&7|0)|0;h=C;H=x|H;v=w;w=v;a[w>>0]=H;a[w+1>>0]=H>>8;a[w+2>>0]=H>>16;a[w+3>>0]=H>>24;v=v+4|0;a[v>>0]=h;a[v+1>>0]=h>>8;a[v+2>>0]=h>>16;a[v+3>>0]=h>>24;u=(c[q>>2]|0)+(u&255)|0;c[q>>2]=u;v=J+-6210|0;h=r+(u>>>3)|0;w=d[h>>0]|0;if(!((v&-16384|0)==0&0==0)){wa=91;break a}F=cc(v|0,0,u&7|0)|0;H=C;F=w|F;A=h;G=A;a[G>>0]=F;a[G+1>>0]=F>>8;a[G+2>>0]=F>>16;a[G+3>>0]=F>>24;A=A+4|0;a[A>>0]=H;a[A+1>>0]=H>>8;a[A+2>>0]=H>>16;a[A+3>>0]=H>>24;c[q>>2]=(c[q>>2]|0)+14;c[ma>>2]=(c[ma>>2]|0)+1;A=0;break}else{u=a[na>>0]|0;v=e[oa>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;H=bc(v|0,0,u&255|0)|0;if(!((H|0)==0&(C|0)==0)){wa=94;break a}if((u&255)>=57){wa=96;break a}H=cc(v|0,0,h&7|0)|0;h=C;H=x|H;v=w;w=v;a[w>>0]=H;a[w+1>>0]=H>>8;a[w+2>>0]=H>>16;a[w+3>>0]=H>>24;v=v+4|0;a[v>>0]=h;a[v+1>>0]=h>>8;a[v+2>>0]=h>>16;a[v+3>>0]=h>>24;u=(c[q>>2]|0)+(u&255)|0;c[q>>2]=u;v=J+-22594|0;h=r+(u>>>3)|0;w=d[h>>0]|0;if(!((v&-16777216|0)==0&0==0)){wa=98;break a}F=cc(v|0,0,u&7|0)|0;H=C;F=w|F;A=h;G=A;a[G>>0]=F;a[G+1>>0]=F>>8;a[G+2>>0]=F>>16;a[G+3>>0]=F>>24;A=A+4|0;a[A>>0]=H;a[A+1>>0]=H>>8;a[A+2>>0]=H>>16;a[A+3>>0]=H>>24;c[q>>2]=(c[q>>2]|0)+24;c[qa>>2]=(c[qa>>2]|0)+1;A=0;break}}else{if(J>>>0<6){u=J+40|0;v=a[m+u>>0]|0;h=e[n+(u<<1)>>1]|0;w=c[q>>2]|0;x=r+(w>>>3)|0;A=d[x>>0]|0;H=bc(h|0,0,v&255|0)|0;if(!((H|0)==0&(C|0)==0)){wa=52;break a}if((v&255)>=57){wa=54;break a}F=cc(h|0,0,w&7|0)|0;H=C;F=A|F;A=x;G=A;a[G>>0]=F;a[G+1>>0]=F>>8;a[G+2>>0]=F>>16;a[G+3>>0]=F>>24;A=A+4|0;a[A>>0]=H;a[A+1>>0]=H>>8;a[A+2>>0]=H>>16;a[A+3>>0]=H>>24;c[q>>2]=(c[q>>2]|0)+(v&255);A=sa+(u<<2)|0;c[A>>2]=(c[A>>2]|0)+1;A=0;break}if(J>>>0<130){A=J+-2|0;G=((aa(A|0)|0)^31)+-1|0;F=A>>>G;H=(G<<1)+F+42|0;u=a[m+H>>0]|0;v=e[n+(H<<1)>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=58;break a}if((u&255)>=57){wa=60;break a}ya=cc(v|0,0,h&7|0)|0;v=C;ya=x|ya;h=w;a[h>>0]=ya;a[h+1>>0]=ya>>8;a[h+2>>0]=ya>>16;a[h+3>>0]=ya>>24;w=w+4|0;a[w>>0]=v;a[w+1>>0]=v>>8;a[w+2>>0]=v>>16;a[w+3>>0]=v>>24;w=(c[q>>2]|0)+(u&255)|0;c[q>>2]=w;u=A-(F<<G)|0;v=r+(w>>>3)|0;h=d[v>>0]|0;ya=bc(u|0,0,G|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=62;break a}if(G>>>0>=57){wa=64;break a}x=cc(u|0,0,w&7|0)|0;ya=C;x=h|x;A=v;F=A;a[F>>0]=x;a[F+1>>0]=x>>8;a[F+2>>0]=x>>16;a[F+3>>0]=x>>24;A=A+4|0;a[A>>0]=ya;a[A+1>>0]=ya>>8;a[A+2>>0]=ya>>16;a[A+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+G;A=sa+(H<<2)|0;c[A>>2]=(c[A>>2]|0)+1;A=0;break}if(J>>>0>=2114){u=a[$>>0]|0;v=e[ba>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=77;break a}if((u&255)>=57){wa=79;break a}ya=cc(v|0,0,h&7|0)|0;h=C;ya=x|ya;v=w;w=v;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;v=v+4|0;a[v>>0]=h;a[v+1>>0]=h>>8;a[v+2>>0]=h>>16;a[v+3>>0]=h>>24;u=(c[q>>2]|0)+(u&255)|0;c[q>>2]=u;v=J+-2114|0;h=r+(u>>>3)|0;w=d[h>>0]|0;if(!((v&-4096|0)==0&0==0)){wa=81;break a}G=cc(v|0,0,u&7|0)|0;ya=C;G=w|G;A=h;H=A;a[H>>0]=G;a[H+1>>0]=G>>8;a[H+2>>0]=G>>16;a[H+3>>0]=G>>24;A=A+4|0;a[A>>0]=ya;a[A+1>>0]=ya>>8;a[A+2>>0]=ya>>16;a[A+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+12;c[ca>>2]=(c[ca>>2]|0)+1;A=0;break}A=J+-66|0;F=(aa(A|0)|0)^31;G=F+50|0;u=a[m+G>>0]|0;v=e[n+(G<<1)>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=68;break a}if((u&255)>=57){wa=70;break a}ya=cc(v|0,0,h&7|0)|0;v=C;ya=x|ya;h=w;a[h>>0]=ya;a[h+1>>0]=ya>>8;a[h+2>>0]=ya>>16;a[h+3>>0]=ya>>24;w=w+4|0;a[w>>0]=v;a[w+1>>0]=v>>8;a[w+2>>0]=v>>16;a[w+3>>0]=v>>24;w=(c[q>>2]|0)+(u&255)|0;c[q>>2]=w;u=A-(1<<F)|0;v=r+(w>>>3)|0;h=d[v>>0]|0;ya=bc(u|0,0,F|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=72;break a}if(F>>>0>=57){wa=74;break a}x=cc(u|0,0,w&7|0)|0;ya=C;x=h|x;A=v;H=A;a[H>>0]=x;a[H+1>>0]=x>>8;a[H+2>>0]=x>>16;a[H+3>>0]=x>>24;A=A+4|0;a[A>>0]=ya;a[A+1>>0]=ya>>8;a[A+2>>0]=ya>>16;a[A+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+F;A=sa+(G<<2)|0;c[A>>2]=(c[A>>2]|0)+1;A=0}while(0);while(1){if(A>>>0>=J>>>0)break;v=d[K+A>>0]|0;u=a[va+v>>0]|0;v=e[ua+(v<<1)>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=102;break a}if((u&255)>=57){wa=104;break a}F=cc(v|0,0,h&7|0)|0;H=C;F=x|F;ya=w;G=ya;a[G>>0]=F;a[G+1>>0]=F>>8;a[G+2>>0]=F>>16;a[G+3>>0]=F>>24;ya=ya+4|0;a[ya>>0]=H;a[ya+1>>0]=H>>8;a[ya+2>>0]=H>>16;a[ya+3>>0]=H>>24;c[q>>2]=(c[q>>2]|0)+(u&255);A=A+1|0}if((I|0)==(M|0)){u=a[da>>0]|0;v=e[ea>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=108;break a}if((u&255)>=57){wa=110;break a}J=cc(v|0,0,h&7|0)|0;ya=C;J=x|J;I=w;K=I;a[K>>0]=J;a[K+1>>0]=J>>8;a[K+2>>0]=J>>16;a[K+3>>0]=J>>24;I=I+4|0;a[I>>0]=ya;a[I+1>>0]=ya>>8;a[I+2>>0]=ya>>16;a[I+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+(u&255);c[fa>>2]=(c[fa>>2]|0)+1;I=M}else{A=I+3|0;G=((aa(A|0)|0)^31)+-1|0;F=A>>>G&1;H=((G<<1)+-2|F)+80|0;u=a[m+H>>0]|0;v=e[n+(H<<1)>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=113;break a}if((u&255)>=57){wa=115;break a}ya=cc(v|0,0,h&7|0)|0;v=C;ya=x|ya;h=w;a[h>>0]=ya;a[h+1>>0]=ya>>8;a[h+2>>0]=ya>>16;a[h+3>>0]=ya>>24;w=w+4|0;a[w>>0]=v;a[w+1>>0]=v>>8;a[w+2>>0]=v>>16;a[w+3>>0]=v>>24;w=(c[q>>2]|0)+(u&255)|0;c[q>>2]=w;u=A-((F|2)<<G)|0;v=r+(w>>>3)|0;h=d[v>>0]|0;ya=bc(u|0,0,G|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=117;break a}if(G>>>0>=57){wa=119;break a}J=cc(u|0,0,w&7|0)|0;M=C;J=h|J;ya=v;K=ya;a[K>>0]=J;a[K+1>>0]=J>>8;a[K+2>>0]=J>>16;a[K+3>>0]=J>>24;ya=ya+4|0;a[ya>>0]=M;a[ya+1>>0]=M>>8;a[ya+2>>0]=M>>16;a[ya+3>>0]=M>>24;c[q>>2]=(c[q>>2]|0)+G;ya=sa+(H<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1}do if(L>>>0<12){u=P+1|0;v=a[m+u>>0]|0;h=e[n+(u<<1)>>1]|0;w=c[q>>2]|0;x=r+(w>>>3)|0;A=d[x>>0]|0;ya=bc(h|0,0,v&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=123;break a}if((v&255)>=57){wa=125;break a}K=cc(h|0,0,w&7|0)|0;M=C;K=A|K;ya=x;L=ya;a[L>>0]=K;a[L+1>>0]=K>>8;a[L+2>>0]=K>>16;a[L+3>>0]=K>>24;ya=ya+4|0;a[ya>>0]=M;a[ya+1>>0]=M>>8;a[ya+2>>0]=M>>16;a[ya+3>>0]=M>>24;c[q>>2]=(c[q>>2]|0)+(v&255);ya=sa+(u<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1}else{if(L>>>0<72){A=P+-3|0;G=((aa(A|0)|0)^31)+-1|0;F=A>>>G;H=(G<<1)+F+4|0;u=a[m+H>>0]|0;v=e[n+(H<<1)>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=129;break a}if((u&255)>=57){wa=131;break a}ya=cc(v|0,0,h&7|0)|0;v=C;ya=x|ya;h=w;a[h>>0]=ya;a[h+1>>0]=ya>>8;a[h+2>>0]=ya>>16;a[h+3>>0]=ya>>24;w=w+4|0;a[w>>0]=v;a[w+1>>0]=v>>8;a[w+2>>0]=v>>16;a[w+3>>0]=v>>24;w=(c[q>>2]|0)+(u&255)|0;c[q>>2]=w;u=A-(F<<G)|0;v=r+(w>>>3)|0;h=d[v>>0]|0;ya=bc(u|0,0,G|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=133;break a}if(G>>>0>=57){wa=135;break a}K=cc(u|0,0,w&7|0)|0;M=C;K=h|K;ya=v;L=ya;a[L>>0]=K;a[L+1>>0]=K>>8;a[L+2>>0]=K>>16;a[L+3>>0]=K>>24;ya=ya+4|0;a[ya>>0]=M;a[ya+1>>0]=M>>8;a[ya+2>>0]=M>>16;a[ya+3>>0]=M>>24;c[q>>2]=(c[q>>2]|0)+G;ya=sa+(H<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1;break}if(L>>>0<136){u=P+-3|0;F=(u>>>5)+30|0;v=a[m+F>>0]|0;h=e[n+(F<<1)>>1]|0;w=c[q>>2]|0;x=r+(w>>>3)|0;A=d[x>>0]|0;ya=bc(h|0,0,v&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=139;break a}if((v&255)>=57){wa=141;break a}ya=cc(h|0,0,w&7|0)|0;h=C;ya=A|ya;w=x;x=w;a[x>>0]=ya;a[x+1>>0]=ya>>8;a[x+2>>0]=ya>>16;a[x+3>>0]=ya>>24;w=w+4|0;a[w>>0]=h;a[w+1>>0]=h>>8;a[w+2>>0]=h>>16;a[w+3>>0]=h>>24;w=(c[q>>2]|0)+(v&255)|0;c[q>>2]=w;h=r+(w>>>3)|0;x=d[h>>0]|0;w=cc(u&31|0,0,w&7|0)|0;v=C;w=x|w;u=h;h=u;a[h>>0]=w;a[h+1>>0]=w>>8;a[h+2>>0]=w>>16;a[h+3>>0]=w>>24;u=u+4|0;a[u>>0]=v;a[u+1>>0]=v>>8;a[u+2>>0]=v>>16;a[u+3>>0]=v>>24;u=(c[q>>2]|0)+5|0;c[q>>2]=u;v=a[da>>0]|0;h=e[ea>>1]|0;w=r+(u>>>3)|0;x=d[w>>0]|0;ya=bc(h|0,0,v&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=143;break a}if((v&255)>=57){wa=145;break a}K=cc(h|0,0,u&7|0)|0;M=C;K=x|K;ya=w;L=ya;a[L>>0]=K;a[L+1>>0]=K>>8;a[L+2>>0]=K>>16;a[L+3>>0]=K>>24;ya=ya+4|0;a[ya>>0]=M;a[ya+1>>0]=M>>8;a[ya+2>>0]=M>>16;a[ya+3>>0]=M>>24;c[q>>2]=(c[q>>2]|0)+(v&255);ya=sa+(F<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1;c[ja>>2]=(c[ja>>2]|0)+1;break}if(L>>>0>=2120){u=a[ga>>0]|0;v=e[ha>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=162;break a}if((u&255)>=57){wa=164;break a}ya=cc(v|0,0,h&7|0)|0;h=C;ya=x|ya;v=w;w=v;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;v=v+4|0;a[v>>0]=h;a[v+1>>0]=h>>8;a[v+2>>0]=h>>16;a[v+3>>0]=h>>24;u=(c[q>>2]|0)+(u&255)|0;c[q>>2]=u;v=P+-2115|0;h=r+(u>>>3)|0;w=d[h>>0]|0;if(!((v&-16777216|0)==0&0==0)){wa=166;break a}u=cc(v|0,0,u&7|0)|0;v=C;w=w|u;u=h;h=u;a[h>>0]=w;a[h+1>>0]=w>>8;a[h+2>>0]=w>>16;a[h+3>>0]=w>>24;u=u+4|0;a[u>>0]=v;a[u+1>>0]=v>>8;a[u+2>>0]=v>>16;a[u+3>>0]=v>>24;u=(c[q>>2]|0)+24|0;c[q>>2]=u;v=a[da>>0]|0;h=e[ea>>1]|0;w=r+(u>>>3)|0;x=d[w>>0]|0;ya=bc(h|0,0,v&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=168;break a}if((v&255)>=57){wa=170;break a}K=cc(h|0,0,u&7|0)|0;M=C;K=x|K;ya=w;L=ya;a[L>>0]=K;a[L+1>>0]=K>>8;a[L+2>>0]=K>>16;a[L+3>>0]=K>>24;ya=ya+4|0;a[ya>>0]=M;a[ya+1>>0]=M>>8;a[ya+2>>0]=M>>16;a[ya+3>>0]=M>>24;c[q>>2]=(c[q>>2]|0)+(v&255);c[ia>>2]=(c[ia>>2]|0)+1;c[ja>>2]=(c[ja>>2]|0)+1;break}A=P+-67|0;F=(aa(A|0)|0)^31;G=F+28|0;u=a[m+G>>0]|0;v=e[n+(G<<1)>>1]|0;h=c[q>>2]|0;w=r+(h>>>3)|0;x=d[w>>0]|0;ya=bc(v|0,0,u&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=149;break a}if((u&255)>=57){wa=151;break a}ya=cc(v|0,0,h&7|0)|0;v=C;ya=x|ya;h=w;a[h>>0]=ya;a[h+1>>0]=ya>>8;a[h+2>>0]=ya>>16;a[h+3>>0]=ya>>24;w=w+4|0;a[w>>0]=v;a[w+1>>0]=v>>8;a[w+2>>0]=v>>16;a[w+3>>0]=v>>24;w=(c[q>>2]|0)+(u&255)|0;c[q>>2]=w;u=A-(1<<F)|0;v=r+(w>>>3)|0;h=d[v>>0]|0;ya=bc(u|0,0,F|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=153;break a}if(F>>>0>=57){wa=155;break a}u=cc(u|0,0,w&7|0)|0;w=C;h=h|u;u=v;v=u;a[v>>0]=h;a[v+1>>0]=h>>8;a[v+2>>0]=h>>16;a[v+3>>0]=h>>24;u=u+4|0;a[u>>0]=w;a[u+1>>0]=w>>8;a[u+2>>0]=w>>16;a[u+3>>0]=w>>24;u=(c[q>>2]|0)+F|0;c[q>>2]=u;v=a[da>>0]|0;h=e[ea>>1]|0;w=r+(u>>>3)|0;x=d[w>>0]|0;ya=bc(h|0,0,v&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=157;break a}if((v&255)>=57){wa=159;break a}K=cc(h|0,0,u&7|0)|0;M=C;K=x|K;ya=w;L=ya;a[L>>0]=K;a[L+1>>0]=K>>8;a[L+2>>0]=K>>16;a[L+3>>0]=K>>24;ya=ya+4|0;a[ya>>0]=M;a[ya+1>>0]=M>>8;a[ya+2>>0]=M>>16;a[ya+3>>0]=M>>24;c[q>>2]=(c[q>>2]|0)+(v&255);ya=sa+(G<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1;c[ja>>2]=(c[ja>>2]|0)+1}while(0);if(N>>>0>=S>>>0){v=O;break c}v=Q+(P+2)|0;ya=v;ya=d[ya>>0]|d[ya+1>>0]<<8|d[ya+2>>0]<<16|d[ya+3>>0]<<24;v=v+4|0;v=d[v>>0]|d[v+1>>0]<<8|d[v+2>>0]<<16|d[v+3>>0]<<24;Q=lc(ya|0,v|0,-1124073472,1979815)|0;Q=bc(Q|0,C|0,ra|0)|0;x=bc(ya|0,v|0,24)|0;x=lc(x|0,C|0,-1124073472,1979815)|0;x=bc(x|0,C|0,ra|0)|0;A=N-_|0;c[k+(Q<<2)>>2]=A+-3;Q=bc(ya|0,v|0,8)|0;Q=lc(Q|0,C|0,-1124073472,1979815)|0;Q=bc(Q|0,C|0,ra|0)|0;c[k+(Q<<2)>>2]=A+-2;v=bc(ya|0,v|0,16)|0;v=lc(v|0,C|0,-1124073472,1979815)|0;v=bc(v|0,C|0,ra|0)|0;c[k+(v<<2)>>2]=A+-1;x=k+(x<<2)|0;v=c[x>>2]|0;c[x>>2]=A;x=I;A=O;while(1){H=f+v|0;ya=A;u=A;if((d[ya>>0]|d[ya+1>>0]<<8|d[ya+2>>0]<<16|d[ya+3>>0]<<24|0)!=(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24|0))break;if((a[u+4>>0]|0)!=(a[f+(v+4)>>0]|0))break;w=v+5|0;v=0;h=u+5|0;while(1){if(h>>>0>R>>>0)break;ya=f+(w+v)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[ya>>0]|d[ya+1>>0]<<8|d[ya+2>>0]<<16|d[ya+3>>0]<<24|0))break;v=v+4|0;h=h+4|0}while(1){if(h>>>0>=t>>>0)break;if((a[f+(w+v)>>0]|0)!=(a[h>>0]|0))break;v=v+1|0;h=h+1|0}G=v+5|0;K=u+G|0;M=K;L=A-H|0;do if(!G)wa=189;else{A=G;F=u;x=H;while(1){h=a[F>>0]|0;w=a[x>>0]|0;if(h<<24>>24!=w<<24>>24){wa=186;break}A=A+-1|0;if(!A)break;else{F=F+1|0;x=x+1|0}}if((wa|0)==186?(wa=0,h<<24>>24!=w<<24>>24):0){wa=187;break a}if(G>>>0<10){wa=189;break}if(G>>>0<134){G=v+-1|0;I=((aa(G|0)|0)^31)+-1|0;H=G>>>I;J=(I<<1)+H+20|0;h=a[m+J>>0]|0;w=e[n+(J<<1)>>1]|0;x=c[q>>2]|0;A=r+(x>>>3)|0;F=d[A>>0]|0;ya=bc(w|0,0,h&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=196;break a}if((h&255)>=57){wa=198;break a}ya=cc(w|0,0,x&7|0)|0;w=C;ya=F|ya;x=A;a[x>>0]=ya;a[x+1>>0]=ya>>8;a[x+2>>0]=ya>>16;a[x+3>>0]=ya>>24;A=A+4|0;a[A>>0]=w;a[A+1>>0]=w>>8;a[A+2>>0]=w>>16;a[A+3>>0]=w>>24;A=(c[q>>2]|0)+(h&255)|0;c[q>>2]=A;h=G-(H<<I)|0;w=r+(A>>>3)|0;x=d[w>>0]|0;ya=bc(h|0,0,I|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=200;break a}if(I>>>0>=57){wa=202;break a}O=cc(h|0,0,A&7|0)|0;Q=C;O=x|O;ya=w;P=ya;a[P>>0]=O;a[P+1>>0]=O>>8;a[P+2>>0]=O>>16;a[P+3>>0]=O>>24;ya=ya+4|0;a[ya>>0]=Q;a[ya+1>>0]=Q>>8;a[ya+2>>0]=Q>>16;a[ya+3>>0]=Q>>24;c[q>>2]=(c[q>>2]|0)+I;ya=sa+(J<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1;break}if(G>>>0>=2118){h=a[ga>>0]|0;w=e[ha>>1]|0;x=c[q>>2]|0;A=r+(x>>>3)|0;F=d[A>>0]|0;ya=bc(w|0,0,h&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=215;break a}if((h&255)>=57){wa=217;break a}ya=cc(w|0,0,x&7|0)|0;x=C;ya=F|ya;w=A;A=w;a[A>>0]=ya;a[A+1>>0]=ya>>8;a[A+2>>0]=ya>>16;a[A+3>>0]=ya>>24;w=w+4|0;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;h=(c[q>>2]|0)+(h&255)|0;c[q>>2]=h;w=v+-2113|0;x=r+(h>>>3)|0;A=d[x>>0]|0;if(!((w&-16777216|0)==0&0==0)){wa=219;break a}O=cc(w|0,0,h&7|0)|0;Q=C;O=A|O;ya=x;P=ya;a[P>>0]=O;a[P+1>>0]=O>>8;a[P+2>>0]=O>>16;a[P+3>>0]=O>>24;ya=ya+4|0;a[ya>>0]=Q;a[ya+1>>0]=Q>>8;a[ya+2>>0]=Q>>16;a[ya+3>>0]=Q>>24;c[q>>2]=(c[q>>2]|0)+24;c[ia>>2]=(c[ia>>2]|0)+1;break}G=v+-65|0;H=(aa(G|0)|0)^31;I=H+28|0;h=a[m+I>>0]|0;w=e[n+(I<<1)>>1]|0;x=c[q>>2]|0;A=r+(x>>>3)|0;F=d[A>>0]|0;ya=bc(w|0,0,h&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=206;break a}if((h&255)>=57){wa=208;break a}ya=cc(w|0,0,x&7|0)|0;w=C;ya=F|ya;x=A;a[x>>0]=ya;a[x+1>>0]=ya>>8;a[x+2>>0]=ya>>16;a[x+3>>0]=ya>>24;A=A+4|0;a[A>>0]=w;a[A+1>>0]=w>>8;a[A+2>>0]=w>>16;a[A+3>>0]=w>>24;A=(c[q>>2]|0)+(h&255)|0;c[q>>2]=A;h=G-(1<<H)|0;w=r+(A>>>3)|0;x=d[w>>0]|0;ya=bc(h|0,0,H|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=210;break a}if(H>>>0>=57){wa=212;break a}O=cc(h|0,0,A&7|0)|0;Q=C;O=x|O;ya=w;P=ya;a[P>>0]=O;a[P+1>>0]=O>>8;a[P+2>>0]=O>>16;a[P+3>>0]=O>>24;ya=ya+4|0;a[ya>>0]=Q;a[ya+1>>0]=Q>>8;a[ya+2>>0]=Q>>16;a[ya+3>>0]=Q>>24;c[q>>2]=(c[q>>2]|0)+H;ya=sa+(I<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1}while(0);if((wa|0)==189){wa=0;h=v+19|0;w=a[m+h>>0]|0;x=e[n+(h<<1)>>1]|0;A=c[q>>2]|0;F=r+(A>>>3)|0;G=d[F>>0]|0;ya=bc(x|0,0,w&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=190;break a}if((w&255)>=57){wa=192;break a}O=cc(x|0,0,A&7|0)|0;Q=C;O=G|O;ya=F;P=ya;a[P>>0]=O;a[P+1>>0]=O>>8;a[P+2>>0]=O>>16;a[P+3>>0]=O>>24;ya=ya+4|0;a[ya>>0]=Q;a[ya+1>>0]=Q>>8;a[ya+2>>0]=Q>>16;a[ya+3>>0]=Q>>24;c[q>>2]=(c[q>>2]|0)+(w&255);ya=sa+(h<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1}G=L+3|0;I=((aa(G|0)|0)^31)+-1|0;H=G>>>I&1;J=((I<<1)+-2|H)+80|0;h=a[m+J>>0]|0;w=e[n+(J<<1)>>1]|0;x=c[q>>2]|0;A=r+(x>>>3)|0;F=d[A>>0]|0;ya=bc(w|0,0,h&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=222;break a}if((h&255)>=57){wa=224;break a}ya=cc(w|0,0,x&7|0)|0;w=C;ya=F|ya;x=A;a[x>>0]=ya;a[x+1>>0]=ya>>8;a[x+2>>0]=ya>>16;a[x+3>>0]=ya>>24;A=A+4|0;a[A>>0]=w;a[A+1>>0]=w>>8;a[A+2>>0]=w>>16;a[A+3>>0]=w>>24;A=(c[q>>2]|0)+(h&255)|0;c[q>>2]=A;h=G-((H|2)<<I)|0;w=r+(A>>>3)|0;x=d[w>>0]|0;ya=bc(h|0,0,I|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=226;break a}if(I>>>0>=57){wa=228;break a}O=cc(h|0,0,A&7|0)|0;Q=C;O=x|O;ya=w;P=ya;a[P>>0]=O;a[P+1>>0]=O>>8;a[P+2>>0]=O>>16;a[P+3>>0]=O>>24;ya=ya+4|0;a[ya>>0]=Q;a[ya+1>>0]=Q>>8;a[ya+2>>0]=Q>>16;a[ya+3>>0]=Q>>24;c[q>>2]=(c[q>>2]|0)+I;ya=sa+(J<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1;if(K>>>0>=S>>>0){v=M;break c}v=u+(v+2)|0;ya=v;ya=d[ya>>0]|d[ya+1>>0]<<8|d[ya+2>>0]<<16|d[ya+3>>0]<<24;v=v+4|0;v=d[v>>0]|d[v+1>>0]<<8|d[v+2>>0]<<16|d[v+3>>0]<<24;Q=lc(ya|0,v|0,-1124073472,1979815)|0;Q=bc(Q|0,C|0,ra|0)|0;x=bc(ya|0,v|0,24)|0;x=lc(x|0,C|0,-1124073472,1979815)|0;x=bc(x|0,C|0,ra|0)|0;A=K-_|0;c[k+(Q<<2)>>2]=A+-3;Q=bc(ya|0,v|0,8)|0;Q=lc(Q|0,C|0,-1124073472,1979815)|0;Q=bc(Q|0,C|0,ra|0)|0;c[k+(Q<<2)>>2]=A+-2;v=bc(ya|0,v|0,16)|0;v=lc(v|0,C|0,-1124073472,1979815)|0;v=bc(v|0,C|0,ra|0)|0;c[k+(v<<2)>>2]=A+-1;x=k+(x<<2)|0;v=c[x>>2]|0;c[x>>2]=A;x=L;A=M}w=u+1|0;h=w;u=h;h=h+4|0;u=d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;M=x;v=A}}while(0);G=v;if(G>>>0>t>>>0){wa=233;break a}h=l-V|0;w=h>>>0<65536?h:65536;if((l|0)==(V|0)){l=0;wa=254;break}x=W+w|0;if(x>>>0>=1048577){l=h;wa=254;break}ac(ta|0,0,1024)|0;l=0;while(1){if(l>>>0>=w>>>0)break;ya=ta+(d[U+(V+l)>>0]<<2)|0;c[ya>>2]=(c[ya>>2]|0)+1;l=l+43|0}ya=w+42|0;l=(ya>>>0)/43|0;if(ya>>>0<11008){z=+(l>>>0);y=+g[19516+(l<<2)>>2]}else{y=+(l>>>0);z=y;y=+Xb(y)}E=(y+.5)*z+200.0;u=0;while(1){if((u|0)==256)break;l=c[ta+(u<<2)>>2]|0;z=+(l>>>0);D=+(d[va+u>>0]|0);if(l>>>0<256)y=+g[19516+(l<<2)>>2];else y=+Xb(z);E=E-z*(D+y);u=u+1|0}if(!(E>=0.0)){l=h;wa=254;break}if(W>>>0<=65536){wa=250;break a}l=20;t=x+-1|0;u=X;while(1){if(!l){l=h;T=Z;V=w;W=x;continue b}V=u&7;ya=8-V|0;ya=l>>>0<ya>>>0?l:ya;W=r+(u>>>3)|0;a[W>>0]=(t&(1<<ya)+-1)<<V|d[W>>0]&(-1<<V+ya|(1<<V)+255);l=l-ya|0;t=t>>>ya;u=u+ya|0}}e:do if((wa|0)==84){wa=0;ya=H-s|0;Z=r+(B>>>3)|0;a[Z>>0]=d[Z>>0]&(1<<(B&7))+255;c[q>>2]=B;vb(ya,1,q,r);Z=(c[q>>2]|0)+7|0;c[q>>2]=Z&-8;dc(r+(Z>>>3)|0,s|0,ya|0)|0;s=(c[q>>2]|0)+(ya<<3)|0;c[q>>2]=s;a[r+(s>>>3)>>0]=0;l=l+(T-H)|0;s=H}else if((wa|0)==254){wa=0;if(G>>>0<t>>>0){F=t-v|0;if(F>>>0>=6210){if(!(((v-s|0)*50|0)>>>0>F>>>0|Y^1)){ya=t-s|0;Y=r+(B>>>3)|0;a[Y>>0]=d[Y>>0]&(1<<(B&7))+255;c[q>>2]=B;vb(ya,1,q,r);Y=(c[q>>2]|0)+7|0;c[q>>2]=Y&-8;dc(r+(Y>>>3)|0,s|0,ya|0)|0;s=(c[q>>2]|0)+(ya<<3)|0;c[q>>2]=s;a[r+(s>>>3)>>0]=0;s=Z;break}if(F>>>0<22594){s=a[ka>>0]|0;t=e[la>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=299;break a}if((s&255)>=57){wa=301;break a}ya=cc(t|0,0,u&7|0)|0;u=C;ya=h|ya;t=v;v=t;a[v>>0]=ya;a[v+1>>0]=ya>>8;a[v+2>>0]=ya>>16;a[v+3>>0]=ya>>24;t=t+4|0;a[t>>0]=u;a[t+1>>0]=u>>8;a[t+2>>0]=u>>16;a[t+3>>0]=u>>24;s=(c[q>>2]|0)+(s&255)|0;c[q>>2]=s;t=F+-6210|0;u=r+(s>>>3)|0;v=d[u>>0]|0;if(!((t&-16384|0)==0&0==0)){wa=303;break a}X=cc(t|0,0,s&7|0)|0;ya=C;X=v|X;w=u;Y=w;a[Y>>0]=X;a[Y+1>>0]=X>>8;a[Y+2>>0]=X>>16;a[Y+3>>0]=X>>24;w=w+4|0;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+14;c[ma>>2]=(c[ma>>2]|0)+1;w=0}else{s=a[na>>0]|0;t=e[oa>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=306;break a}if((s&255)>=57){wa=308;break a}ya=cc(t|0,0,u&7|0)|0;u=C;ya=h|ya;t=v;v=t;a[v>>0]=ya;a[v+1>>0]=ya>>8;a[v+2>>0]=ya>>16;a[v+3>>0]=ya>>24;t=t+4|0;a[t>>0]=u;a[t+1>>0]=u>>8;a[t+2>>0]=u>>16;a[t+3>>0]=u>>24;s=(c[q>>2]|0)+(s&255)|0;c[q>>2]=s;t=F+-22594|0;u=r+(s>>>3)|0;v=d[u>>0]|0;if(!((t&-16777216|0)==0&0==0)){wa=310;break a}X=cc(t|0,0,s&7|0)|0;ya=C;X=v|X;w=u;Y=w;a[Y>>0]=X;a[Y+1>>0]=X>>8;a[Y+2>>0]=X>>16;a[Y+3>>0]=X>>24;w=w+4|0;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+24;c[qa>>2]=(c[qa>>2]|0)+1;w=0}while(1){if(w>>>0>=F>>>0){s=Z;break e}t=d[G+w>>0]|0;s=a[va+t>>0]|0;t=e[ua+(t<<1)>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=314;break a}if((s&255)>=57){wa=316;break a}W=cc(t|0,0,u&7|0)|0;Y=C;W=h|W;ya=v;X=ya;a[X>>0]=W;a[X+1>>0]=W>>8;a[X+2>>0]=W>>16;a[X+3>>0]=W>>24;ya=ya+4|0;a[ya>>0]=Y;a[ya+1>>0]=Y>>8;a[ya+2>>0]=Y>>16;a[ya+3>>0]=Y>>24;c[q>>2]=(c[q>>2]|0)+(s&255);w=w+1|0}}do if(F>>>0<6){s=F+40|0;t=a[m+s>>0]|0;u=e[n+(s<<1)>>1]|0;v=c[q>>2]|0;h=r+(v>>>3)|0;w=d[h>>0]|0;ya=bc(u|0,0,t&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=258;break a}if((t&255)>=57){wa=260;break a}X=cc(u|0,0,v&7|0)|0;ya=C;X=w|X;w=h;Y=w;a[Y>>0]=X;a[Y+1>>0]=X>>8;a[Y+2>>0]=X>>16;a[Y+3>>0]=X>>24;w=w+4|0;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+(t&255);w=sa+(s<<2)|0;c[w>>2]=(c[w>>2]|0)+1;w=0}else{if(F>>>0<130){w=F+-2|0;A=((aa(w|0)|0)^31)+-1|0;x=w>>>A;B=(A<<1)+x+42|0;s=a[m+B>>0]|0;t=e[n+(B<<1)>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=264;break a}if((s&255)>=57){wa=266;break a}ya=cc(t|0,0,u&7|0)|0;t=C;ya=h|ya;u=v;a[u>>0]=ya;a[u+1>>0]=ya>>8;a[u+2>>0]=ya>>16;a[u+3>>0]=ya>>24;v=v+4|0;a[v>>0]=t;a[v+1>>0]=t>>8;a[v+2>>0]=t>>16;a[v+3>>0]=t>>24;v=(c[q>>2]|0)+(s&255)|0;c[q>>2]=v;s=w-(x<<A)|0;t=r+(v>>>3)|0;u=d[t>>0]|0;ya=bc(s|0,0,A|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=268;break a}if(A>>>0>=57){wa=270;break a}X=cc(s|0,0,v&7|0)|0;ya=C;X=u|X;w=t;Y=w;a[Y>>0]=X;a[Y+1>>0]=X>>8;a[Y+2>>0]=X>>16;a[Y+3>>0]=X>>24;w=w+4|0;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+A;w=sa+(B<<2)|0;c[w>>2]=(c[w>>2]|0)+1;w=0;break}if(F>>>0>=2114){s=a[$>>0]|0;t=e[ba>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=283;break a}if((s&255)>=57){wa=285;break a}ya=cc(t|0,0,u&7|0)|0;u=C;ya=h|ya;t=v;v=t;a[v>>0]=ya;a[v+1>>0]=ya>>8;a[v+2>>0]=ya>>16;a[v+3>>0]=ya>>24;t=t+4|0;a[t>>0]=u;a[t+1>>0]=u>>8;a[t+2>>0]=u>>16;a[t+3>>0]=u>>24;s=(c[q>>2]|0)+(s&255)|0;c[q>>2]=s;t=F+-2114|0;u=r+(s>>>3)|0;v=d[u>>0]|0;if(!((t&-4096|0)==0&0==0)){wa=287;break a}X=cc(t|0,0,s&7|0)|0;ya=C;X=v|X;w=u;Y=w;a[Y>>0]=X;a[Y+1>>0]=X>>8;a[Y+2>>0]=X>>16;a[Y+3>>0]=X>>24;w=w+4|0;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+12;c[ca>>2]=(c[ca>>2]|0)+1;w=0;break}w=F+-66|0;x=(aa(w|0)|0)^31;A=x+50|0;s=a[m+A>>0]|0;t=e[n+(A<<1)>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=274;break a}if((s&255)>=57){wa=276;break a}ya=cc(t|0,0,u&7|0)|0;t=C;ya=h|ya;u=v;a[u>>0]=ya;a[u+1>>0]=ya>>8;a[u+2>>0]=ya>>16;a[u+3>>0]=ya>>24;v=v+4|0;a[v>>0]=t;a[v+1>>0]=t>>8;a[v+2>>0]=t>>16;a[v+3>>0]=t>>24;v=(c[q>>2]|0)+(s&255)|0;c[q>>2]=v;s=w-(1<<x)|0;t=r+(v>>>3)|0;u=d[t>>0]|0;ya=bc(s|0,0,x|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=278;break a}if(x>>>0>=57){wa=280;break a}X=cc(s|0,0,v&7|0)|0;ya=C;X=u|X;w=t;Y=w;a[Y>>0]=X;a[Y+1>>0]=X>>8;a[Y+2>>0]=X>>16;a[Y+3>>0]=X>>24;w=w+4|0;a[w>>0]=ya;a[w+1>>0]=ya>>8;a[w+2>>0]=ya>>16;a[w+3>>0]=ya>>24;c[q>>2]=(c[q>>2]|0)+x;w=sa+(A<<2)|0;c[w>>2]=(c[w>>2]|0)+1;w=0}while(0);while(1){if(w>>>0>=F>>>0){s=Z;break e}t=d[G+w>>0]|0;s=a[va+t>>0]|0;t=e[ua+(t<<1)>>1]|0;u=c[q>>2]|0;v=r+(u>>>3)|0;h=d[v>>0]|0;ya=bc(t|0,0,s&255|0)|0;if(!((ya|0)==0&(C|0)==0)){wa=291;break a}if((s&255)>=57){wa=293;break a}W=cc(t|0,0,u&7|0)|0;Y=C;W=h|W;ya=v;X=ya;a[X>>0]=W;a[X+1>>0]=W>>8;a[X+2>>0]=W>>16;a[X+3>>0]=W>>24;ya=ya+4|0;a[ya>>0]=Y;a[ya+1>>0]=Y>>8;a[ya+2>>0]=Y>>16;a[ya+3>>0]=Y>>24;c[q>>2]=(c[q>>2]|0)+(s&255);w=w+1|0}}else s=Z}while(0);if(!l){wa=320;break}u=l>>>0<98304?l:98304;B=c[q>>2]|0;vb(u,0,q,r);t=r+((c[q>>2]|0)>>>3)|0;Z=d[t>>0]|0;ya=t;a[ya>>0]=Z;a[ya+1>>0]=Z>>8;a[ya+2>>0]=Z>>16;a[ya+3>>0]=Z>>24;t=t+4|0;a[t>>0]=0;a[t+1>>0]=0;a[t+2>>0]=0;a[t+3>>0]=0;c[q>>2]=(c[q>>2]|0)+13;t=wb(b,s,u,va,ua,q,r)|0;xb(sa,m,n,q,r)}switch(wa|0){case 24:{pa(405740,405401,539,405439);break}case 26:{pa(405474,405401,544,405439);break}case 32:{pa(405779,405401,559,405439);break}case 34:{pa(405800,405401,560,405439);break}case 48:{pa(405815,405401,579,405439);break}case 52:{pa(406196,406218,54,406251);break}case 54:{pa(406267,406218,55,406251);break}case 58:{pa(406196,406218,54,406251);break}case 60:{pa(406267,406218,55,406251);break}case 62:{pa(406196,406218,54,406251);break}case 64:{pa(406267,406218,55,406251);break}case 68:{pa(406196,406218,54,406251);break}case 70:{pa(406267,406218,55,406251);break}case 72:{pa(406196,406218,54,406251);break}case 74:{pa(406267,406218,55,406251);break}case 77:{pa(406196,406218,54,406251);break}case 79:{pa(406267,406218,55,406251);break}case 81:{pa(406196,406218,54,406251);break}case 87:{pa(406196,406218,54,406251);break}case 89:{pa(406267,406218,55,406251);break}case 91:{pa(406196,406218,54,406251);break}case 94:{pa(406196,406218,54,406251);break}case 96:{pa(406267,406218,55,406251);break}case 98:{pa(406196,406218,54,406251);break}case 102:{pa(406196,406218,54,406251);break}case 104:{pa(406267,406218,55,406251);break}case 108:{pa(406196,406218,54,406251);break}case 110:{pa(406267,406218,55,406251);break}case 113:{pa(406196,406218,54,406251);break}case 115:{pa(406267,406218,55,406251);break}case 117:{pa(406196,406218,54,406251);break}case 119:{pa(406267,406218,55,406251);break}case 123:{pa(406196,406218,54,406251);break}case 125:{pa(406267,406218,55,406251);break}case 129:{pa(406196,406218,54,406251);break}case 131:{pa(406267,406218,55,406251);break}case 133:{pa(406196,406218,54,406251);break}case 135:{pa(406267,406218,55,406251);break}case 139:{pa(406196,406218,54,406251);break}case 141:{pa(406267,406218,55,406251);break}case 143:{pa(406196,406218,54,406251);break}case 145:{pa(406267,406218,55,406251);break}case 149:{pa(406196,406218,54,406251);break}case 151:{pa(406267,406218,55,406251);break}case 153:{pa(406196,406218,54,406251);break}case 155:{pa(406267,406218,55,406251);break}case 157:{pa(406196,406218,54,406251);break}case 159:{pa(406267,406218,55,406251);break}case 162:{pa(406196,406218,54,406251);break}case 164:{pa(406267,406218,55,406251);break}case 166:{pa(406196,406218,54,406251);break}case 168:{pa(406196,406218,54,406251);break}case 170:{pa(406267,406218,55,406251);break}case 187:{pa(405815,405401,638,405439);break}case 190:{pa(406196,406218,54,406251);break}case 192:{pa(406267,406218,55,406251);break}case 196:{pa(406196,406218,54,406251);break}case 198:{pa(406267,406218,55,406251);break}case 200:{pa(406196,406218,54,406251);break}case 202:{pa(406267,406218,55,406251);break}case 206:{pa(406196,406218,54,406251);break}case 208:{pa(406267,406218,55,406251);break}case 210:{pa(406196,406218,54,406251);break}case 212:{pa(406267,406218,55,406251);break}case 215:{pa(406196,406218,54,406251);break}case 217:{pa(406267,406218,55,406251);break}case 219:{pa(406196,406218,54,406251);break}case 222:{pa(406196,406218,54,406251);break}case 224:{pa(406267,406218,55,406251);break}case 226:{pa(406196,406218,54,406251);break}case 228:{pa(406267,406218,55,406251);break}case 233:{pa(405853,405401,671,405439);break}case 250:{pa(405503,405401,681,405439);break}case 258:{pa(406196,406218,54,406251);break}case 260:{pa(406267,406218,55,406251);break}case 264:{pa(406196,406218,54,406251);break}case 266:{pa(406267,406218,55,406251);break}case 268:{pa(406196,406218,54,406251);break}case 270:{pa(406267,406218,55,406251);break}case 274:{pa(406196,406218,54,406251);break}case 276:{pa(406267,406218,55,406251);break}case 278:{pa(406196,406218,54,406251);break}case 280:{pa(406267,406218,55,406251);break}case 283:{pa(406196,406218,54,406251);break}case 285:{pa(406267,406218,55,406251);break}case 287:{pa(406196,406218,54,406251);break}case 291:{pa(406196,406218,54,406251);break}case 293:{pa(406267,406218,55,406251);break}case 299:{pa(406196,406218,54,406251);break}case 301:{pa(406267,406218,55,406251);break}case 303:{pa(406196,406218,54,406251);break}case 306:{pa(406196,406218,54,406251);break}case 308:{pa(406267,406218,55,406251);break}case 310:{pa(406196,406218,54,406251);break}case 314:{pa(406196,406218,54,406251);break}case 316:{pa(406267,406218,55,406251);break}case 320:if(j){b=c[q>>2]|0;wa=r+(b>>>3)|0;k=d[wa>>0]|0;b=cc(1,0,b&7|0)|0;ya=C;b=k|b;k=wa;a[k>>0]=b;a[k+1>>0]=b>>8;a[k+2>>0]=b>>16;a[k+3>>0]=b>>24;wa=wa+4|0;a[wa>>0]=ya;a[wa+1>>0]=ya>>8;a[wa+2>>0]=ya>>16;a[wa+3>>0]=ya>>24;wa=(c[q>>2]|0)+1|0;c[q>>2]=wa;ya=r+(wa>>>3)|0;k=d[ya>>0]|0;r=cc(1,0,wa&7|0)|0;wa=C;r=k|r;k=ya;a[k>>0]=r;a[k+1>>0]=r>>8;a[k+2>>0]=r>>16;a[k+3>>0]=r>>24;ya=ya+4|0;a[ya>>0]=wa;a[ya+1>>0]=wa>>8;a[ya+2>>0]=wa>>16;a[ya+3>>0]=wa>>24;c[q>>2]=(c[q>>2]|0)+8&-8;i=xa;return}else{a[p>>0]=0;c[o>>2]=0;xb(sa,m,n,o,p);i=xa;return}}}function vb(b,e,f,g){b=b|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0;h=g+((c[f>>2]|0)>>>3)|0;j=d[h>>0]|0;i=h;a[i>>0]=j;a[i+1>>0]=j>>8;a[i+2>>0]=j>>16;a[i+3>>0]=j>>24;h=h+4|0;a[h>>0]=0;a[h+1>>0]=0;a[h+2>>0]=0;a[h+3>>0]=0;h=(c[f>>2]|0)+1|0;c[f>>2]=h;i=g+(h>>>3)|0;j=d[i>>0]|0;if(b>>>0<65537){k=i;h=k;a[h>>0]=j;a[h+1>>0]=j>>8;a[h+2>>0]=j>>16;a[h+3>>0]=j>>24;k=k+4|0;a[k>>0]=0;a[k+1>>0]=0;a[k+2>>0]=0;a[k+3>>0]=0;k=(c[f>>2]|0)+2|0;c[f>>2]=k;h=b+-1|0;i=g+(k>>>3)|0;j=d[i>>0]|0;if(!((h&-65536|0)==0&0==0))pa(406196,406218,54,406251);h=cc(h|0,0,k&7|0)|0;b=C;h=j|h;k=i;j=k;a[j>>0]=h;a[j+1>>0]=h>>8;a[j+2>>0]=h>>16;a[j+3>>0]=h>>24;k=k+4|0;a[k>>0]=b;a[k+1>>0]=b>>8;a[k+2>>0]=b>>16;a[k+3>>0]=b>>24;k=(c[f>>2]|0)+16|0;c[f>>2]=k;e=e&1;b=k>>>3;g=g+b|0;b=a[g>>0]|0;b=b&255;k=k&7;k=cc(e|0,0,k|0)|0;e=C;k=b|k;b=g;a[b>>0]=k;a[b+1>>0]=k>>8;a[b+2>>0]=k>>16;a[b+3>>0]=k>>24;g=g+4|0;a[g>>0]=e;a[g+1>>0]=e>>8;a[g+2>>0]=e>>16;a[g+3>>0]=e>>24;g=c[f>>2]|0;g=g+1|0;c[f>>2]=g;return}else{k=cc(1,0,h&7|0)|0;h=C;j=j|k;k=i;i=k;a[i>>0]=j;a[i+1>>0]=j>>8;a[i+2>>0]=j>>16;a[i+3>>0]=j>>24;k=k+4|0;a[k>>0]=h;a[k+1>>0]=h>>8;a[k+2>>0]=h>>16;a[k+3>>0]=h>>24;k=(c[f>>2]|0)+2|0;c[f>>2]=k;h=b+-1|0;i=g+(k>>>3)|0;j=d[i>>0]|0;if(!((h&-1048576|0)==0&0==0))pa(406196,406218,54,406251);h=cc(h|0,0,k&7|0)|0;b=C;h=j|h;k=i;j=k;a[j>>0]=h;a[j+1>>0]=h>>8;a[j+2>>0]=h>>16;a[j+3>>0]=h>>24;k=k+4|0;a[k>>0]=b;a[k+1>>0]=b>>8;a[k+2>>0]=b>>16;a[k+3>>0]=b>>24;k=(c[f>>2]|0)+20|0;c[f>>2]=k;e=e&1;b=k>>>3;g=g+b|0;b=a[g>>0]|0;b=b&255;k=k&7;k=cc(e|0,0,k|0)|0;e=C;k=b|k;b=g;a[b>>0]=k;a[b+1>>0]=k>>8;a[b+2>>0]=k>>16;a[b+3>>0]=k>>24;g=g+4|0;a[g>>0]=e;a[g+1>>0]=e>>8;a[g+2>>0]=e>>16;a[g+3>>0]=e>>24;g=c[f>>2]|0;g=g+1|0;c[f>>2]=g;return}}function wb(a,b,e,f,g,h,j){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;var k=0,l=0,m=0,n=0,o=0;n=i;i=i+1024|0;m=n;ac(m|0,0,1024)|0;a:do if(e>>>0<32768){k=0;while(1){if((k|0)==(e|0)){k=0;break}l=m+((d[b+k>>0]|0)<<2)|0;c[l>>2]=(c[l>>2]|0)+1;k=k+1|0}while(1){if((k|0)==256){l=e;break a}b=m+(k<<2)|0;o=c[b>>2]|0;l=(o>>>0<11?o:11)<<1;c[b>>2]=o+l;e=e+l|0;k=k+1|0}}else{k=0;while(1){if(k>>>0>=e>>>0)break;o=m+((d[b+k>>0]|0)<<2)|0;c[o>>2]=(c[o>>2]|0)+1;k=k+29|0}e=((e+28|0)>>>0)/29|0;k=0;while(1){if((k|0)==256){l=e;break a}l=m+(k<<2)|0;b=c[l>>2]|0;o=(b>>>0<11?b:11)<<1|1;c[l>>2]=b+o;e=e+o|0;k=k+1|0}}while(0);$a(a,m,l,8,f,g,h,j);e=0;b=0;while(1){if((b|0)==256)break;k=c[m+(b<<2)>>2]|0;if(k)e=e+(_(k,d[f+b>>0]|0)|0)|0;b=b+1|0}i=n;return ((e*125|0)>>>0)/(l>>>0)|0|0}function xb(e,f,g,h,j){e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;s=i;i=i+1872|0;r=s+704|0;q=s;o=s+1736|0;ac(q|0,0,704)|0;Hb(e,64,15,r,f);p=f+64|0;Hb(e+256|0,64,14,r,p);l=q;m=f;n=l+24|0;do{a[l>>0]=a[m>>0]|0;l=l+1|0;m=m+1|0}while((l|0)<(n|0));l=f+40|0;k=l;l=l+4|0;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;e=q+24|0;c[e>>2]=d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24;c[e+4>>2]=l;e=f+24|0;l=e;k=l;l=l+4|0;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;n=q+32|0;c[n>>2]=d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24;c[n+4>>2]=l;n=f+48|0;l=n;n=n+4|0;n=d[n>>0]|d[n+1>>0]<<8|d[n+2>>0]<<16|d[n+3>>0]<<24;k=q+40|0;c[k>>2]=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;c[k+4>>2]=n;k=f+32|0;n=k;l=n;n=n+4|0;n=d[n>>0]|d[n+1>>0]<<8|d[n+2>>0]<<16|d[n+3>>0]<<24;m=q+48|0;c[m>>2]=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;c[m+4>>2]=n;m=f+56|0;n=m;m=m+4|0;m=d[m>>0]|d[m+1>>0]<<8|d[m+2>>0]<<16|d[m+3>>0]<<24;l=q+56|0;c[l>>2]=d[n>>0]|d[n+1>>0]<<8|d[n+2>>0]<<16|d[n+3>>0]<<24;c[l+4>>2]=m;Jb(q,64,o);l=g;m=o;n=l+48|0;do{b[l>>1]=b[m>>1]|0;l=l+2|0;m=m+2|0}while((l|0)<(n|0));l=g+48|0;n=o+64|0;b[l>>1]=b[n>>1]|0;b[l+2>>1]=b[n+2>>1]|0;b[l+4>>1]=b[n+4>>1]|0;b[l+6>>1]=b[n+6>>1]|0;b[l+8>>1]=b[n+8>>1]|0;b[l+10>>1]=b[n+10>>1]|0;b[l+12>>1]=b[n+12>>1]|0;b[l+14>>1]=b[n+14>>1]|0;l=g+64|0;n=o+96|0;b[l>>1]=b[n>>1]|0;b[l+2>>1]=b[n+2>>1]|0;b[l+4>>1]=b[n+4>>1]|0;b[l+6>>1]=b[n+6>>1]|0;b[l+8>>1]=b[n+8>>1]|0;b[l+10>>1]=b[n+10>>1]|0;b[l+12>>1]=b[n+12>>1]|0;b[l+14>>1]=b[n+14>>1]|0;l=g+80|0;n=o+48|0;b[l>>1]=b[n>>1]|0;b[l+2>>1]=b[n+2>>1]|0;b[l+4>>1]=b[n+4>>1]|0;b[l+6>>1]=b[n+6>>1]|0;b[l+8>>1]=b[n+8>>1]|0;b[l+10>>1]=b[n+10>>1]|0;b[l+12>>1]=b[n+12>>1]|0;b[l+14>>1]=b[n+14>>1]|0;l=g+96|0;n=o+80|0;b[l>>1]=b[n>>1]|0;b[l+2>>1]=b[n+2>>1]|0;b[l+4>>1]=b[n+4>>1]|0;b[l+6>>1]=b[n+6>>1]|0;b[l+8>>1]=b[n+8>>1]|0;b[l+10>>1]=b[n+10>>1]|0;b[l+12>>1]=b[n+12>>1]|0;b[l+14>>1]=b[n+14>>1]|0;l=g+112|0;n=o+112|0;b[l>>1]=b[n>>1]|0;b[l+2>>1]=b[n+2>>1]|0;b[l+4>>1]=b[n+4>>1]|0;b[l+6>>1]=b[n+6>>1]|0;b[l+8>>1]=b[n+8>>1]|0;b[l+10>>1]=b[n+10>>1]|0;b[l+12>>1]=b[n+12>>1]|0;b[l+14>>1]=b[n+14>>1]|0;Jb(p,64,g+128|0);l=q;n=l+64|0;do{c[l>>2]=0;l=l+4|0}while((l|0)<(n|0));g=f;o=g;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;n=q;c[n>>2]=d[o>>0]|d[o+1>>0]<<8|d[o+2>>0]<<16|d[o+3>>0]<<24;c[n+4>>2]=g;n=f+8|0;g=n;n=n+4|0;n=d[n>>0]|d[n+1>>0]<<8|d[n+2>>0]<<16|d[n+3>>0]<<24;o=q+64|0;c[o>>2]=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;c[o+4>>2]=n;o=f+16|0;n=o;o=o+4|0;o=d[o>>0]|d[o+1>>0]<<8|d[o+2>>0]<<16|d[o+3>>0]<<24;g=q+128|0;c[g>>2]=d[n>>0]|d[n+1>>0]<<8|d[n+2>>0]<<16|d[n+3>>0]<<24;c[g+4>>2]=o;g=e;e=g;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;o=q+192|0;c[o>>2]=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;c[o+4>>2]=g;o=k;g=o;o=o+4|0;o=d[o>>0]|d[o+1>>0]<<8|d[o+2>>0]<<16|d[o+3>>0]<<24;e=q+384|0;c[e>>2]=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;c[e+4>>2]=o;e=0;while(1){if((e|0)==8)break;o=e<<3;a[q+(o+128)>>0]=a[f+(e+40)>>0]|0;a[q+(o+256)>>0]=a[f+(e+48)>>0]|0;a[q+(o+448)>>0]=a[f+(e+56)>>0]|0;e=e+1|0}_a(q,704,r,h,j);_a(p,64,r,h,j);i=s;return}function yb(f,h,j,k,l,m,n,o,p,q){f=f|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;var r=0.0,s=0,t=0,u=0.0,v=0.0,w=0.0,x=0,y=0,z=0.0,A=0,B=0,D=0,E=0.0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Ua=0,Va=0;Va=i;i=i+4560|0;Ta=Va+2376|0;Ma=Va+128|0;La=Va+4176|0;Ra=Va+4304|0;Qa=Va+3664|0;Na=Va;Ka=Va+3408|0;Oa=Va+1864|0;Sa=Va+840|0;Pa=Va+832|0;Fa=l;Ga=m;Ha=64-((aa(o|0)|0)^31)|0;Ia=(o|0)==0;Ja=o>>>0<2147483649;T=o+-1|0;Ea=(T&o|0)==0;S=bc(16777215,-1,Ha|0)|0;S=(T|0)==(S|0);T=h;U=m;V=l;W=Oa+4|0;X=Oa+8|0;Y=Oa+256|0;Z=Oa+336|0;_=Oa+256|0;$=Na+64|0;ba=Na+24|0;ca=Ma+24|0;da=Ma+32|0;ea=Na+48|0;fa=Ma+40|0;ga=Na+8|0;ha=Ma+48|0;ia=Na+56|0;ja=Ma+56|0;ka=Na+16|0;la=La+48|0;ma=Ka+16|0;na=La+80|0;oa=Ka+32|0;qa=La+112|0;ra=Ka+48|0;sa=Ka+96|0;ta=La+64|0;ua=Ka+112|0;va=La+96|0;wa=Ka+128|0;xa=Ma+64|0;ya=Na+32|0;za=Ma+128|0;Aa=Na+40|0;Ba=Ma+192|0;Ca=Ma+384|0;R=Sa;Da=Sa+1024|0;O=h;a:while(1){if(!j){Ua=155;break}P=j>>>0<131072?j:131072;c[Pa>>2]=Fa;Q=O+P|0;o=O;if(Ia){Ua=4;break}if(!Ja){Ua=6;break}if(!Ea){Ua=8;break}if(!S){Ua=10;break}b:do if(P>>>0>15){A=P+-6|0;N=j+-16|0;N=O+(A>>>0<N>>>0?A:N)|0;A=O+1|0;t=A;s=t;t=t+4|0;s=d[s>>0]|d[s+1>>0]<<8|d[s+2>>0]<<16|d[s+3>>0]<<24;t=d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24;L=-1;x=Ga;while(1){s=lc(s|0,t|0,-1480785920,7733)|0;s=bc(s|0,C|0,Ha|0)|0;J=o;if(J>>>0>=A>>>0){Ua=15;break a}F=0-L|0;K=A;D=32;while(1){G=K;t=D;D=D+1|0;M=K;I=M;M=M+4|0;M=lc(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24|0,d[M>>0]|d[M+1>>0]<<8|d[M+2>>0]<<16|d[M+3>>0]<<24|0,-1480785920,7733)|0;M=bc(M|0,C|0,Ha|0)|0;if((s|0)!=(M|0)){Ua=17;break a}A=K+(t>>>5)|0;if(A>>>0>N>>>0)break b;B=A;t=B;B=B+4|0;B=lc(d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0,d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24|0,-1480785920,7733)|0;B=bc(B|0,C|0,Ha|0)|0;t=K+F|0;if(((d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24|0)==(d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)?(a[K+4>>0]|0)==(a[K+(4-L)>>0]|0):0)?((F|0)<0?(a[K+5>>0]|0)==(a[K+(5-L)>>0]|0):0):0){Ua=22;break}s=n+(s<<2)|0;y=c[s>>2]|0;t=h+y|0;if((y|0)<0){Ua=24;break a}if(t>>>0>=K>>>0){Ua=26;break a}c[s>>2]=G-T;if((d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24|0)!=(d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)){s=B;K=A;continue}if((a[K+4>>0]|0)!=(a[h+(y+4)>>0]|0)){s=B;K=A;continue}if((a[K+5>>0]|0)==(a[h+(y+5)>>0]|0))break;else{s=B;K=A}}if((Ua|0)==22){Ua=0;c[n+(s<<2)>>2]=G-T}A=t;M=O+(P+-4)|0;s=0;y=K+6|0;while(1){if(y>>>0>M>>>0){I=s;s=y;break}I=t+(s+6)|0;if((d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24|0)!=(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24|0)){I=s;s=y;break}s=s+4|0;y=y+4|0}while(1){if(s>>>0>=Q>>>0)break;if((a[t+(I+6)>>0]|0)!=(a[s>>0]|0))break;I=I+1|0;s=s+1|0}H=I+6|0;y=G-A|0;F=G-o|0;G=K+H|0;o=G;c:do if(H){B=H;D=K;while(1){s=a[D>>0]|0;A=a[t>>0]|0;if(s<<24>>24!=A<<24>>24)break;B=B+-1|0;if(!B)break c;else{D=D+1|0;t=t+1|0}}if(s<<24>>24!=A<<24>>24){Ua=41;break a}}while(0);do if(F>>>0>=6){if(F>>>0<130){A=F+-2|0;D=((aa(A|0)|0)^31)+-1|0;B=A>>>D;c[c[Pa>>2]>>2]=(D<<1)+B+2|A-(B<<D)<<8;break}if(F>>>0<2114){B=F+-66|0;D=(aa(B|0)|0)^31;c[c[Pa>>2]>>2]=D+10|B-(1<<D)<<8;break}if(F>>>0<6210){c[c[Pa>>2]>>2]=(F<<8)+-541184|21;break}s=F<<8;if(F>>>0<22594){c[c[Pa>>2]>>2]=s+-1589760|22;break}else{c[c[Pa>>2]>>2]=s+-5784064|23;break}}else c[c[Pa>>2]>>2]=F;while(0);c[Pa>>2]=(c[Pa>>2]|0)+4;dc(x|0,J|0,F|0)|0;x=x+F|0;if((y|0)==(L|0)){c[c[Pa>>2]>>2]=64;s=c[Pa>>2]|0;y=L}else{J=y+3|0;s=((aa(J|0)|0)^31)+-1|0;L=J>>>s&1;c[c[Pa>>2]>>2]=((s<<1)+-2|L)+80|J-((L|2)<<s)<<8;s=c[Pa>>2]|0}s=s+4|0;c[Pa>>2]=s;do if(H>>>0>=12){if(H>>>0<72){H=I+-2|0;L=((aa(H|0)|0)^31)+-1|0;J=H>>>L;c[s>>2]=(L<<1)+J+28|H-(J<<L)<<8;c[Pa>>2]=(c[Pa>>2]|0)+4;break}if(H>>>0<136){L=I+-2|0;c[s>>2]=(L>>>5)+54|L<<8&7936;L=(c[Pa>>2]|0)+4|0;c[Pa>>2]=L;c[L>>2]=64;c[Pa>>2]=(c[Pa>>2]|0)+4;break}if(H>>>0<2120){J=I+-66|0;L=(aa(J|0)|0)^31;c[s>>2]=L+52|J-(1<<L)<<8;L=(c[Pa>>2]|0)+4|0;c[Pa>>2]=L;c[L>>2]=64;c[Pa>>2]=(c[Pa>>2]|0)+4;break}else{c[s>>2]=(H<<8)+-542720|63;L=(c[Pa>>2]|0)+4|0;c[Pa>>2]=L;c[L>>2]=64;c[Pa>>2]=(c[Pa>>2]|0)+4;break}}else{c[s>>2]=I+26;c[Pa>>2]=(c[Pa>>2]|0)+4}while(0);if(G>>>0>=N>>>0)break b;t=K+(I+1)|0;L=t;L=d[L>>0]|d[L+1>>0]<<8|d[L+2>>0]<<16|d[L+3>>0]<<24;t=t+4|0;t=d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24;H=lc(L|0,t|0,-1480785920,7733)|0;H=bc(H|0,C|0,Ha|0)|0;J=G-T|0;c[n+(H<<2)>>2]=J+-5;H=bc(L|0,t|0,8)|0;H=lc(H|0,C|0,-1480785920,7733)|0;H=bc(H|0,C|0,Ha|0)|0;c[n+(H<<2)>>2]=J+-4;t=bc(L|0,t|0,16)|0;t=lc(t|0,C|0,-1480785920,7733)|0;t=bc(t|0,C|0,Ha|0)|0;c[n+(t<<2)>>2]=J+-3;t=K+(I+4)|0;K=t;K=d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24;t=t+4|0;t=d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24;L=bc(K|0,t|0,16)|0;L=lc(L|0,C|0,-1480785920,7733)|0;L=bc(L|0,C|0,Ha|0)|0;I=lc(K|0,t|0,-1480785920,7733)|0;I=bc(I|0,C|0,Ha|0)|0;c[n+(I<<2)>>2]=J+-2;t=bc(K|0,t|0,8)|0;t=lc(t|0,C|0,-1480785920,7733)|0;t=bc(t|0,C|0,Ha|0)|0;c[n+(t<<2)>>2]=J+-1;L=n+(L<<2)|0;t=c[L>>2]|0;c[L>>2]=J;while(1){B=h+t|0;L=o;s=o;if((d[L>>0]|d[L+1>>0]<<8|d[L+2>>0]<<16|d[L+3>>0]<<24|0)!=(d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24|0))break;if((a[s+4>>0]|0)!=(a[h+(t+4)>>0]|0))break;if((a[s+5>>0]|0)!=(a[h+(t+5)>>0]|0))break;A=t+6|0;t=0;y=s+6|0;while(1){if(y>>>0>M>>>0)break;L=h+(A+t)|0;if((d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24|0)!=(d[L>>0]|d[L+1>>0]<<8|d[L+2>>0]<<16|d[L+3>>0]<<24|0))break;t=t+4|0;y=y+4|0}while(1){if(y>>>0>=Q>>>0)break;if((a[h+(A+t)>>0]|0)!=(a[y>>0]|0))break;t=t+1|0;y=y+1|0}G=t+6|0;H=s+G|0;I=H;y=o-B|0;do if(G){D=G;F=s;while(1){o=a[F>>0]|0;A=a[B>>0]|0;if(o<<24>>24!=A<<24>>24){Ua=80;break}D=D+-1|0;if(!D)break;else{F=F+1|0;B=B+1|0}}if((Ua|0)==80?(Ua=0,o<<24>>24!=A<<24>>24):0){Ua=81;break a}if(G>>>0<10){Ua=83;break}if(G>>>0<134){L=((aa(t|0)|0)^31)+-1|0;K=t>>>L;c[c[Pa>>2]>>2]=(L<<1)+K+44|t-(K<<L)<<8;break}if(G>>>0<2118){K=t+-64|0;L=(aa(K|0)|0)^31;c[c[Pa>>2]>>2]=L+52|K-(1<<L)<<8;break}else{c[c[Pa>>2]>>2]=(G<<8)+-542208|63;break}}else Ua=83;while(0);if((Ua|0)==83){Ua=0;c[c[Pa>>2]>>2]=t+44}L=(c[Pa>>2]|0)+4|0;c[Pa>>2]=L;G=y+3|0;K=((aa(G|0)|0)^31)+-1|0;J=G>>>K&1;c[L>>2]=((K<<1)+-2|J)+80|G-((J|2)<<K)<<8;c[Pa>>2]=(c[Pa>>2]|0)+4;if(H>>>0>=N>>>0){o=I;break b}K=s+(t+1)|0;o=K;o=d[o>>0]|d[o+1>>0]<<8|d[o+2>>0]<<16|d[o+3>>0]<<24;K=K+4|0;K=d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24;J=lc(o|0,K|0,-1480785920,7733)|0;J=bc(J|0,C|0,Ha|0)|0;L=H-T|0;c[n+(J<<2)>>2]=L+-5;J=bc(o|0,K|0,8)|0;J=lc(J|0,C|0,-1480785920,7733)|0;J=bc(J|0,C|0,Ha|0)|0;c[n+(J<<2)>>2]=L+-4;K=bc(o|0,K|0,16)|0;K=lc(K|0,C|0,-1480785920,7733)|0;K=bc(K|0,C|0,Ha|0)|0;c[n+(K<<2)>>2]=L+-3;t=s+(t+4)|0;K=t;K=d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24;t=t+4|0;t=d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24;o=bc(K|0,t|0,16)|0;o=lc(o|0,C|0,-1480785920,7733)|0;o=bc(o|0,C|0,Ha|0)|0;J=lc(K|0,t|0,-1480785920,7733)|0;J=bc(J|0,C|0,Ha|0)|0;c[n+(J<<2)>>2]=L+-2;t=bc(K|0,t|0,8)|0;t=lc(t|0,C|0,-1480785920,7733)|0;t=bc(t|0,C|0,Ha|0)|0;c[n+(t<<2)>>2]=L+-1;o=n+(o<<2)|0;t=c[o>>2]|0;c[o>>2]=L;o=I}A=s+1|0;t=A;s=t;t=t+4|0;s=d[s>>0]|d[s+1>>0]<<8|d[s+2>>0]<<16|d[s+3>>0]<<24;t=d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24;L=y}}else x=Ga;while(0);t=o;if(t>>>0>Q>>>0){Ua=93;break}if(t>>>0<Q>>>0){o=Q-o|0;do if(o>>>0>=6){if(o>>>0<130){L=o+-2|0;N=((aa(L|0)|0)^31)+-1|0;M=L>>>N;c[c[Pa>>2]>>2]=(N<<1)+M+2|L-(M<<N)<<8;break}if(o>>>0<2114){M=o+-66|0;N=(aa(M|0)|0)^31;c[c[Pa>>2]>>2]=N+10|M-(1<<N)<<8;break}if(o>>>0<6210){c[c[Pa>>2]>>2]=(o<<8)+-541184|21;break}s=o<<8;if(o>>>0<22594){c[c[Pa>>2]>>2]=s+-1589760|22;break}else{c[c[Pa>>2]>>2]=s+-5784064|23;break}}else c[c[Pa>>2]>>2]=o;while(0);c[Pa>>2]=(c[Pa>>2]|0)+4;dc(x|0,t|0,o|0)|0;x=x+o|0}A=x-U|0;r=+(P>>>0);if(!(+(A>>>0)<r*.98)){ac(Sa|0,0,1024)|0;E=r*8.0*.98;o=0;while(1){if(o>>>0>=P>>>0){o=R;r=0.0;y=0;break}N=Sa+(d[O+o>>0]<<2)|0;c[N>>2]=(c[N>>2]|0)+1;o=o+43|0}while(1){if(o>>>0>=Da>>>0)break;s=c[o>>2]|0;u=+(s>>>0);if(s>>>0<256)z=+g[19516+(s<<2)>>2];else z=+Xb(u);t=c[o+4>>2]|0;v=+(t>>>0);if(t>>>0<256)w=+g[19516+(t<<2)>>2];else w=+Xb(v);o=o+8|0;r=r-u*z-v*w;y=y+s+t|0}z=E/43.0;v=+(y>>>0);if(!y)w=v;else{if(y>>>0<256)u=+g[19516+(y<<2)>>2];else u=+Xb(v);w=v;r=r+v*u}if(!((r<w?w:r)<z)){zb(P,1,p,q);N=(c[p>>2]|0)+7|0;c[p>>2]=N&-8;dc(q+(N>>>3)|0,O|0,P|0)|0;O=(c[p>>2]|0)+(P<<3)|0;c[p>>2]=O;a[q+(O>>>3)>>0]=0}else Ua=126}else Ua=126;if((Ua|0)==126){Ua=0;G=(c[Pa>>2]|0)-V>>2;zb(P,0,p,q);s=q+((c[p>>2]|0)>>>3)|0;O=d[s>>0]|0;t=s;a[t>>0]=O;a[t+1>>0]=O>>8;a[t+2>>0]=O>>16;a[t+3>>0]=O>>24;s=s+4|0;a[s>>0]=0;a[s+1>>0]=0;a[s+2>>0]=0;a[s+3>>0]=0;c[p>>2]=(c[p>>2]|0)+13;ac(Sa|0,0,1024)|0;s=Na;t=s+128|0;do{c[s>>2]=0;s=s+4|0}while((s|0)<(t|0));ac(Ka|0,0,256)|0;ac(Oa|0,0,512)|0;o=x-U|0;s=0;while(1){if((s|0)==(o|0))break;O=Sa+(d[m+s>>0]<<2)|0;c[O>>2]=(c[O>>2]|0)+1;s=s+1|0}$a(f,Sa,A,8,Ra,Qa,p,q);o=0;while(1){if((o|0)==(G|0))break;O=Oa+((c[l+(o<<2)>>2]&255)<<2)|0;c[O>>2]=(c[O>>2]|0)+1;o=o+1|0}c[W>>2]=(c[W>>2]|0)+1;c[X>>2]=(c[X>>2]|0)+1;c[Y>>2]=(c[Y>>2]|0)+1;c[Z>>2]=(c[Z>>2]|0)+1;ac(Ma|0,0,704)|0;Hb(Oa,64,15,Ta,Na);Hb(_,64,14,Ta,$);c[Ma>>2]=c[ba>>2];c[Ma+4>>2]=c[ba+4>>2];c[Ma+8>>2]=c[ba+8>>2];c[Ma+12>>2]=c[ba+12>>2];c[Ma+16>>2]=c[ba+16>>2];c[Ma+20>>2]=c[ba+20>>2];t=Na;o=c[t+4>>2]|0;s=ca;c[s>>2]=c[t>>2];c[s+4>>2]=o;s=ea;o=c[s+4>>2]|0;t=da;c[t>>2]=c[s>>2];c[t+4>>2]=o;t=ga;o=c[t+4>>2]|0;s=fa;c[s>>2]=c[t>>2];c[s+4>>2]=o;s=ia;o=c[s+4>>2]|0;t=ha;c[t>>2]=c[s>>2];c[t+4>>2]=o;t=ka;o=c[t+4>>2]|0;s=ja;c[s>>2]=c[t>>2];c[s+4>>2]=o;Jb(Ma,64,La);b[Ka>>1]=b[la>>1]|0;b[Ka+2>>1]=b[la+2>>1]|0;b[Ka+4>>1]=b[la+4>>1]|0;b[Ka+6>>1]=b[la+6>>1]|0;b[Ka+8>>1]=b[la+8>>1]|0;b[Ka+10>>1]=b[la+10>>1]|0;b[Ka+12>>1]=b[la+12>>1]|0;b[Ka+14>>1]=b[la+14>>1]|0;b[ma>>1]=b[na>>1]|0;b[ma+2>>1]=b[na+2>>1]|0;b[ma+4>>1]=b[na+4>>1]|0;b[ma+6>>1]=b[na+6>>1]|0;b[ma+8>>1]=b[na+8>>1]|0;b[ma+10>>1]=b[na+10>>1]|0;b[ma+12>>1]=b[na+12>>1]|0;b[ma+14>>1]=b[na+14>>1]|0;b[oa>>1]=b[qa>>1]|0;b[oa+2>>1]=b[qa+2>>1]|0;b[oa+4>>1]=b[qa+4>>1]|0;b[oa+6>>1]=b[qa+6>>1]|0;b[oa+8>>1]=b[qa+8>>1]|0;b[oa+10>>1]=b[qa+10>>1]|0;b[oa+12>>1]=b[qa+12>>1]|0;b[oa+14>>1]=b[qa+14>>1]|0;s=ra;o=La;t=s+48|0;do{b[s>>1]=b[o>>1]|0;s=s+2|0;o=o+2|0}while((s|0)<(t|0));b[sa>>1]=b[ta>>1]|0;b[sa+2>>1]=b[ta+2>>1]|0;b[sa+4>>1]=b[ta+4>>1]|0;b[sa+6>>1]=b[ta+6>>1]|0;b[sa+8>>1]=b[ta+8>>1]|0;b[sa+10>>1]=b[ta+10>>1]|0;b[sa+12>>1]=b[ta+12>>1]|0;b[sa+14>>1]=b[ta+14>>1]|0;b[ua>>1]=b[va>>1]|0;b[ua+2>>1]=b[va+2>>1]|0;b[ua+4>>1]=b[va+4>>1]|0;b[ua+6>>1]=b[va+6>>1]|0;b[ua+8>>1]=b[va+8>>1]|0;b[ua+10>>1]=b[va+10>>1]|0;b[ua+12>>1]=b[va+12>>1]|0;b[ua+14>>1]=b[va+14>>1]|0;Jb($,64,wa);s=Ma;t=s+64|0;do{c[s>>2]=0;s=s+4|0}while((s|0)<(t|0));N=ba;O=c[N+4>>2]|0;o=Ma;c[o>>2]=c[N>>2];c[o+4>>2]=O;o=ya;O=c[o+4>>2]|0;N=xa;c[N>>2]=c[o>>2];c[N+4>>2]=O;N=Aa;O=c[N+4>>2]|0;o=za;c[o>>2]=c[N>>2];c[o+4>>2]=O;o=ea;O=c[o+4>>2]|0;N=Ba;c[N>>2]=c[o>>2];c[N+4>>2]=O;N=ia;O=c[N+4>>2]|0;o=Ca;c[o>>2]=c[N>>2];c[o+4>>2]=O;o=0;while(1){if((o|0)==8)break;O=o<<3;a[Ma+(O+128)>>0]=a[Na+o>>0]|0;a[Ma+(O+256)>>0]=a[Na+(o+8)>>0]|0;a[Ma+(O+448)>>0]=a[Na+(o+16)>>0]|0;o=o+1|0}_a(Ma,704,Ta,p,q);_a($,64,Ta,p,q);o=Ga;F=0;while(1){if(F>>>0>=G>>>0)break;D=c[l+(F<<2)>>2]|0;B=D&255;D=D>>>8;s=a[Na+B>>0]|0;t=e[Ka+(B<<1)>>1]|0;x=c[p>>2]|0;y=q+(x>>>3)|0;A=d[y>>0]|0;O=bc(t|0,0,s&255|0)|0;if(!((O|0)==0&(C|0)==0)){Ua=138;break a}if((s&255)>=57){Ua=140;break a}O=cc(t|0,0,x&7|0)|0;x=C;O=A|O;t=y;y=t;a[y>>0]=O;a[y+1>>0]=O>>8;a[y+2>>0]=O>>16;a[y+3>>0]=O>>24;t=t+4|0;a[t>>0]=x;a[t+1>>0]=x>>8;a[t+2>>0]=x>>16;a[t+3>>0]=x>>24;s=(c[p>>2]|0)+(s&255)|0;c[p>>2]=s;t=c[18276+(B<<2)>>2]|0;x=q+(s>>>3)|0;y=d[x>>0]|0;O=bc(D|0,0,t|0)|0;if(!((O|0)==0&(C|0)==0)){Ua=142;break a}M=cc(D|0,0,s&7|0)|0;O=C;M=y|M;s=x;N=s;a[N>>0]=M;a[N+1>>0]=M>>8;a[N+2>>0]=M>>16;a[N+3>>0]=M>>24;s=s+4|0;a[s>>0]=O;a[s+1>>0]=O>>8;a[s+2>>0]=O>>16;a[s+3>>0]=O>>24;s=(c[p>>2]|0)+t|0;c[p>>2]=s;d:do if(B>>>0<24){D=(c[18788+(B<<2)>>2]|0)+D|0;B=0;while(1){if(B>>>0>=D>>>0)break d;x=d[o>>0]|0;t=a[Ra+x>>0]|0;x=e[Qa+(x<<1)>>1]|0;y=q+(s>>>3)|0;A=d[y>>0]|0;O=bc(x|0,0,t&255|0)|0;if(!((O|0)==0&(C|0)==0)){Ua=147;break a}if((t&255)>=57){Ua=149;break a}L=cc(x|0,0,s&7|0)|0;N=C;L=A|L;O=y;M=O;a[M>>0]=L;a[M+1>>0]=L>>8;a[M+2>>0]=L>>16;a[M+3>>0]=L>>24;O=O+4|0;a[O>>0]=N;a[O+1>>0]=N>>8;a[O+2>>0]=N>>16;a[O+3>>0]=N>>24;O=(c[p>>2]|0)+(t&255)|0;c[p>>2]=O;o=o+1|0;s=O;B=B+1|0}}while(0);F=F+1|0}}j=j-P|0;O=Q}switch(Ua|0){case 4:{pa(405532,405543,251,405590);break}case 6:{pa(405605,405543,252,405590);break}case 8:{pa(405630,405543,254,405590);break}case 10:{pa(405667,405543,256,405590);break}case 15:{pa(405740,405543,289,405590);break}case 17:{pa(405755,405543,295,405590);break}case 24:{pa(405779,405543,309,405590);break}case 26:{pa(405800,405543,310,405590);break}case 41:{pa(405815,405543,329,405590);break}case 81:{pa(405815,405543,378,405590);break}case 93:{pa(405853,405543,415,405590);break}case 138:{pa(406196,406218,54,406251);break}case 140:{pa(406267,406218,55,406251);break}case 142:{pa(406196,406218,54,406251);break}case 147:{pa(406196,406218,54,406251);break}case 149:{pa(406267,406218,55,406251);break}case 155:{if(!k){i=Va;return}f=c[p>>2]|0;Ta=q+(f>>>3)|0;n=d[Ta>>0]|0;f=cc(1,0,f&7|0)|0;Ua=C;f=n|f;n=Ta;a[n>>0]=f;a[n+1>>0]=f>>8;a[n+2>>0]=f>>16;a[n+3>>0]=f>>24;Ta=Ta+4|0;a[Ta>>0]=Ua;a[Ta+1>>0]=Ua>>8;a[Ta+2>>0]=Ua>>16;a[Ta+3>>0]=Ua>>24;Ta=(c[p>>2]|0)+1|0;c[p>>2]=Ta;Ua=q+(Ta>>>3)|0;n=d[Ua>>0]|0;q=cc(1,0,Ta&7|0)|0;Ta=C;q=n|q;n=Ua;a[n>>0]=q;a[n+1>>0]=q>>8;a[n+2>>0]=q>>16;a[n+3>>0]=q>>24;Ua=Ua+4|0;a[Ua>>0]=Ta;a[Ua+1>>0]=Ta>>8;a[Ua+2>>0]=Ta>>16;a[Ua+3>>0]=Ta>>24;c[p>>2]=(c[p>>2]|0)+8&-8;i=Va;return}}}function zb(b,e,f,g){b=b|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0;h=g+((c[f>>2]|0)>>>3)|0;j=d[h>>0]|0;i=h;a[i>>0]=j;a[i+1>>0]=j>>8;a[i+2>>0]=j>>16;a[i+3>>0]=j>>24;h=h+4|0;a[h>>0]=0;a[h+1>>0]=0;a[h+2>>0]=0;a[h+3>>0]=0;h=(c[f>>2]|0)+1|0;c[f>>2]=h;i=g+(h>>>3)|0;j=d[i>>0]|0;if(b>>>0<65537){k=i;h=k;a[h>>0]=j;a[h+1>>0]=j>>8;a[h+2>>0]=j>>16;a[h+3>>0]=j>>24;k=k+4|0;a[k>>0]=0;a[k+1>>0]=0;a[k+2>>0]=0;a[k+3>>0]=0;k=(c[f>>2]|0)+2|0;c[f>>2]=k;h=b+-1|0;i=g+(k>>>3)|0;j=d[i>>0]|0;if(!((h&-65536|0)==0&0==0))pa(406196,406218,54,406251);h=cc(h|0,0,k&7|0)|0;b=C;h=j|h;k=i;j=k;a[j>>0]=h;a[j+1>>0]=h>>8;a[j+2>>0]=h>>16;a[j+3>>0]=h>>24;k=k+4|0;a[k>>0]=b;a[k+1>>0]=b>>8;a[k+2>>0]=b>>16;a[k+3>>0]=b>>24;k=(c[f>>2]|0)+16|0;c[f>>2]=k;e=e&1;b=k>>>3;g=g+b|0;b=a[g>>0]|0;b=b&255;k=k&7;k=cc(e|0,0,k|0)|0;e=C;k=b|k;b=g;a[b>>0]=k;a[b+1>>0]=k>>8;a[b+2>>0]=k>>16;a[b+3>>0]=k>>24;g=g+4|0;a[g>>0]=e;a[g+1>>0]=e>>8;a[g+2>>0]=e>>16;a[g+3>>0]=e>>24;g=c[f>>2]|0;g=g+1|0;c[f>>2]=g;return}else{k=cc(1,0,h&7|0)|0;h=C;j=j|k;k=i;i=k;a[i>>0]=j;a[i+1>>0]=j>>8;a[i+2>>0]=j>>16;a[i+3>>0]=j>>24;k=k+4|0;a[k>>0]=h;a[k+1>>0]=h>>8;a[k+2>>0]=h>>16;a[k+3>>0]=h>>24;k=(c[f>>2]|0)+2|0;c[f>>2]=k;h=b+-1|0;i=g+(k>>>3)|0;j=d[i>>0]|0;if(!((h&-1048576|0)==0&0==0))pa(406196,406218,54,406251);h=cc(h|0,0,k&7|0)|0;b=C;h=j|h;k=i;j=k;a[j>>0]=h;a[j+1>>0]=h>>8;a[j+2>>0]=h>>16;a[j+3>>0]=h>>24;k=k+4|0;a[k>>0]=b;a[k+1>>0]=b>>8;a[k+2>>0]=b>>16;a[k+3>>0]=b>>24;k=(c[f>>2]|0)+20|0;c[f>>2]=k;e=e&1;b=k>>>3;g=g+b|0;b=a[g>>0]|0;b=b&255;k=k&7;k=cc(e|0,0,k|0)|0;e=C;k=b|k;b=g;a[b>>0]=k;a[b+1>>0]=k>>8;a[b+2>>0]=k>>16;a[b+3>>0]=k>>24;g=g+4|0;a[g>>0]=e;a[g+1>>0]=e>>8;a[g+2>>0]=e>>16;a[g+3>>0]=e>>24;g=c[f>>2]|0;g=g+1|0;c[f>>2]=g;return}}function Ab(e,f,g,h,j,k){e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,qa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ua=0,Va=0,Wa=0,Xa=0,Ya=0,Za=0,_a=0,$a=0,ab=0,bb=0,cb=0,db=0,eb=0,fb=0;fb=i;i=i+16|0;db=fb+8|0;eb=fb+4|0;$a=fb;if(!(Bb(e)|0)){k=0;i=fb;return k|0}bb=e+5232|0;if((c[bb>>2]|0)!=0?(c[f>>2]|0)!=0:0){k=0;i=fb;return k|0}cb=e+4|0;l=c[cb>>2]|0;if(l>>>0<2){M=1<<c[e+8>>2];o=c[f>>2]|0;o=o>>>0<M>>>0?o:M;o=o>>>0>131072?131072:o;N=e+16|0;a:do switch(l|0){case 0:{p=0;n=0;L=0;K=0;ab=20;break}case 1:{n=e+5208|0;ab=c[n>>2]|0;l=ab;b:do if(!ab){do if((o|0)==131072){l=e+24|0;m=sa[c[N>>2]&1](c[l>>2]|0,524288)|0;if(!m)oa(1);c[n>>2]=m;l=sa[c[N>>2]&1](c[l>>2]|0,131072)|0;if(l){c[e+5212>>2]=l;l=c[n>>2]|0;if(!l)break;else{p=l;break b}}else oa(1)}while(0);l=e+24|0;m=sa[c[N>>2]&1](c[l>>2]|0,o<<2)|0;if(!m)oa(1);l=sa[c[N>>2]&1](c[l>>2]|0,o)|0;if(!l)oa(1);else{p=m;n=l;L=m;K=l;ab=20;break a}}else p=l;while(0);n=c[e+5212>>2]|0;L=0;K=0;ab=20;break}default:O=0}while(0);do if((ab|0)==20){v=e+193|0;w=e+5220|0;x=e+196|0;y=e+200|0;z=e+20|0;A=e+24|0;B=e+192|0;D=e+4308|0;E=e+4436|0;F=e+5204|0;G=e+4692|0;H=e+5216|0;I=e+5224|0;J=(k|0)==0;c:while(1){l=c[bb>>2]|0;while(1){if((l|0)==1?(a[v>>0]|0)!=0:0){ab=24;break}m=c[w>>2]|0;if(m)break;if(l){ab=49;break c}u=c[f>>2]|0;s=M>>>0<u>>>0?M:u;u=(u|0)==(s|0)?1:0;l=(s<<1)+502|0;c[db>>2]=d[v>>0];if(l>>>0>(c[h>>2]|0)>>>0){m=c[y>>2]|0;if((c[x>>2]|0)>>>0<l>>>0){ra[c[z>>2]&1](c[A>>2]|0,m);c[y>>2]=0;m=sa[c[N>>2]&1](c[A>>2]|0,l)|0;if(!m){ab=34;break c}c[y>>2]=m;c[x>>2]=l;t=0}else t=0}else{t=1;m=c[j>>2]|0}a[m>>0]=a[B>>0]|0;l=Cb(e,c[cb>>2]|0,s,eb)|0;o=c[g>>2]|0;q=u<<24>>24!=0;r=c[eb>>2]|0;if(!(c[cb>>2]|0))ub(N,o,s,q,l,r,D,E,F,G,db,m);else yb(N,o,s,q,p,n,l,r,db,m);c[g>>2]=(c[g>>2]|0)+s;c[f>>2]=(c[f>>2]|0)-s;o=c[db>>2]|0;q=o>>>3;if(t<<24>>24){l=c[h>>2]|0;if(q>>>0>l>>>0){ab=41;break c}if(!((o&7|0)==0|q>>>0<l>>>0)){ab=43;break c}c[j>>2]=(c[j>>2]|0)+q;c[h>>2]=(c[h>>2]|0)-q;l=(c[I>>2]|0)+q|0;c[I>>2]=l;if(!J)c[k>>2]=l}else{c[H>>2]=m;c[w>>2]=q}a[B>>0]=a[m+((c[db>>2]|0)>>>3)>>0]|0;a[v>>0]=c[db>>2]&7;if(!(u<<24>>24))continue c;c[bb>>2]=2;l=2}if((ab|0)==24){ab=0;Eb(e);continue}l=c[h>>2]|0;if(!l){ab=49;break}l=m>>>0<l>>>0?m:l;dc(c[j>>2]|0,c[H>>2]|0,l|0)|0;c[j>>2]=(c[j>>2]|0)+l;c[h>>2]=(c[h>>2]|0)-l;c[H>>2]=(c[H>>2]|0)+l;c[w>>2]=(c[w>>2]|0)-l;l=(c[I>>2]|0)+l|0;c[I>>2]=l;if(J)continue;c[k>>2]=l}if((ab|0)==34)oa(1);else if((ab|0)==41)pa(406280,406053,1436,406308);else if((ab|0)==43)pa(406340,406053,1437,406308);else if((ab|0)==49){ra[c[z>>2]&1](c[A>>2]|0,L);ra[c[z>>2]&1](c[A>>2]|0,K);if((c[bb>>2]|0)!=1){O=1;break}if(c[w>>2]|0){O=1;break}c[bb>>2]=0;c[H>>2]=0;O=1;break}}while(0);k=O;i=fb;return k|0}Xa=e+80|0;Ya=e+152|0;x=e+12|0;y=e+193|0;z=e+5220|0;A=e+5216|0;B=e+16|0;D=e+116|0;E=e+92|0;F=e+5236|0;G=e+5208|0;H=e+24|0;I=e+5212|0;J=e+196|0;K=e+200|0;L=e+20|0;M=e+24|0;N=e+192|0;O=e+4308|0;P=e+4436|0;Q=e+5204|0;R=e+4692|0;S=e+128|0;T=e+120|0;U=e+124|0;V=e+20|0;W=e+160|0;X=e+136|0;Y=e+132|0;Z=e+4|0;_=e+72|0;$=e+8|0;ba=e+144|0;ca=e+28|0;da=e+32|0;ea=e+36|0;fa=e+40|0;ga=e+44|0;ha=e+48|0;ia=e+52|0;ja=e+56|0;ka=e+60|0;la=e+64|0;ma=e+68|0;na=e+8|0;qa=e+194|0;ta=e+195|0;ua=e+176|0;va=e+176|0;wa=e+160|0;xa=e+8|0;ya=e+5224|0;za=(k|0)==0;Aa=e+108|0;Ba=e+96|0;Ca=e+112|0;Da=e+104|0;Ea=e+116|0;Fa=e+116|0;Ga=e+92|0;Ua=e+104|0;Va=e+100|0;Wa=e+88|0;d:while(1){m=Xa;l=Ya;l=$b(c[m>>2]|0,c[m+4>>2]|0,c[l>>2]|0,c[l+4>>2]|0)|0;m=C;if(Bb(e)|0)n=1<<c[x>>2];else n=0;if((m>>>0<0|(m|0)==0&l>>>0<n>>>0?(Za=n-l|0,(Za|0)!=0):0)?(_a=c[f>>2]|0,(_a|0)!=0):0){p=Za>>>0<_a>>>0?Za:_a;o=c[g>>2]|0;if(Bb(e)|0){l=c[Aa>>2]|0;if((l|0)==0?p>>>0<(c[Ba>>2]|0)>>>0:0){c[Aa>>2]=p;l=sa[c[B>>2]&1](c[H>>2]|0,p+9|0)|0;if(!l){ab=62;break}m=c[Ca>>2]|0;if(m){dc(l|0,m|0,(c[Da>>2]|0)+9|0)|0;ra[c[V>>2]&1](c[H>>2]|0,c[Ca>>2]|0);c[Ca>>2]=0}c[Ca>>2]=l;c[Da>>2]=p;c[Ea>>2]=l+2;a[l+1>>0]=0;a[(c[Ea>>2]|0)+-2>>0]=0;l=0;while(1){if((l|0)==7)break;a[(c[Ea>>2]|0)+((c[Da>>2]|0)+l)>>0]=0;l=l+1|0}dc(c[Fa>>2]|0,o|0,p|0)|0}else{n=c[Va>>2]|0;if((c[Ua>>2]|0)>>>0<n>>>0){l=sa[c[B>>2]&1](c[H>>2]|0,n+9|0)|0;if(!l){ab=71;break}m=c[Ca>>2]|0;if(m){dc(l|0,m|0,(c[Da>>2]|0)+9|0)|0;ra[c[V>>2]&1](c[H>>2]|0,c[Ca>>2]|0);c[Ca>>2]=0}c[Ca>>2]=l;c[Da>>2]=n;c[Ea>>2]=l+2;a[l+1>>0]=0;a[(c[Ea>>2]|0)+-2>>0]=0;l=0;while(1){if((l|0)==7)break;a[(c[Ea>>2]|0)+((c[Da>>2]|0)+l)>>0]=0;l=l+1|0}a[(c[Fa>>2]|0)+((c[Wa>>2]|0)+-2)>>0]=0;a[(c[Fa>>2]|0)+((c[Wa>>2]|0)+-1)>>0]=0;l=c[Aa>>2]|0}n=l&c[Ga>>2];l=c[Ba>>2]|0;if(l>>>0>n>>>0){ab=l-n|0;dc((c[Fa>>2]|0)+((c[Wa>>2]|0)+n)|0,o|0,(p>>>0<ab>>>0?p:ab)|0)|0}m=(c[Fa>>2]|0)+n|0;l=o;if((n+p|0)>>>0>(c[Wa>>2]|0)>>>0){ab=(c[Va>>2]|0)-n|0;dc(m|0,l|0,(p>>>0<ab>>>0?p:ab)|0)|0;ab=c[Wa>>2]|0;dc(c[Fa>>2]|0,l+(ab-n)|0,p+(n-ab)|0)|0}else dc(m|0,l|0,p|0)|0;l=c[Fa>>2]|0;a[l+-2>>0]=a[l+((c[Wa>>2]|0)+-2)>>0]|0;l=c[Fa>>2]|0;a[l+-1>>0]=a[l+((c[Wa>>2]|0)+-1)>>0]|0;l=(c[Aa>>2]|0)+p|0;c[Aa>>2]=l;if(l>>>0>1073741824)c[Aa>>2]=l&1073741823|1073741824}ab=Xa;ab=ec(c[ab>>2]|0,c[ab+4>>2]|0,p|0,0)|0;l=Xa;c[l>>2]=ab;c[l+4>>2]=C;l=c[Aa>>2]|0;if(l>>>0<=(c[Ga>>2]|0)>>>0){ab=(c[Fa>>2]|0)+l|0;a[ab>>0]=0;a[ab+1>>0]=0;a[ab+2>>0]=0;a[ab+3>>0]=0;a[ab+4>>0]=0;a[ab+5>>0]=0;a[ab+6>>0]=0}}c[g>>2]=(c[g>>2]|0)+p;c[f>>2]=(c[f>>2]|0)-p;continue}l=c[bb>>2]|0;n=(l|0)==1;if(n?(a[y>>0]|0)!=0:0){Eb(e);continue}m=c[z>>2]|0;o=(m|0)==0;if(!o){l=c[h>>2]|0;if(!l){ab=234;break}l=m>>>0<l>>>0?m:l;dc(c[j>>2]|0,c[A>>2]|0,l|0)|0;c[j>>2]=(c[j>>2]|0)+l;c[h>>2]=(c[h>>2]|0)-l;c[A>>2]=(c[A>>2]|0)+l;c[z>>2]=(c[z>>2]|0)-l;l=(c[ya>>2]|0)+l|0;c[ya>>2]=l;if(za)continue;c[k>>2]=l;continue}if(l){ab=234;break}w=(c[f>>2]|0)==0?1:0;o=Xa;n=c[o>>2]|0;o=c[o+4>>2]|0;q=Ya;p=c[q>>2]|0;q=c[q+4>>2]|0;s=$b(n|0,o|0,p|0,q|0)|0;l=C;if(q>>>0>0|(q|0)==0&p>>>0>3221225471){r=bc(p|0,q|0,30)|0;r=p&1073741823|((r&1^1)<<30)+1073741824}else r=p;if(!(Bb(e)|0)){ab=231;break}u=c[D>>2]|0;v=c[E>>2]|0;if(a[F>>0]&1){ab=231;break}t=w<<24>>24==0;if(!t)a[F>>0]=1;if(Bb(e)|0)m=1<<c[x>>2];else m=0;if(l>>>0>0|(l|0)==0&s>>>0>m>>>0){ab=231;break}l=c[cb>>2]|0;if((l|0)==1)if(!(c[G>>2]|0)){l=sa[c[B>>2]&1](c[H>>2]|0,524288)|0;if(!l){ab=108;break}c[G>>2]=l;l=sa[c[B>>2]&1](c[H>>2]|0,131072)|0;if(!l){ab=110;break}c[I>>2]=l;l=c[cb>>2]|0;ab=112}else ab=114;else ab=112;e:do if((ab|0)==112){ab=0;if((l|0)!=0?(l|0)!=1:0){m=c[S>>2]|0;l=m+(s>>>1)+1|0;if(l>>>0>(c[T>>2]|0)>>>0){l=l+((s>>>2)+16)|0;c[T>>2]=l;l=sa[c[B>>2]&1](c[H>>2]|0,l<<4)|0;if(!l){ab=127;break d}m=c[U>>2]|0;if(m){dc(l|0,m|0,c[S>>2]<<4|0)|0;ra[c[V>>2]&1](c[H>>2]|0,c[U>>2]|0);c[U>>2]=0}c[U>>2]=l;m=c[S>>2]|0}else l=c[U>>2]|0;m=l+(m<<4)|0;l=c[Z>>2]|0;f:do switch(l|0){case 10:{Ha(B,s,r,w<<24>>24!=0,u,v,e,c[_>>2]|0,W,X,m,S,Y);break}case 11:{Ia(B,s,r,w<<24>>24!=0,u,v,e,c[_>>2]|0,W,X,m,S,Y);break}default:{if((l|0)>9)break f;do if((l|0)<5)ab=139;else{if((c[xa>>2]|0)>=17){ab=139;break}if((l|0)<7)break;l=(l|0)<9?41:42;ab=139}while(0);g:do if((ab|0)==139){ab=0;do switch(l|0){case 40:break g;case 2:{Ja(B,s,r,w<<24>>24!=0,u,v,e,c[ca>>2]|0,W,X,m,S,Y);break f}case 3:{Ka(B,s,r,w<<24>>24!=0,u,v,e,c[da>>2]|0,W,X,m,S,Y);break f}case 4:{La(B,s,r,w<<24>>24!=0,u,v,e,c[ea>>2]|0,W,X,m,S,Y);break f}case 5:{Ma(B,s,r,w<<24>>24!=0,u,v,e,c[fa>>2]|0,W,X,m,S,Y);break f}case 6:{Na(B,s,r,w<<24>>24!=0,u,v,e,c[ga>>2]|0,W,X,m,S,Y);break f}case 7:{Oa(B,s,r,w<<24>>24!=0,u,v,e,c[ha>>2]|0,W,X,m,S,Y);break f}case 8:{Pa(B,s,r,w<<24>>24!=0,u,v,e,c[ia>>2]|0,W,X,m,S,Y);break f}case 9:{Qa(B,s,r,w<<24>>24!=0,u,v,e,c[ja>>2]|0,W,X,m,S,Y);break f}case 41:{Sa(B,s,r,w<<24>>24!=0,u,v,e,c[la>>2]|0,W,X,m,S,Y);break f}case 42:{Ta(B,s,r,w<<24>>24!=0,u,v,e,c[ma>>2]|0,W,X,m,S,Y);break f}default:break f}while(0)}while(0);Ra(B,s,r,w<<24>>24!=0,u,v,e,c[ka>>2]|0,W,X,m,S,Y)}}while(0);n=c[$>>2]|0;m=c[x>>2]|0;m=((n|0)>(m|0)?n:m)+1|0;m=1<<((m|0)<24?m:24);n=m>>>3;s=Xa;o=ba;o=$b(c[s>>2]|0,c[s+4>>2]|0,c[o>>2]|0,c[o+4>>2]|0)|0;if(Bb(e)|0)q=1<<c[x>>2];else q=0;l=c[cb>>2]|0;if((l|0)<4)p=((c[Y>>2]|0)+(c[S>>2]|0)|0)>>>0>12286;else p=0;do if(!(t^1|p|(o+q|0)>>>0>m>>>0)){if((c[Y>>2]|0)>>>0>=n>>>0)break;if((c[S>>2]|0)>>>0>=n>>>0)break;n=Ya;m=c[n>>2]|0;n=c[n+4>>2]|0;if(n>>>0>0|(n|0)==0&m>>>0>3221225471){v=bc(m|0,n|0,30)|0;m=m&1073741823|((v&1^1)<<30)+1073741824}o=Xa;n=c[o>>2]|0;o=c[o+4>>2]|0;if(o>>>0>0|(o|0)==0&n>>>0>3221225471){p=bc(n|0,o|0,30)|0;p=n&1073741823|((p&1^1)<<30)+1073741824}else p=n;v=Ya;c[v>>2]=n;c[v+4>>2]=o;h:do if(p>>>0<m>>>0){i:do if((l|0)<=9){do if((l|0)<5)ab=168;else{if((c[na>>2]|0)>=17){ab=168;break}if((l|0)<7)break;l=(l|0)<9?41:42;ab=168}while(0);j:do if((ab|0)==168){ab=0;do switch(l|0){case 10:break i;case 40:break j;case 2:{v=c[ca>>2]|0;a[v+262148>>0]=1;c[v+262152>>2]=0;c[v+262156>>2]=0;break h}case 3:{v=c[da>>2]|0;a[v+262152>>0]=1;c[v+262156>>2]=0;c[v+262160>>2]=0;break h}case 4:{v=c[ea>>2]|0;a[v+524304>>0]=1;c[v+524308>>2]=0;c[v+524312>>2]=0;break h}case 5:{v=c[fa>>2]|0;a[v+1081344>>0]=1;c[v+1081348>>2]=0;c[v+1081352>>2]=0;break h}case 6:{v=c[ga>>2]|0;a[v+2129920>>0]=1;c[v+2129924>>2]=0;c[v+2129928>>2]=0;break h}case 7:{v=c[ha>>2]|0;a[v+8454144>>0]=1;c[v+8454148>>2]=0;c[v+8454152>>2]=0;break h}case 8:{v=c[ia>>2]|0;a[v+16842752>>0]=1;c[v+16842756>>2]=0;c[v+16842760>>2]=0;break h}case 9:{v=c[ja>>2]|0;a[v+33619968>>0]=1;c[v+33619972>>2]=0;c[v+33619976>>2]=0;break h}case 41:{v=c[la>>2]|0;a[v+524290>>0]=1;c[v+524292>>2]=0;c[v+524296>>2]=0;break h}case 42:{v=c[ma>>2]|0;a[v+1311744>>0]=1;c[v+1311748>>2]=0;c[v+1311752>>2]=0;break h}default:break h}while(0)}while(0);v=c[ka>>2]|0;a[v+524290>>0]=1;c[v+524292>>2]=0;c[v+524296>>2]=0;break h}while(0);a[(c[_>>2]|0)+524304>>0]=1}while(0);c[z>>2]=0;break e}while(0);l=c[X>>2]|0;if(l){m=c[S>>2]|0;c[S>>2]=m+1;s=c[U>>2]|0;c[s+(m<<4)>>2]=l;c[s+(m<<4)+4>>2]=67108864;c[s+(m<<4)+8>>2]=0;b[s+(m<<4)+14>>1]=16;m=s+(m<<4)+12|0;do if(l>>>0>=6){if(l>>>0<130){s=l+-2|0;l=((aa(s|0)|0)^31)+-1|0;l=(l<<1)+(s>>>l)+2|0;break}if(l>>>0<2114){l=((aa(l+-66|0)|0)^31)+10|0;break}if(l>>>0<6210){l=21;break}l=l>>>0<22594?22:23}while(0);s=l&65535;b[m>>1]=b[88156+((s>>>3)*3<<1)>>1]|(s<<3&56|2)&65535;c[Y>>2]=(c[Y>>2]|0)+(c[X>>2]|0);c[X>>2]=0}m=Xa;l=c[m>>2]|0;m=c[m+4>>2]|0;o=ba;n=c[o>>2]|0;o=c[o+4>>2]|0;if(t&((l|0)==(n|0)&(m|0)==(o|0))){c[z>>2]=0;break}if(m>>>0<o>>>0|(m|0)==(o|0)&l>>>0<n>>>0){ab=194;break d}if(!(m>>>0>o>>>0|(m|0)==(o|0)&l>>>0>n>>>0|t^1)){ab=196;break d}n=$b(l|0,m|0,n|0,o|0)|0;t=C;if(!(t>>>0<0|(t|0)==0&n>>>0<16777217)){ab=198;break d}l=(n<<1)+502|0;if((c[J>>2]|0)>>>0<l>>>0){ra[c[L>>2]&1](c[M>>2]|0,c[K>>2]|0);c[K>>2]=0;t=sa[c[B>>2]&1](c[M>>2]|0,l)|0;m=t;if(!t){ab=202;break d}c[K>>2]=m;c[J>>2]=l}else m=c[K>>2]|0;c[$a>>2]=d[y>>0];p=m;a[p>>0]=a[N>>0]|0;q=u;o=ba;Db(B,q,v,c[o>>2]|0,c[o+4>>2]|0,n,w<<24>>24!=0,e,a[qa>>0]|0,a[ta>>0]|0,c[Y>>2]|0,c[S>>2]|0,c[U>>2]|0,ua,W,$a,p);a[N>>0]=a[p+((c[$a>>2]|0)>>>3)>>0]|0;a[y>>0]=c[$a>>2]&7;p=Xa;o=c[p>>2]|0;p=c[p+4>>2]|0;n=ba;c[n>>2]=o;c[n+4>>2]=p;n=Ya;l=c[n>>2]|0;n=c[n+4>>2]|0;if(n>>>0>0|(n|0)==0&l>>>0>3221225471){u=bc(l|0,n|0,30)|0;l=l&1073741823|((u&1^1)<<30)+1073741824}if(p>>>0>0|(p|0)==0&o>>>0>3221225471){n=bc(o|0,p|0,30)|0;n=o&1073741823|((n&1^1)<<30)+1073741824}else n=o;u=Ya;c[u>>2]=o;c[u+4>>2]=p;k:do if(n>>>0<l>>>0){l=c[cb>>2]|0;l:do if((l|0)<=9){do if((l|0)<5)ab=214;else{if((c[na>>2]|0)>=17){ab=214;break}if((l|0)<7)break;l=(l|0)<9?41:42;ab=214}while(0);m:do if((ab|0)==214){ab=0;do switch(l|0){case 10:break l;case 40:break m;case 2:{u=c[ca>>2]|0;a[u+262148>>0]=1;c[u+262152>>2]=0;c[u+262156>>2]=0;break k}case 3:{u=c[da>>2]|0;a[u+262152>>0]=1;c[u+262156>>2]=0;c[u+262160>>2]=0;break k}case 4:{u=c[ea>>2]|0;a[u+524304>>0]=1;c[u+524308>>2]=0;c[u+524312>>2]=0;break k}case 5:{u=c[fa>>2]|0;a[u+1081344>>0]=1;c[u+1081348>>2]=0;c[u+1081352>>2]=0;break k}case 6:{u=c[ga>>2]|0;a[u+2129920>>0]=1;c[u+2129924>>2]=0;c[u+2129928>>2]=0;break k}case 7:{u=c[ha>>2]|0;a[u+8454144>>0]=1;c[u+8454148>>2]=0;c[u+8454152>>2]=0;break k}case 8:{u=c[ia>>2]|0;a[u+16842752>>0]=1;c[u+16842756>>2]=0;c[u+16842760>>2]=0;break k}case 9:{u=c[ja>>2]|0;a[u+33619968>>0]=1;c[u+33619972>>2]=0;c[u+33619976>>2]=0;break k}case 41:{u=c[la>>2]|0;a[u+524290>>0]=1;c[u+524292>>2]=0;c[u+524296>>2]=0;break k}case 42:{u=c[ma>>2]|0;a[u+1311744>>0]=1;c[u+1311748>>2]=0;c[u+1311752>>2]=0;break k}default:break k}while(0)}while(0);u=c[ka>>2]|0;a[u+524290>>0]=1;c[u+524292>>2]=0;c[u+524296>>2]=0;break k}while(0);a[(c[_>>2]|0)+524304>>0]=1}while(0);n=ba;l=c[n>>2]|0;n=c[n+4>>2]|0;do if(!((l|0)==0&(n|0)==0)){a[qa>>0]=a[q+(l+-1&v)>>0]|0;if(!(n>>>0>0|(n|0)==0&l>>>0>1))break;u=ec(l|0,n|0,-2,0)|0;a[ta>>0]=a[q+(u&v)>>0]|0}while(0);c[S>>2]=0;c[Y>>2]=0;c[va>>2]=c[wa>>2];c[va+4>>2]=c[wa+4>>2];c[va+8>>2]=c[wa+8>>2];c[va+12>>2]=c[wa+12>>2];c[A>>2]=m;c[z>>2]=(c[$a>>2]|0)>>>3}else ab=114}while(0);do if((ab|0)==114){c[db>>2]=d[y>>0];if(!((n|0)==(p|0)&(o|0)==(q|0)^1|t^1)){c[z>>2]=0;break}m=(s<<1)+502|0;l=c[K>>2]|0;if((c[J>>2]|0)>>>0<m>>>0){ra[c[L>>2]&1](c[M>>2]|0,l);c[K>>2]=0;l=sa[c[B>>2]&1](c[M>>2]|0,m)|0;if(!l){ab=118;break d}c[K>>2]=l;c[J>>2]=m;n=l}else n=l;a[n>>0]=a[N>>0]|0;o=Cb(e,c[cb>>2]|0,s,eb)|0;l=u+(r&v)|0;m=w<<24>>24!=0;if(!(c[cb>>2]|0))ub(B,l,s,m,o,c[eb>>2]|0,O,P,Q,R,db,n);else yb(B,l,s,m,c[G>>2]|0,c[I>>2]|0,o,c[eb>>2]|0,db,n);a[N>>0]=a[n+((c[db>>2]|0)>>>3)>>0]|0;a[y>>0]=c[db>>2]&7;u=Xa;v=c[u+4>>2]|0;ab=Ya;c[ab>>2]=c[u>>2];c[ab+4>>2]=v;c[A>>2]=n;c[z>>2]=(c[db>>2]|0)>>>3}while(0);if(!(w<<24>>24))continue;c[bb>>2]=2}switch(ab|0){case 62:{oa(1);break}case 71:{oa(1);break}case 108:{oa(1);break}case 110:{oa(1);break}case 118:{oa(1);break}case 127:{oa(1);break}case 194:{pa(406017,406053,906,406080);break}case 196:{pa(406103,406053,907,406080);break}case 198:{pa(406149,406053,908,406080);break}case 202:{oa(1);break}case 231:{k=0;i=fb;return k|0}case 234:{if(n^1|o^1){k=1;i=fb;return k|0}c[bb>>2]=0;c[A>>2]=0;k=1;i=fb;return k|0}}return 0}function Bb(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;k=b+5237|0;if(a[k>>0]&1)return 1;e=c[b+4>>2]|0;if((e|0)>=0)if((e|0)>11)h=11;else i=4;else{e=0;i=4}if((i|0)==4)h=e;c[b+4>>2]=h;e=b+8|0;f=c[e>>2]|0;if((f|0)>=10){if((f|0)>24){c[e>>2]=24;f=24}}else{c[e>>2]=10;f=10}g=b+12|0;e=c[g>>2]|0;i=(h|0)==0;do if(h>>>0>=2)if((h|0)<4)e=14;else{if(!e){if(!((h|0)>8&(f|0)>16)){e=16;break}e=(f|0)>18?18:f;break}if((e|0)>=16){if((e|0)>24){e=24;break}}else e=16}else e=f;while(0);c[g>>2]=e;g=1<<((f|0)>(e|0)?f:e)+1;c[b+88>>2]=g;c[b+92>>2]=g+-1;e=1<<e;c[b+96>>2]=e;c[b+100>>2]=g+e;e=b+192|0;g=b+193|0;a:do switch(f|0){case 16:{a[e>>0]=0;a[g>>0]=1;break}case 17:{a[e>>0]=1;a[g>>0]=7;break}default:if((f|0)>17){a[e>>0]=(f<<1)+222|1;a[g>>0]=4;break a}else{a[e>>0]=(f<<4)+128|1;a[g>>0]=7;break a}}while(0);b:do if(i){g=b+4436|0;h=b+4692|0;i=b+5204|0;d=b+4308|0;e=406473;f=d+128|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));dc(g|0,88174,256)|0;d=h;e=406601;f=d+57|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));c[i>>2]=448}else{e=b+16|0;g=b+28|0;c:do if((h|0)>9){f=g;i=65}else{do if((h|0)>=5&(f|0)<17)if((h|0)<7){f=g;i=56;break}else{h=(h|0)<9?41:42;i=31;break}else i=31;while(0);d:do if((i|0)==31){f=g;e:do switch(h|0){case 10:{i=65;break c}case 40:{i=56;break d}case 2:{d=sa[c[e>>2]&1](c[b+24>>2]|0,262160)|0;if(!d)oa(1);else{c[g>>2]=d;d=f;i=69;break e}break}case 3:{d=sa[c[e>>2]&1](c[b+24>>2]|0,262164)|0;if(!d)oa(1);else{c[b+32>>2]=d;d=f;i=70;break e}break}case 4:{d=sa[c[e>>2]&1](c[b+24>>2]|0,524316)|0;if(!d)oa(1);else{c[b+36>>2]=d;d=f;i=71;break e}break}case 5:{d=sa[c[e>>2]&1](c[b+24>>2]|0,1081356)|0;if(!d)oa(1);else{c[b+40>>2]=d;d=f;i=72;break e}break}case 6:{d=sa[c[e>>2]&1](c[b+24>>2]|0,2129932)|0;if(!d)oa(1);else{c[b+44>>2]=d;d=f;i=73;break e}break}case 7:{d=sa[c[e>>2]&1](c[b+24>>2]|0,8454156)|0;if(!d)oa(1);else{c[b+48>>2]=d;d=f;i=74;break e}break}case 8:{d=sa[c[e>>2]&1](c[b+24>>2]|0,16842764)|0;if(!d)oa(1);else{c[b+52>>2]=d;d=f;i=75;break e}break}case 9:{d=sa[c[e>>2]&1](c[b+24>>2]|0,33619980)|0;if(!d)oa(1);else{c[b+56>>2]=d;d=f;i=76;break e}break}case 41:{d=sa[c[e>>2]&1](c[b+24>>2]|0,524304)|0;if(!d)oa(1);else{c[b+64>>2]=d;d=f;i=78;break e}break}case 42:{d=sa[c[e>>2]&1](c[b+24>>2]|0,1311760)|0;if(!d)oa(1);else{c[b+68>>2]=d;d=f;i=79;break e}break}default:{d=f;switch(h|0){case 10:break c;case 40:{j=d;break d}case 2:{i=69;break}case 3:{i=70;break}case 4:{i=71;break}case 5:{i=72;break}case 6:{i=73;break}case 7:{i=74;break}case 8:{i=75;break}case 9:{i=76;break}case 41:{i=78;break}case 42:{i=79;break}default:break b}}}while(0);switch(i|0){case 69:{b=c[d>>2]|0;a[b+262148>>0]=1;c[b+262152>>2]=0;c[b+262156>>2]=0;break b}case 70:{b=c[d+4>>2]|0;a[b+262152>>0]=1;c[b+262156>>2]=0;c[b+262160>>2]=0;break b}case 71:{b=c[d+8>>2]|0;a[b+524304>>0]=1;c[b+524308>>2]=0;c[b+524312>>2]=0;break b}case 72:{b=c[d+12>>2]|0;a[b+1081344>>0]=1;c[b+1081348>>2]=0;c[b+1081352>>2]=0;break b}case 73:{b=c[d+16>>2]|0;a[b+2129920>>0]=1;c[b+2129924>>2]=0;c[b+2129928>>2]=0;break b}case 74:{b=c[d+20>>2]|0;a[b+8454144>>0]=1;c[b+8454148>>2]=0;c[b+8454152>>2]=0;break b}case 75:{b=c[d+24>>2]|0;a[b+16842752>>0]=1;c[b+16842756>>2]=0;c[b+16842760>>2]=0;break b}case 76:{b=c[d+28>>2]|0;a[b+33619968>>0]=1;c[b+33619972>>2]=0;c[b+33619976>>2]=0;break b}case 78:{b=c[d+36>>2]|0;a[b+524290>>0]=1;c[b+524292>>2]=0;c[b+524296>>2]=0;break b}case 79:{b=c[d+40>>2]|0;a[b+1311744>>0]=1;c[b+1311748>>2]=0;c[b+1311752>>2]=0;break b}}}while(0);do if((i|0)==56){d=sa[c[e>>2]&1](c[b+24>>2]|0,524304)|0;if(!d)oa(1);else{c[b+60>>2]=d;j=f;break}}while(0);b=c[j+32>>2]|0;a[b+524290>>0]=1;c[b+524292>>2]=0;c[b+524296>>2]=0;break b}while(0);do if((i|0)==65){e=sa[c[e>>2]&1](c[b+24>>2]|0,524308)|0;if(!e)oa(1);else{c[f+44>>2]=e;d=c[f+44>>2]|0;c[d+524292>>2]=0;c[d+524300>>2]=0;a[d+524304>>0]=1;d=f;break}}while(0);a[(c[d+44>>2]|0)+524304>>0]=1}while(0);a[k>>0]=1;return 1}function Cb(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0;b=(b|0)==0?32768:131072;f=256;while(1){if(!(f>>>0<b>>>0&f>>>0<d>>>0))break;f=f<<1}do if(f>>>0>=1025){b=a+4304|0;if(f>>>0<=(c[b>>2]|0)>>>0){g=c[a+4300>>2]|0;break}c[b>>2]=f;d=a+4300|0;b=a+24|0;ra[c[a+20>>2]&1](c[b>>2]|0,c[d>>2]|0);c[d>>2]=0;a=sa[c[a+16>>2]&1](c[b>>2]|0,f<<2)|0;b=a;if(!a)oa(1);else{c[d>>2]=b;g=b;break}}else g=a+204|0;while(0);c[e>>2]=f;ac(g|0,0,f<<2|0)|0;return g|0}
function Pa(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0;qa=i;i=i+32|0;oa=qa+16|0;pa=qa;ma=(1<<c[m+8>>2])+-16|0;v=c[p>>2]|0;na=h+g|0;la=g>>>0>3?na+-3|0:h;ka=m+4|0;ja=(c[ka>>2]|0)<9?64:512;u=ja+h|0;if((((h|0)==0^1|j^1)^1)&g>>>0<513){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;b[n+((_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17<<1)>>1]=0;f=f+1|0}if(g){a[n+16842752>>0]=0;t=8}}else{f=n+16842752|0;if(!(a[f>>0]&1))t=8;else{ac(n|0,0,65536)|0;a[f>>0]=0;t=8}}if((t|0)==8?g>>>0>2&h>>>0>2:0){ga=h+-3|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ia=n+(ha<<1)|0;c[n+65536+((e[ia>>1]&127|ha<<7)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16;ia=h+-2|0;ha=k+(ia&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ga=n+(ha<<1)|0;c[n+65536+((e[ga>>1]&127|ha<<7)<<2)>>2]=ia;b[ga>>1]=(b[ga>>1]|0)+1<<16>>16;ga=h+-1|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ia=n+(ha<<1)|0;c[n+65536+((e[ia>>1]&127|ha<<7)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16}P=oa+4|0;Q=oa+8|0;R=oa+12|0;S=oa+4|0;T=oa+8|0;U=oa+12|0;V=n+16842760|0;W=n+16842756|0;X=ja<<2;Y=na+-4|0;Z=na+-3|0;$=pa+12|0;ba=pa+4|0;ca=pa+8|0;da=pa+4|0;ea=pa+8|0;fa=pa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;O=q;f=v;a:while(1){M=O;N=u+X|0;b:while(1){L=na-m|0;if((m+4|0)>>>0>=na>>>0)break a;J=m>>>0<ma>>>0?m:ma;c[oa>>2]=0;c[P>>2]=0;c[Q>>2]=0;c[R>>2]=4240;K=m&l;g=c[oa>>2]|0;c[oa>>2]=0;c[S>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;y=4240;j=0;x=0;while(1){if((x|0)==10)break;w=(c[o+(c[11372+(x<<2)>>2]<<2)>>2]|0)+(c[11436+(x<<2)>>2]|0)|0;t=m-w|0;do if(((!(t>>>0>=m>>>0|w>>>0>J>>>0)?(ra=t&l,sa=K+g|0,sa>>>0<=l>>>0):0)?(ta=ra+g|0,ta>>>0<=l>>>0):0)?(a[k+sa>>0]|0)==(a[k+ta>>0]|0):0){v=0;t=F;while(1){h=t;if(h>>>0>H>>>0)break;D=t;E=k+(ra+v)|0;if((d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;v=v+4|0;t=h+4|0}while(1){if(t>>>0>=G>>>0)break;if((a[k+(ra+v)>>0]|0)!=(a[t>>0]|0))break;v=v+1|0;t=t+1|0}if(v>>>0<=2?!((v|0)==2&x>>>0<2):0){t=y;break}t=(v*540|0)+(c[11500+(x<<2)>>2]|0)|0;if(y>>>0<t>>>0){c[oa>>2]=v;c[T>>2]=w;c[U>>2]=t;g=v;j=1}else t=y}else t=y;while(0);y=t;x=x+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;C=D<<7;D=n+(D<<1)|0;E=b[D>>1]|0;t=E&65535;E=(E&65535)>128?t+-128|0:0;c:while(1){B=K+g|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(t>>>0<=E>>>0)break c;t=t+-1|0;h=c[n+65536+((C|t&127)<<2)>>2]|0;z=m-h|0;if(z>>>0>J>>>0)break c;x=h&l;if(A)continue;h=x+g|0;if(h>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+h>>0]|0)){w=0;h=F}else continue;while(1){v=h;if(v>>>0>H>>>0)break;Ia=h;Ha=k+(x+w)|0;if((d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0)!=(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0))break;w=w+4|0;h=v+4|0}while(1){if(h>>>0>=G>>>0)break;if((a[k+(x+w)>>0]|0)!=(a[h>>0]|0))break;w=w+1|0;h=h+1|0}if(w>>>0<=3)continue;h=(w*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(y>>>0<h>>>0)break}c[oa>>2]=w;c[T>>2]=z;c[U>>2]=h;g=w;y=h;j=1}Ia=b[D>>1]|0;c[n+65536+((C|Ia&127)<<2)>>2]=m;b[D>>1]=Ia+1<<16>>16;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ia=b[21084+(w<<1)>>1]|0;t=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(ua=t&31,va=t>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=L>>>0):0){g=wa+ua|0;v=280811+g|0;g=280811+(g+-4)|0;h=0;t=280811+wa|0;while(1){if(t>>>0>g>>>0)break;Ia=k+(K+h)|0;if((d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;t=t+4|0}while(1){if(t>>>0>=v>>>0)break;if((a[k+(K+h)>>0]|0)!=(a[t>>0]|0))break;h=h+1|0;t=t+1|0}if(!((h+10|0)>>>0<=ua>>>0|(h|0)==0)?(xa=J+va+1+(d[407930+(ua-h)>>0]<<d[280786+ua>>0])|0,ya=(h*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[U>>2]|0)>>>0):0){c[oa>>2]=h;c[S>>2]=ua^h;c[T>>2]=xa;c[U>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){M=0;x=f;break}f=f+1|0;t=m+1|0;if(t>>>0<=u>>>0){m=t;continue}if(t>>>0>N>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&127|Ha<<7)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&127|Ha<<7)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+2|0;f=f+2|0}}}while(1){L=L+-1|0;if((c[ka>>2]|0)<5){f=(c[oa>>2]|0)+-1|0;f=f>>>0<L>>>0?f:L}else f=0;c[pa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;J=y>>>0<ma>>>0?y:ma;K=y&l;t=c[pa>>2]|0;c[pa>>2]=0;c[da>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;w=4240;f=0;v=0;while(1){if((v|0)==10)break;h=(c[o+(c[11372+(v<<2)>>2]<<2)>>2]|0)+(c[11436+(v<<2)>>2]|0)|0;j=y-h|0;do if(((!(j>>>0>=y>>>0|h>>>0>J>>>0)?(za=j&l,Aa=K+t|0,Aa>>>0<=l>>>0):0)?(Ba=za+t|0,Ba>>>0<=l>>>0):0)?(a[k+Aa>>0]|0)==(a[k+Ba>>0]|0):0){u=0;j=F;while(1){g=j;if(g>>>0>H>>>0)break;Ha=j;Ia=k+(za+u)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;u=u+4|0;j=g+4|0}while(1){if(j>>>0>=G>>>0)break;if((a[k+(za+u)>>0]|0)!=(a[j>>0]|0))break;u=u+1|0;j=j+1|0}if(u>>>0<=2?!((u|0)==2&v>>>0<2):0){j=w;break}j=(u*540|0)+(c[11500+(v<<2)>>2]|0)|0;if(w>>>0<j>>>0){c[pa>>2]=u;c[ea>>2]=h;c[fa>>2]=j;t=u;f=1}else j=w}else j=w;while(0);w=j;v=v+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;C=D<<7;D=n+(D<<1)|0;E=b[D>>1]|0;j=E&65535;E=(E&65535)>128?j+-128|0:0;d:while(1){B=K+t|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(j>>>0<=E>>>0)break d;j=j+-1|0;g=c[n+65536+((C|j&127)<<2)>>2]|0;z=y-g|0;if(z>>>0>J>>>0)break d;v=g&l;if(A)continue;g=v+t|0;if(g>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+g>>0]|0)){h=0;g=F}else continue;while(1){u=g;if(u>>>0>H>>>0)break;Ha=g;Ia=k+(v+h)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;g=u+4|0}while(1){if(g>>>0>=G>>>0)break;if((a[k+(v+h)>>0]|0)!=(a[g>>0]|0))break;h=h+1|0;g=g+1|0}if(h>>>0<=3)continue;g=(h*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(w>>>0<g>>>0)break}c[pa>>2]=h;c[ea>>2]=z;c[fa>>2]=g;t=h;w=g;f=1}Ia=b[D>>1]|0;c[n+65536+((C|Ia&127)<<2)>>2]=y;b[D>>1]=Ia+1<<16>>16;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;h=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ia=b[21084+(h<<1)>>1]|0;j=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(Ca=j&31,Da=j>>>5,Ea=(c[11272+(Ca<<2)>>2]|0)+(_(Ca,Da)|0)|0,Ca>>>0<=L>>>0):0){t=Ea+Ca|0;u=280811+t|0;t=280811+(t+-4)|0;g=0;j=280811+Ea|0;while(1){if(j>>>0>t>>>0)break;Ia=k+(K+g)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;g=g+4|0;j=j+4|0}while(1){if(j>>>0>=u>>>0)break;if((a[k+(K+g)>>0]|0)!=(a[j>>0]|0))break;g=g+1|0;j=j+1|0}if(!((g+10|0)>>>0<=Ca>>>0|(g|0)==0)?(Fa=J+Da+1+(d[407930+(Ca-g)>>0]<<d[280786+Ca>>0])|0,Ga=(g*540|0)+3840+(_((aa(Fa|0)|0)^31,-120)|0)|0,Ga>>>0>=(c[fa>>2]|0)>>>0):0){c[pa>>2]=g;c[da>>2]=Ca^g;c[ea>>2]=Fa;c[fa>>2]=Ga;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;h=h+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[R>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[oa>>2]=c[pa>>2];c[oa+4>>2]=c[pa+4>>2];c[oa+8>>2]=c[pa+8>>2];c[oa+12>>2]=c[pa+12>>2];M=M+1|0;if(!((M|0)<4&(m+5|0)>>>0<na>>>0)){x=f;break}else{m=y;x=f}}m=c[oa>>2]|0;u=y+(m<<1)+ja|0;f=c[Q>>2]|0;e:do if(f>>>0<=(y>>>0<ma>>>0?y:ma)>>>0){g=f+3|0;Ia=c[o>>2]|0;j=g-Ia|0;t=c[ia>>2]|0;g=g-t|0;if((f|0)==(Ia|0))f=0;else{f:do if((f|0)!=(t|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(g>>>0<7){f=266017486>>>(g<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[Q>>2];m=c[oa>>2]|0}}else f=f+15|0;while(0);w=O+16|0;Ia=c[P>>2]|0;j=m^Ia;c[O>>2]=x;c[O+4>>2]=m|Ia<<24;m=O+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{N=f+-12|0;Ia=((aa(N|0)|0)^31)+-1|0;Ha=N>>>Ia&1;f=((Ia<<1)+65534|Ha)+16&65535;b[m>>1]=f;m=Ia<<24|N-((Ha|2)<<Ia)}c[O+8>>2]=m;h=f<<16>>16==0;v=O+12|0;do if(x>>>0>=6){if(x>>>0<130){Ia=x+-2|0;g=((aa(Ia|0)|0)^31)+-1|0;g=(g<<1)+(Ia>>>g)+2&65535;break}if(x>>>0<2114){g=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)g=21;else g=x>>>0<22594?22:23}else g=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ia=j+-6|0;f=((aa(Ia|0)|0)^31)+-1|0;f=(f<<1)+(Ia>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=g&65535;t=m&7|j<<3&56;if(h&(g&65535)<8&(f&65535)<16)f=((f&65535)<8?t:t|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|t&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[oa>>2]|0)|0;f=f>>>0<la>>>0?f:la;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&127|Ha<<7)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+1|0}m=y+(c[oa>>2]|0)|0;O=w;f=0}c[p>>2]=f+L;c[r>>2]=(c[r>>2]|0)+(M-q>>4);i=qa;return}function Qa(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0;qa=i;i=i+32|0;oa=qa+16|0;pa=qa;ma=(1<<c[m+8>>2])+-16|0;v=c[p>>2]|0;na=h+g|0;la=g>>>0>3?na+-3|0:h;ka=m+4|0;ja=(c[ka>>2]|0)<9?64:512;u=ja+h|0;if((((h|0)==0^1|j^1)^1)&g>>>0<513){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;b[n+((_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17<<1)>>1]=0;f=f+1|0}if(g){a[n+33619968>>0]=0;t=8}}else{f=n+33619968|0;if(!(a[f>>0]&1))t=8;else{ac(n|0,0,65536)|0;a[f>>0]=0;t=8}}if((t|0)==8?g>>>0>2&h>>>0>2:0){ga=h+-3|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ia=n+(ha<<1)|0;c[n+65536+((e[ia>>1]&255|ha<<8)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16;ia=h+-2|0;ha=k+(ia&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ga=n+(ha<<1)|0;c[n+65536+((e[ga>>1]&255|ha<<8)<<2)>>2]=ia;b[ga>>1]=(b[ga>>1]|0)+1<<16>>16;ga=h+-1|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ia=n+(ha<<1)|0;c[n+65536+((e[ia>>1]&255|ha<<8)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16}P=oa+4|0;Q=oa+8|0;R=oa+12|0;S=oa+4|0;T=oa+8|0;U=oa+12|0;V=n+33619976|0;W=n+33619972|0;X=ja<<2;Y=na+-4|0;Z=na+-3|0;$=pa+12|0;ba=pa+4|0;ca=pa+8|0;da=pa+4|0;ea=pa+8|0;fa=pa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;O=q;f=v;a:while(1){M=O;N=u+X|0;b:while(1){L=na-m|0;if((m+4|0)>>>0>=na>>>0)break a;J=m>>>0<ma>>>0?m:ma;c[oa>>2]=0;c[P>>2]=0;c[Q>>2]=0;c[R>>2]=4240;K=m&l;g=c[oa>>2]|0;c[oa>>2]=0;c[S>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;y=4240;j=0;x=0;while(1){if((x|0)==16)break;w=(c[o+(c[11372+(x<<2)>>2]<<2)>>2]|0)+(c[11436+(x<<2)>>2]|0)|0;t=m-w|0;do if(((!(t>>>0>=m>>>0|w>>>0>J>>>0)?(ra=t&l,sa=K+g|0,sa>>>0<=l>>>0):0)?(ta=ra+g|0,ta>>>0<=l>>>0):0)?(a[k+sa>>0]|0)==(a[k+ta>>0]|0):0){v=0;t=F;while(1){h=t;if(h>>>0>H>>>0)break;D=t;E=k+(ra+v)|0;if((d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;v=v+4|0;t=h+4|0}while(1){if(t>>>0>=G>>>0)break;if((a[k+(ra+v)>>0]|0)!=(a[t>>0]|0))break;v=v+1|0;t=t+1|0}if(v>>>0<=2?!((v|0)==2&x>>>0<2):0){t=y;break}t=(v*540|0)+(c[11500+(x<<2)>>2]|0)|0;if(y>>>0<t>>>0){c[oa>>2]=v;c[T>>2]=w;c[U>>2]=t;g=v;j=1}else t=y}else t=y;while(0);y=t;x=x+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;C=D<<8;D=n+(D<<1)|0;E=b[D>>1]|0;t=E&65535;E=(E&65535)>256?t+-256|0:0;c:while(1){B=K+g|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(t>>>0<=E>>>0)break c;t=t+-1|0;h=c[n+65536+((C|t&255)<<2)>>2]|0;z=m-h|0;if(z>>>0>J>>>0)break c;x=h&l;if(A)continue;h=x+g|0;if(h>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+h>>0]|0)){w=0;h=F}else continue;while(1){v=h;if(v>>>0>H>>>0)break;Ia=h;Ha=k+(x+w)|0;if((d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0)!=(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0))break;w=w+4|0;h=v+4|0}while(1){if(h>>>0>=G>>>0)break;if((a[k+(x+w)>>0]|0)!=(a[h>>0]|0))break;w=w+1|0;h=h+1|0}if(w>>>0<=3)continue;h=(w*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(y>>>0<h>>>0)break}c[oa>>2]=w;c[T>>2]=z;c[U>>2]=h;g=w;y=h;j=1}Ia=b[D>>1]|0;c[n+65536+((C|Ia&255)<<2)>>2]=m;b[D>>1]=Ia+1<<16>>16;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ia=b[21084+(w<<1)>>1]|0;t=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(ua=t&31,va=t>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=L>>>0):0){g=wa+ua|0;v=280811+g|0;g=280811+(g+-4)|0;h=0;t=280811+wa|0;while(1){if(t>>>0>g>>>0)break;Ia=k+(K+h)|0;if((d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;t=t+4|0}while(1){if(t>>>0>=v>>>0)break;if((a[k+(K+h)>>0]|0)!=(a[t>>0]|0))break;h=h+1|0;t=t+1|0}if(!((h+10|0)>>>0<=ua>>>0|(h|0)==0)?(xa=J+va+1+(d[407930+(ua-h)>>0]<<d[280786+ua>>0])|0,ya=(h*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[U>>2]|0)>>>0):0){c[oa>>2]=h;c[S>>2]=ua^h;c[T>>2]=xa;c[U>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){M=0;x=f;break}f=f+1|0;t=m+1|0;if(t>>>0<=u>>>0){m=t;continue}if(t>>>0>N>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&255|Ha<<8)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&255|Ha<<8)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+2|0;f=f+2|0}}}while(1){L=L+-1|0;if((c[ka>>2]|0)<5){f=(c[oa>>2]|0)+-1|0;f=f>>>0<L>>>0?f:L}else f=0;c[pa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;J=y>>>0<ma>>>0?y:ma;K=y&l;t=c[pa>>2]|0;c[pa>>2]=0;c[da>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;w=4240;f=0;v=0;while(1){if((v|0)==16)break;h=(c[o+(c[11372+(v<<2)>>2]<<2)>>2]|0)+(c[11436+(v<<2)>>2]|0)|0;j=y-h|0;do if(((!(j>>>0>=y>>>0|h>>>0>J>>>0)?(za=j&l,Aa=K+t|0,Aa>>>0<=l>>>0):0)?(Ba=za+t|0,Ba>>>0<=l>>>0):0)?(a[k+Aa>>0]|0)==(a[k+Ba>>0]|0):0){u=0;j=F;while(1){g=j;if(g>>>0>H>>>0)break;Ha=j;Ia=k+(za+u)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;u=u+4|0;j=g+4|0}while(1){if(j>>>0>=G>>>0)break;if((a[k+(za+u)>>0]|0)!=(a[j>>0]|0))break;u=u+1|0;j=j+1|0}if(u>>>0<=2?!((u|0)==2&v>>>0<2):0){j=w;break}j=(u*540|0)+(c[11500+(v<<2)>>2]|0)|0;if(w>>>0<j>>>0){c[pa>>2]=u;c[ea>>2]=h;c[fa>>2]=j;t=u;f=1}else j=w}else j=w;while(0);w=j;v=v+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;C=D<<8;D=n+(D<<1)|0;E=b[D>>1]|0;j=E&65535;E=(E&65535)>256?j+-256|0:0;d:while(1){B=K+t|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(j>>>0<=E>>>0)break d;j=j+-1|0;g=c[n+65536+((C|j&255)<<2)>>2]|0;z=y-g|0;if(z>>>0>J>>>0)break d;v=g&l;if(A)continue;g=v+t|0;if(g>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+g>>0]|0)){h=0;g=F}else continue;while(1){u=g;if(u>>>0>H>>>0)break;Ha=g;Ia=k+(v+h)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;g=u+4|0}while(1){if(g>>>0>=G>>>0)break;if((a[k+(v+h)>>0]|0)!=(a[g>>0]|0))break;h=h+1|0;g=g+1|0}if(h>>>0<=3)continue;g=(h*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(w>>>0<g>>>0)break}c[pa>>2]=h;c[ea>>2]=z;c[fa>>2]=g;t=h;w=g;f=1}Ia=b[D>>1]|0;c[n+65536+((C|Ia&255)<<2)>>2]=y;b[D>>1]=Ia+1<<16>>16;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;h=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ia=b[21084+(h<<1)>>1]|0;j=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(Ca=j&31,Da=j>>>5,Ea=(c[11272+(Ca<<2)>>2]|0)+(_(Ca,Da)|0)|0,Ca>>>0<=L>>>0):0){t=Ea+Ca|0;u=280811+t|0;t=280811+(t+-4)|0;g=0;j=280811+Ea|0;while(1){if(j>>>0>t>>>0)break;Ia=k+(K+g)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;g=g+4|0;j=j+4|0}while(1){if(j>>>0>=u>>>0)break;if((a[k+(K+g)>>0]|0)!=(a[j>>0]|0))break;g=g+1|0;j=j+1|0}if(!((g+10|0)>>>0<=Ca>>>0|(g|0)==0)?(Fa=J+Da+1+(d[407930+(Ca-g)>>0]<<d[280786+Ca>>0])|0,Ga=(g*540|0)+3840+(_((aa(Fa|0)|0)^31,-120)|0)|0,Ga>>>0>=(c[fa>>2]|0)>>>0):0){c[pa>>2]=g;c[da>>2]=Ca^g;c[ea>>2]=Fa;c[fa>>2]=Ga;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;h=h+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[R>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[oa>>2]=c[pa>>2];c[oa+4>>2]=c[pa+4>>2];c[oa+8>>2]=c[pa+8>>2];c[oa+12>>2]=c[pa+12>>2];M=M+1|0;if(!((M|0)<4&(m+5|0)>>>0<na>>>0)){x=f;break}else{m=y;x=f}}m=c[oa>>2]|0;u=y+(m<<1)+ja|0;f=c[Q>>2]|0;e:do if(f>>>0<=(y>>>0<ma>>>0?y:ma)>>>0){g=f+3|0;Ia=c[o>>2]|0;j=g-Ia|0;t=c[ia>>2]|0;g=g-t|0;if((f|0)==(Ia|0))f=0;else{f:do if((f|0)!=(t|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(g>>>0<7){f=266017486>>>(g<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[Q>>2];m=c[oa>>2]|0}}else f=f+15|0;while(0);w=O+16|0;Ia=c[P>>2]|0;j=m^Ia;c[O>>2]=x;c[O+4>>2]=m|Ia<<24;m=O+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{N=f+-12|0;Ia=((aa(N|0)|0)^31)+-1|0;Ha=N>>>Ia&1;f=((Ia<<1)+65534|Ha)+16&65535;b[m>>1]=f;m=Ia<<24|N-((Ha|2)<<Ia)}c[O+8>>2]=m;h=f<<16>>16==0;v=O+12|0;do if(x>>>0>=6){if(x>>>0<130){Ia=x+-2|0;g=((aa(Ia|0)|0)^31)+-1|0;g=(g<<1)+(Ia>>>g)+2&65535;break}if(x>>>0<2114){g=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)g=21;else g=x>>>0<22594?22:23}else g=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ia=j+-6|0;f=((aa(Ia|0)|0)^31)+-1|0;f=(f<<1)+(Ia>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=g&65535;t=m&7|j<<3&56;if(h&(g&65535)<8&(f&65535)<16)f=((f&65535)<8?t:t|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|t&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[oa>>2]|0)|0;f=f>>>0<la>>>0?f:la;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&255|Ha<<8)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+1|0}m=y+(c[oa>>2]|0)|0;O=w;f=0}c[p>>2]=f+L;c[r>>2]=(c[r>>2]|0)+(M-q>>4);i=qa;return}function Ra(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0;sa=i;i=i+32|0;pa=sa+16|0;qa=sa;na=(1<<c[m+8>>2])+-16|0;u=c[p>>2]|0;oa=h+g|0;ma=g>>>0>3?oa+-3|0:h;la=m+4|0;ia=c[la>>2]|0;ja=(ia|0)<9?64:512;t=ja+h|0;ka=n+524300|0;c[ka>>2]=((ia|0)>6?7:8)<<ia+-4;if((((h|0)==0^1|j^1)^1)&g>>>0<513){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;ia=(_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17;c[n+(ia<<2)>>2]=-858993460;b[n+131072+(ia<<1)>>1]=-13108;f=f+1|0}ac(n+196608|0,0,65536)|0;b[n+524288>>1]=0;if(g){a[n+524290>>0]=0;ra=8}}else{f=n+524290|0;if(!(a[f>>0]&1))ra=8;else{ac(n|0,-52,131072)|0;b[n+524288>>1]=0;a[f>>0]=0;ac(n+131072|0,0,131072)|0;ra=8}}if((ra|0)==8?g>>>0>2&h>>>0>2:0){da=h+-3|0;ga=k+(da&l)|0;ga=(_(d[ga>>0]|d[ga+1>>0]<<8|d[ga+2>>0]<<16|d[ga+3>>0]<<24,506832829)|0)>>>17;ea=n+524288|0;fa=b[ea>>1]|0;b[ea>>1]=fa+1<<16>>16;ha=fa&65535;ia=n+(ga<<2)|0;ca=da-(c[ia>>2]|0)|0;a[(da&65535)+(n+196608)>>0]=ga;b[n+262144+(ha<<2)>>1]=ca>>>0>65535?65535:ca;ga=n+131072+(ga<<1)|0;b[n+262144+(ha<<2)+2>>1]=b[ga>>1]|0;c[ia>>2]=da;b[ga>>1]=fa;ga=h+-2|0;fa=k+(ga&l)|0;fa=(_(d[fa>>0]|d[fa+1>>0]<<8|d[fa+2>>0]<<16|d[fa+3>>0]<<24,506832829)|0)>>>17;ia=b[ea>>1]|0;b[ea>>1]=ia+1<<16>>16;da=ia&65535;ha=n+(fa<<2)|0;ca=ga-(c[ha>>2]|0)|0;a[(ga&65535)+(n+196608)>>0]=fa;b[n+262144+(da<<2)>>1]=ca>>>0>65535?65535:ca;fa=n+131072+(fa<<1)|0;b[n+262144+(da<<2)+2>>1]=b[fa>>1]|0;c[ha>>2]=ga;b[fa>>1]=ia;fa=h+-1|0;ia=k+(fa&l)|0;ia=(_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17;ha=b[ea>>1]|0;b[ea>>1]=ha+1<<16>>16;ea=ha&65535;ga=n+(ia<<2)|0;da=fa-(c[ga>>2]|0)|0;a[(fa&65535)+(n+196608)>>0]=ia;b[n+262144+(ea<<2)>>1]=da>>>0>65535?65535:da;ia=n+131072+(ia<<1)|0;b[n+262144+(ea<<2)+2>>1]=b[ia>>1]|0;c[ga>>2]=fa;b[ia>>1]=ha}O=pa+4|0;P=pa+8|0;Q=pa+12|0;R=pa+4|0;S=pa+8|0;T=pa+12|0;U=n+524288|0;V=n+524296|0;W=n+524292|0;X=ja<<2;Y=oa+-4|0;Z=oa+-3|0;$=qa+12|0;ba=qa+4|0;ca=qa+8|0;da=qa+4|0;ea=qa+8|0;fa=qa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;N=q;f=u;a:while(1){L=N;M=t+X|0;b:while(1){K=oa-m|0;if((m+4|0)>>>0>=oa>>>0)break a;I=m>>>0<na>>>0?m:na;c[pa>>2]=0;c[O>>2]=0;c[P>>2]=0;c[Q>>2]=4240;J=m&l;z=c[pa>>2]|0;H=k+J|0;B=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;y=B&255;c[pa>>2]=0;c[R>>2]=0;G=J+K|0;F=k+G|0;E=H;G=k+(G+-4)|0;h=4240;j=0;A=0;while(1){if((A|0)==4)break;x=(c[o+(c[11372+(A<<2)>>2]<<2)>>2]|0)+(c[11436+(A<<2)>>2]|0)|0;g=m-x|0;if(!A)if(g>>>0>=m>>>0|x>>>0>I>>>0)g=z;else ra=17;else if((g>>>0<m>>>0?(a[(g&65535)+(n+196608)>>0]|0)==y<<24>>24:0)^1|x>>>0>I>>>0)g=z;else ra=17;if((ra|0)==17){ra=0;w=g&l;g=0;u=E;while(1){v=u;if(v>>>0>G>>>0)break;C=u;D=k+(w+g)|0;if((d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24|0)!=(d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0))break;g=g+4|0;u=v+4|0}while(1){if(u>>>0>=F>>>0)break;if((a[k+(w+g)>>0]|0)!=(a[u>>0]|0))break;g=g+1|0;u=u+1|0}if(g>>>0>1?(ta=(g*540|0)+(c[11500+(A<<2)>>2]|0)|0,h>>>0<ta>>>0):0){c[pa>>2]=g;c[S>>2]=x;c[T>>2]=ta;h=ta;j=1}else g=z}z=g;A=A+1|0}w=0;u=m-(c[n+(B<<2)>>2]|0)|0;v=c[ka>>2]|0;g=e[n+131072+(B<<1)>>1]|0;c:while(1){D=J+z|0;C=D>>>0>l>>>0;D=k+D|0;x=w;y=u;A=v;B=g;while(1){w=A;A=A+-1|0;if(!w)break c;x=x+y|0;if(x>>>0>I>>>0)break c;w=m-x&l;y=B;B=e[n+262144+(B<<2)+2>>1]|0;y=e[n+262144+(y<<2)>>1]|0;if(C)continue;g=w+z|0;if(g>>>0>l>>>0)continue;if((a[D>>0]|0)==(a[k+g>>0]|0)){v=0;g=E}else continue;while(1){u=g;if(u>>>0>G>>>0)break;Ga=g;Fa=k+(w+v)|0;if((d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0)!=(d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0))break;v=v+4|0;g=u+4|0}while(1){if(g>>>0>=F>>>0)break;if((a[k+(w+v)>>0]|0)!=(a[g>>0]|0))break;v=v+1|0;g=g+1|0}if(v>>>0<=3)continue;g=(v*540|0)+3840+(_((aa(x|0)|0)^31,-120)|0)|0;if(h>>>0<g>>>0)break}c[pa>>2]=v;c[S>>2]=x;c[T>>2]=g;w=x;z=v;h=g;u=y;v=A;j=1;g=B}Ga=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;F=Fa&65535;G=n+(Ga<<2)|0;E=m-(c[G>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(F<<2)>>1]=E>>>0>65535?65535:E;Ga=n+131072+(Ga<<1)|0;b[n+262144+(F<<2)+2>>1]=b[Ga>>1]|0;c[G>>2]=m;b[Ga>>1]=Fa;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ga=b[21084+(w<<1)>>1]|0;g=Ga&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ga<<16>>16!=0?(ua=g&31,va=g>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=K>>>0):0){h=wa+ua|0;v=280811+h|0;h=280811+(h+-4)|0;u=0;g=280811+wa|0;while(1){if(g>>>0>h>>>0)break;Ga=k+(J+u)|0;if((d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;u=u+4|0;g=g+4|0}while(1){if(g>>>0>=v>>>0)break;if((a[k+(J+u)>>0]|0)!=(a[g>>0]|0))break;u=u+1|0;g=g+1|0}if(!((u+10|0)>>>0<=ua>>>0|(u|0)==0)?(xa=I+va+1+(d[407930+(ua-u)>>0]<<d[280786+ua>>0])|0,ya=(u*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[T>>2]|0)>>>0):0){c[pa>>2]=u;c[R>>2]=ua^u;c[S>>2]=xa;c[T>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){L=0;x=f;break}f=f+1|0;g=m+1|0;if(g>>>0<=t>>>0){m=g;continue}if(g>>>0>M>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=g;while(1){if(m>>>0>=j>>>0)continue b;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;J=Fa&65535;K=n+(Ga<<2)|0;I=m-(c[K>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(J<<2)>>1]=I>>>0>65535?65535:I;Ga=n+131072+(Ga<<1)|0;b[n+262144+(J<<2)+2>>1]=b[Ga>>1]|0;c[K>>2]=m;b[Ga>>1]=Fa;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=g;while(1){if(m>>>0>=j>>>0)continue b;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;J=Fa&65535;K=n+(Ga<<2)|0;I=m-(c[K>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(J<<2)>>1]=I>>>0>65535?65535:I;Ga=n+131072+(Ga<<1)|0;b[n+262144+(J<<2)+2>>1]=b[Ga>>1]|0;c[K>>2]=m;b[Ga>>1]=Fa;m=m+2|0;f=f+2|0}}}while(1){K=K+-1|0;if((c[la>>2]|0)<5){f=(c[pa>>2]|0)+-1|0;f=f>>>0<K>>>0?f:K}else f=0;c[qa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;I=y>>>0<na>>>0?y:na;J=y&l;z=c[qa>>2]|0;H=k+J|0;B=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;w=B&255;c[qa>>2]=0;c[da>>2]=0;G=J+K|0;F=k+G|0;E=H;G=k+(G+-4)|0;g=4240;f=0;A=0;while(1){if((A|0)==4)break;v=(c[o+(c[11372+(A<<2)>>2]<<2)>>2]|0)+(c[11436+(A<<2)>>2]|0)|0;j=y-v|0;if(!A)if(j>>>0>=y>>>0|v>>>0>I>>>0)j=z;else ra=71;else if((j>>>0<y>>>0?(a[(j&65535)+(n+196608)>>0]|0)==w<<24>>24:0)^1|v>>>0>I>>>0)j=z;else ra=71;if((ra|0)==71){ra=0;u=j&l;j=0;t=E;while(1){h=t;if(h>>>0>G>>>0)break;Fa=t;Ga=k+(u+j)|0;if((d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;j=j+4|0;t=h+4|0}while(1){if(t>>>0>=F>>>0)break;if((a[k+(u+j)>>0]|0)!=(a[t>>0]|0))break;j=j+1|0;t=t+1|0}if(j>>>0>1?(za=(j*540|0)+(c[11500+(A<<2)>>2]|0)|0,g>>>0<za>>>0):0){c[qa>>2]=j;c[ea>>2]=v;c[fa>>2]=za;g=za;f=1}else j=z}z=j;A=A+1|0}u=0;t=y-(c[n+(B<<2)>>2]|0)|0;h=c[ka>>2]|0;j=e[n+131072+(B<<1)>>1]|0;d:while(1){D=J+z|0;C=D>>>0>l>>>0;D=k+D|0;v=u;w=t;A=h;B=j;while(1){Ga=A;A=A+-1|0;if(!Ga)break d;v=v+w|0;if(v>>>0>I>>>0)break d;u=y-v&l;w=B;B=e[n+262144+(B<<2)+2>>1]|0;w=e[n+262144+(w<<2)>>1]|0;if(C)continue;j=u+z|0;if(j>>>0>l>>>0)continue;if((a[D>>0]|0)==(a[k+j>>0]|0)){h=0;j=E}else continue;while(1){t=j;if(t>>>0>G>>>0)break;Fa=j;Ga=k+(u+h)|0;if((d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;h=h+4|0;j=t+4|0}while(1){if(j>>>0>=F>>>0)break;if((a[k+(u+h)>>0]|0)!=(a[j>>0]|0))break;h=h+1|0;j=j+1|0}if(h>>>0<=3)continue;j=(h*540|0)+3840+(_((aa(v|0)|0)^31,-120)|0)|0;if(g>>>0<j>>>0)break}c[qa>>2]=h;c[ea>>2]=v;c[fa>>2]=j;u=v;z=h;g=j;t=w;h=A;f=1;j=B}Ga=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;G=Fa&65535;M=n+(Ga<<2)|0;F=y-(c[M>>2]|0)|0;a[(y&65535)+(n+196608)>>0]=Ga;b[n+262144+(G<<2)>>1]=F>>>0>65535?65535:F;Ga=n+131072+(Ga<<1)|0;b[n+262144+(G<<2)+2>>1]=b[Ga>>1]|0;c[M>>2]=y;b[Ga>>1]=Fa;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;u=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ga=b[21084+(u<<1)>>1]|0;j=Ga&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ga<<16>>16!=0?(Aa=j&31,Ba=j>>>5,Ca=(c[11272+(Aa<<2)>>2]|0)+(_(Aa,Ba)|0)|0,Aa>>>0<=K>>>0):0){g=Ca+Aa|0;h=280811+g|0;g=280811+(g+-4)|0;t=0;j=280811+Ca|0;while(1){if(j>>>0>g>>>0)break;Ga=k+(J+t)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;t=t+4|0;j=j+4|0}while(1){if(j>>>0>=h>>>0)break;if((a[k+(J+t)>>0]|0)!=(a[j>>0]|0))break;t=t+1|0;j=j+1|0}if(!((t+10|0)>>>0<=Aa>>>0|(t|0)==0)?(Da=I+Ba+1+(d[407930+(Aa-t)>>0]<<d[280786+Aa>>0])|0,Ea=(t*540|0)+3840+(_((aa(Da|0)|0)^31,-120)|0)|0,Ea>>>0>=(c[fa>>2]|0)>>>0):0){c[qa>>2]=t;c[da>>2]=Aa^t;c[ea>>2]=Da;c[fa>>2]=Ea;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;u=u+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[Q>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[pa>>2]=c[qa>>2];c[pa+4>>2]=c[qa+4>>2];c[pa+8>>2]=c[qa+8>>2];c[pa+12>>2]=c[qa+12>>2];L=L+1|0;if(!((L|0)<4&(m+5|0)>>>0<oa>>>0)){x=f;break}else{m=y;x=f}}m=c[pa>>2]|0;t=y+(m<<1)+ja|0;f=c[P>>2]|0;e:do if(f>>>0<=(y>>>0<na>>>0?y:na)>>>0){h=f+3|0;Ga=c[o>>2]|0;j=h-Ga|0;g=c[ia>>2]|0;h=h-g|0;if((f|0)==(Ga|0))f=0;else{f:do if((f|0)!=(g|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(h>>>0<7){f=266017486>>>(h<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[P>>2];m=c[pa>>2]|0}}else f=f+15|0;while(0);w=N+16|0;Ga=c[O>>2]|0;j=m^Ga;c[N>>2]=x;c[N+4>>2]=m|Ga<<24;m=N+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{M=f+-12|0;Ga=((aa(M|0)|0)^31)+-1|0;Fa=M>>>Ga&1;f=((Ga<<1)+65534|Fa)+16&65535;b[m>>1]=f;m=Ga<<24|M-((Fa|2)<<Ga)}c[N+8>>2]=m;u=f<<16>>16==0;v=N+12|0;do if(x>>>0>=6){if(x>>>0<130){Ga=x+-2|0;h=((aa(Ga|0)|0)^31)+-1|0;h=(h<<1)+(Ga>>>h)+2&65535;break}if(x>>>0<2114){h=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)h=21;else h=x>>>0<22594?22:23}else h=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ga=j+-6|0;f=((aa(Ga|0)|0)^31)+-1|0;f=(f<<1)+(Ga>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=h&65535;g=m&7|j<<3&56;if(u&(h&65535)<8&(f&65535)<16)f=((f&65535)<8?g:g|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|g&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[pa>>2]|0)|0;f=f>>>0<ma>>>0?f:ma;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;M=Fa&65535;N=n+(Ga<<2)|0;L=m-(c[N>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(M<<2)>>1]=L>>>0>65535?65535:L;Ga=n+131072+(Ga<<1)|0;b[n+262144+(M<<2)+2>>1]=b[Ga>>1]|0;c[N>>2]=m;b[Ga>>1]=Fa;m=m+1|0}m=y+(c[pa>>2]|0)|0;N=w;f=0}c[p>>2]=f+K;c[r>>2]=(c[r>>2]|0)+(L-q>>4);i=sa;return}function Sa(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0;sa=i;i=i+32|0;pa=sa+16|0;qa=sa;na=(1<<c[m+8>>2])+-16|0;u=c[p>>2]|0;oa=h+g|0;ma=g>>>0>3?oa+-3|0:h;la=m+4|0;ia=c[la>>2]|0;ja=(ia|0)<9?64:512;t=ja+h|0;ka=n+524300|0;c[ka>>2]=((ia|0)>6?7:8)<<ia+-4;if((((h|0)==0^1|j^1)^1)&g>>>0<513){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;ia=(_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17;c[n+(ia<<2)>>2]=-858993460;b[n+131072+(ia<<1)>>1]=-13108;f=f+1|0}ac(n+196608|0,0,65536)|0;b[n+524288>>1]=0;if(g){a[n+524290>>0]=0;ra=8}}else{f=n+524290|0;if(!(a[f>>0]&1))ra=8;else{ac(n|0,-52,131072)|0;b[n+524288>>1]=0;a[f>>0]=0;ac(n+131072|0,0,131072)|0;ra=8}}if((ra|0)==8?g>>>0>2&h>>>0>2:0){da=h+-3|0;ga=k+(da&l)|0;ga=(_(d[ga>>0]|d[ga+1>>0]<<8|d[ga+2>>0]<<16|d[ga+3>>0]<<24,506832829)|0)>>>17;ea=n+524288|0;fa=b[ea>>1]|0;b[ea>>1]=fa+1<<16>>16;ha=fa&65535;ia=n+(ga<<2)|0;ca=da-(c[ia>>2]|0)|0;a[(da&65535)+(n+196608)>>0]=ga;b[n+262144+(ha<<2)>>1]=ca>>>0>65535?65535:ca;ga=n+131072+(ga<<1)|0;b[n+262144+(ha<<2)+2>>1]=b[ga>>1]|0;c[ia>>2]=da;b[ga>>1]=fa;ga=h+-2|0;fa=k+(ga&l)|0;fa=(_(d[fa>>0]|d[fa+1>>0]<<8|d[fa+2>>0]<<16|d[fa+3>>0]<<24,506832829)|0)>>>17;ia=b[ea>>1]|0;b[ea>>1]=ia+1<<16>>16;da=ia&65535;ha=n+(fa<<2)|0;ca=ga-(c[ha>>2]|0)|0;a[(ga&65535)+(n+196608)>>0]=fa;b[n+262144+(da<<2)>>1]=ca>>>0>65535?65535:ca;fa=n+131072+(fa<<1)|0;b[n+262144+(da<<2)+2>>1]=b[fa>>1]|0;c[ha>>2]=ga;b[fa>>1]=ia;fa=h+-1|0;ia=k+(fa&l)|0;ia=(_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17;ha=b[ea>>1]|0;b[ea>>1]=ha+1<<16>>16;ea=ha&65535;ga=n+(ia<<2)|0;da=fa-(c[ga>>2]|0)|0;a[(fa&65535)+(n+196608)>>0]=ia;b[n+262144+(ea<<2)>>1]=da>>>0>65535?65535:da;ia=n+131072+(ia<<1)|0;b[n+262144+(ea<<2)+2>>1]=b[ia>>1]|0;c[ga>>2]=fa;b[ia>>1]=ha}O=pa+4|0;P=pa+8|0;Q=pa+12|0;R=pa+4|0;S=pa+8|0;T=pa+12|0;U=n+524288|0;V=n+524296|0;W=n+524292|0;X=ja<<2;Y=oa+-4|0;Z=oa+-3|0;$=qa+12|0;ba=qa+4|0;ca=qa+8|0;da=qa+4|0;ea=qa+8|0;fa=qa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;N=q;f=u;a:while(1){L=N;M=t+X|0;b:while(1){K=oa-m|0;if((m+4|0)>>>0>=oa>>>0)break a;I=m>>>0<na>>>0?m:na;c[pa>>2]=0;c[O>>2]=0;c[P>>2]=0;c[Q>>2]=4240;J=m&l;z=c[pa>>2]|0;H=k+J|0;B=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;y=B&255;c[pa>>2]=0;c[R>>2]=0;G=J+K|0;F=k+G|0;E=H;G=k+(G+-4)|0;h=4240;j=0;A=0;while(1){if((A|0)==10)break;x=(c[o+(c[11372+(A<<2)>>2]<<2)>>2]|0)+(c[11436+(A<<2)>>2]|0)|0;g=m-x|0;if(!A)if(g>>>0>=m>>>0|x>>>0>I>>>0)g=z;else ra=17;else if((g>>>0<m>>>0?(a[(g&65535)+(n+196608)>>0]|0)==y<<24>>24:0)^1|x>>>0>I>>>0)g=z;else ra=17;if((ra|0)==17){ra=0;w=g&l;g=0;u=E;while(1){v=u;if(v>>>0>G>>>0)break;C=u;D=k+(w+g)|0;if((d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24|0)!=(d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0))break;g=g+4|0;u=v+4|0}while(1){if(u>>>0>=F>>>0)break;if((a[k+(w+g)>>0]|0)!=(a[u>>0]|0))break;g=g+1|0;u=u+1|0}if(g>>>0>1?(ta=(g*540|0)+(c[11500+(A<<2)>>2]|0)|0,h>>>0<ta>>>0):0){c[pa>>2]=g;c[S>>2]=x;c[T>>2]=ta;h=ta;j=1}else g=z}z=g;A=A+1|0}w=0;u=m-(c[n+(B<<2)>>2]|0)|0;v=c[ka>>2]|0;g=e[n+131072+(B<<1)>>1]|0;c:while(1){D=J+z|0;C=D>>>0>l>>>0;D=k+D|0;x=w;y=u;A=v;B=g;while(1){w=A;A=A+-1|0;if(!w)break c;x=x+y|0;if(x>>>0>I>>>0)break c;w=m-x&l;y=B;B=e[n+262144+(B<<2)+2>>1]|0;y=e[n+262144+(y<<2)>>1]|0;if(C)continue;g=w+z|0;if(g>>>0>l>>>0)continue;if((a[D>>0]|0)==(a[k+g>>0]|0)){v=0;g=E}else continue;while(1){u=g;if(u>>>0>G>>>0)break;Ga=g;Fa=k+(w+v)|0;if((d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0)!=(d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0))break;v=v+4|0;g=u+4|0}while(1){if(g>>>0>=F>>>0)break;if((a[k+(w+v)>>0]|0)!=(a[g>>0]|0))break;v=v+1|0;g=g+1|0}if(v>>>0<=3)continue;g=(v*540|0)+3840+(_((aa(x|0)|0)^31,-120)|0)|0;if(h>>>0<g>>>0)break}c[pa>>2]=v;c[S>>2]=x;c[T>>2]=g;w=x;z=v;h=g;u=y;v=A;j=1;g=B}Ga=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;F=Fa&65535;G=n+(Ga<<2)|0;E=m-(c[G>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(F<<2)>>1]=E>>>0>65535?65535:E;Ga=n+131072+(Ga<<1)|0;b[n+262144+(F<<2)+2>>1]=b[Ga>>1]|0;c[G>>2]=m;b[Ga>>1]=Fa;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ga=b[21084+(w<<1)>>1]|0;g=Ga&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ga<<16>>16!=0?(ua=g&31,va=g>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=K>>>0):0){h=wa+ua|0;v=280811+h|0;h=280811+(h+-4)|0;u=0;g=280811+wa|0;while(1){if(g>>>0>h>>>0)break;Ga=k+(J+u)|0;if((d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;u=u+4|0;g=g+4|0}while(1){if(g>>>0>=v>>>0)break;if((a[k+(J+u)>>0]|0)!=(a[g>>0]|0))break;u=u+1|0;g=g+1|0}if(!((u+10|0)>>>0<=ua>>>0|(u|0)==0)?(xa=I+va+1+(d[407930+(ua-u)>>0]<<d[280786+ua>>0])|0,ya=(u*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[T>>2]|0)>>>0):0){c[pa>>2]=u;c[R>>2]=ua^u;c[S>>2]=xa;c[T>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){L=0;x=f;break}f=f+1|0;g=m+1|0;if(g>>>0<=t>>>0){m=g;continue}if(g>>>0>M>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=g;while(1){if(m>>>0>=j>>>0)continue b;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;J=Fa&65535;K=n+(Ga<<2)|0;I=m-(c[K>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(J<<2)>>1]=I>>>0>65535?65535:I;Ga=n+131072+(Ga<<1)|0;b[n+262144+(J<<2)+2>>1]=b[Ga>>1]|0;c[K>>2]=m;b[Ga>>1]=Fa;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=g;while(1){if(m>>>0>=j>>>0)continue b;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;J=Fa&65535;K=n+(Ga<<2)|0;I=m-(c[K>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(J<<2)>>1]=I>>>0>65535?65535:I;Ga=n+131072+(Ga<<1)|0;b[n+262144+(J<<2)+2>>1]=b[Ga>>1]|0;c[K>>2]=m;b[Ga>>1]=Fa;m=m+2|0;f=f+2|0}}}while(1){K=K+-1|0;if((c[la>>2]|0)<5){f=(c[pa>>2]|0)+-1|0;f=f>>>0<K>>>0?f:K}else f=0;c[qa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;I=y>>>0<na>>>0?y:na;J=y&l;z=c[qa>>2]|0;H=k+J|0;B=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;w=B&255;c[qa>>2]=0;c[da>>2]=0;G=J+K|0;F=k+G|0;E=H;G=k+(G+-4)|0;g=4240;f=0;A=0;while(1){if((A|0)==10)break;v=(c[o+(c[11372+(A<<2)>>2]<<2)>>2]|0)+(c[11436+(A<<2)>>2]|0)|0;j=y-v|0;if(!A)if(j>>>0>=y>>>0|v>>>0>I>>>0)j=z;else ra=71;else if((j>>>0<y>>>0?(a[(j&65535)+(n+196608)>>0]|0)==w<<24>>24:0)^1|v>>>0>I>>>0)j=z;else ra=71;if((ra|0)==71){ra=0;u=j&l;j=0;t=E;while(1){h=t;if(h>>>0>G>>>0)break;Fa=t;Ga=k+(u+j)|0;if((d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;j=j+4|0;t=h+4|0}while(1){if(t>>>0>=F>>>0)break;if((a[k+(u+j)>>0]|0)!=(a[t>>0]|0))break;j=j+1|0;t=t+1|0}if(j>>>0>1?(za=(j*540|0)+(c[11500+(A<<2)>>2]|0)|0,g>>>0<za>>>0):0){c[qa>>2]=j;c[ea>>2]=v;c[fa>>2]=za;g=za;f=1}else j=z}z=j;A=A+1|0}u=0;t=y-(c[n+(B<<2)>>2]|0)|0;h=c[ka>>2]|0;j=e[n+131072+(B<<1)>>1]|0;d:while(1){D=J+z|0;C=D>>>0>l>>>0;D=k+D|0;v=u;w=t;A=h;B=j;while(1){Ga=A;A=A+-1|0;if(!Ga)break d;v=v+w|0;if(v>>>0>I>>>0)break d;u=y-v&l;w=B;B=e[n+262144+(B<<2)+2>>1]|0;w=e[n+262144+(w<<2)>>1]|0;if(C)continue;j=u+z|0;if(j>>>0>l>>>0)continue;if((a[D>>0]|0)==(a[k+j>>0]|0)){h=0;j=E}else continue;while(1){t=j;if(t>>>0>G>>>0)break;Fa=j;Ga=k+(u+h)|0;if((d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;h=h+4|0;j=t+4|0}while(1){if(j>>>0>=F>>>0)break;if((a[k+(u+h)>>0]|0)!=(a[j>>0]|0))break;h=h+1|0;j=j+1|0}if(h>>>0<=3)continue;j=(h*540|0)+3840+(_((aa(v|0)|0)^31,-120)|0)|0;if(g>>>0<j>>>0)break}c[qa>>2]=h;c[ea>>2]=v;c[fa>>2]=j;u=v;z=h;g=j;t=w;h=A;f=1;j=B}Ga=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;G=Fa&65535;M=n+(Ga<<2)|0;F=y-(c[M>>2]|0)|0;a[(y&65535)+(n+196608)>>0]=Ga;b[n+262144+(G<<2)>>1]=F>>>0>65535?65535:F;Ga=n+131072+(Ga<<1)|0;b[n+262144+(G<<2)+2>>1]=b[Ga>>1]|0;c[M>>2]=y;b[Ga>>1]=Fa;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;u=(_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ga=b[21084+(u<<1)>>1]|0;j=Ga&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ga<<16>>16!=0?(Aa=j&31,Ba=j>>>5,Ca=(c[11272+(Aa<<2)>>2]|0)+(_(Aa,Ba)|0)|0,Aa>>>0<=K>>>0):0){g=Ca+Aa|0;h=280811+g|0;g=280811+(g+-4)|0;t=0;j=280811+Ca|0;while(1){if(j>>>0>g>>>0)break;Ga=k+(J+t)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;t=t+4|0;j=j+4|0}while(1){if(j>>>0>=h>>>0)break;if((a[k+(J+t)>>0]|0)!=(a[j>>0]|0))break;t=t+1|0;j=j+1|0}if(!((t+10|0)>>>0<=Aa>>>0|(t|0)==0)?(Da=I+Ba+1+(d[407930+(Aa-t)>>0]<<d[280786+Aa>>0])|0,Ea=(t*540|0)+3840+(_((aa(Da|0)|0)^31,-120)|0)|0,Ea>>>0>=(c[fa>>2]|0)>>>0):0){c[qa>>2]=t;c[da>>2]=Aa^t;c[ea>>2]=Da;c[fa>>2]=Ea;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;u=u+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[Q>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[pa>>2]=c[qa>>2];c[pa+4>>2]=c[qa+4>>2];c[pa+8>>2]=c[qa+8>>2];c[pa+12>>2]=c[qa+12>>2];L=L+1|0;if(!((L|0)<4&(m+5|0)>>>0<oa>>>0)){x=f;break}else{m=y;x=f}}m=c[pa>>2]|0;t=y+(m<<1)+ja|0;f=c[P>>2]|0;e:do if(f>>>0<=(y>>>0<na>>>0?y:na)>>>0){h=f+3|0;Ga=c[o>>2]|0;j=h-Ga|0;g=c[ia>>2]|0;h=h-g|0;if((f|0)==(Ga|0))f=0;else{f:do if((f|0)!=(g|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(h>>>0<7){f=266017486>>>(h<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[P>>2];m=c[pa>>2]|0}}else f=f+15|0;while(0);w=N+16|0;Ga=c[O>>2]|0;j=m^Ga;c[N>>2]=x;c[N+4>>2]=m|Ga<<24;m=N+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{M=f+-12|0;Ga=((aa(M|0)|0)^31)+-1|0;Fa=M>>>Ga&1;f=((Ga<<1)+65534|Fa)+16&65535;b[m>>1]=f;m=Ga<<24|M-((Fa|2)<<Ga)}c[N+8>>2]=m;u=f<<16>>16==0;v=N+12|0;do if(x>>>0>=6){if(x>>>0<130){Ga=x+-2|0;h=((aa(Ga|0)|0)^31)+-1|0;h=(h<<1)+(Ga>>>h)+2&65535;break}if(x>>>0<2114){h=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)h=21;else h=x>>>0<22594?22:23}else h=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ga=j+-6|0;f=((aa(Ga|0)|0)^31)+-1|0;f=(f<<1)+(Ga>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=h&65535;g=m&7|j<<3&56;if(u&(h&65535)<8&(f&65535)<16)f=((f&65535)<8?g:g|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|g&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[pa>>2]|0)|0;f=f>>>0<ma>>>0?f:ma;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;Fa=b[U>>1]|0;b[U>>1]=Fa+1<<16>>16;M=Fa&65535;N=n+(Ga<<2)|0;L=m-(c[N>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(M<<2)>>1]=L>>>0>65535?65535:L;Ga=n+131072+(Ga<<1)|0;b[n+262144+(M<<2)+2>>1]=b[Ga>>1]|0;c[N>>2]=m;b[Ga>>1]=Fa;m=m+1|0}m=y+(c[pa>>2]|0)|0;N=w;f=0}c[p>>2]=f+K;c[r>>2]=(c[r>>2]|0)+(L-q>>4);i=sa;return}function Ta(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0;ra=i;i=i+32|0;pa=ra+16|0;qa=ra;na=(1<<c[m+8>>2])+-16|0;u=c[p>>2]|0;oa=h+g|0;ma=g>>>0>3?oa+-3|0:h;la=m+4|0;ia=c[la>>2]|0;ja=(ia|0)<9?64:512;t=ja+h|0;ka=n+1311756|0;c[ka>>2]=((ia|0)>6?7:8)<<ia+-4;if((((h|0)==0^1|j^1)^1)&g>>>0<513){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;ia=(_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17;c[n+(ia<<2)>>2]=-858993460;b[n+131072+(ia<<1)>>1]=-13108;f=f+1|0}ac(n+196608|0,0,65536)|0;ac(n+1310720|0,0,1024)|0;if(g){a[n+1311744>>0]=0;Ea=8}}else if(!(a[n+1311744>>0]&1))Ea=8;else{ac(n|0,-52,131072)|0;ac(n+131072|0,0,131072)|0;ac(n+1310720|0,0,1025)|0;Ea=8}if((Ea|0)==8?g>>>0>2&h>>>0>2:0){ea=h+-3|0;ga=k+(ea&l)|0;ga=(_(d[ga>>0]|d[ga+1>>0]<<8|d[ga+2>>0]<<16|d[ga+3>>0]<<24,506832829)|0)>>>17;ia=ga&511;ha=n+1310720+(ia<<1)|0;fa=b[ha>>1]|0;b[ha>>1]=fa+1<<16>>16;fa=fa&511;ha=n+(ga<<2)|0;da=ea-(c[ha>>2]|0)|0;a[(ea&65535)+(n+196608)>>0]=ga;b[n+262144+(ia<<11)+(fa<<2)>>1]=da>>>0>65535?65535:da;ga=n+131072+(ga<<1)|0;b[n+262144+(ia<<11)+(fa<<2)+2>>1]=b[ga>>1]|0;c[ha>>2]=ea;b[ga>>1]=fa;ga=h+-2|0;fa=k+(ga&l)|0;fa=(_(d[fa>>0]|d[fa+1>>0]<<8|d[fa+2>>0]<<16|d[fa+3>>0]<<24,506832829)|0)>>>17;ha=fa&511;ea=n+1310720+(ha<<1)|0;ia=b[ea>>1]|0;b[ea>>1]=ia+1<<16>>16;ia=ia&511;ea=n+(fa<<2)|0;da=ga-(c[ea>>2]|0)|0;a[(ga&65535)+(n+196608)>>0]=fa;b[n+262144+(ha<<11)+(ia<<2)>>1]=da>>>0>65535?65535:da;fa=n+131072+(fa<<1)|0;b[n+262144+(ha<<11)+(ia<<2)+2>>1]=b[fa>>1]|0;c[ea>>2]=ga;b[fa>>1]=ia;fa=h+-1|0;ia=k+(fa&l)|0;ia=(_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17;ea=ia&511;ga=n+1310720+(ea<<1)|0;ha=b[ga>>1]|0;b[ga>>1]=ha+1<<16>>16;ha=ha&511;ga=n+(ia<<2)|0;da=fa-(c[ga>>2]|0)|0;a[(fa&65535)+(n+196608)>>0]=ia;b[n+262144+(ea<<11)+(ha<<2)>>1]=da>>>0>65535?65535:da;ia=n+131072+(ia<<1)|0;b[n+262144+(ea<<11)+(ha<<2)+2>>1]=b[ia>>1]|0;c[ga>>2]=fa;b[ia>>1]=ha}P=pa+4|0;Q=pa+8|0;R=pa+12|0;S=pa+4|0;T=pa+8|0;U=pa+12|0;V=n+1311752|0;W=n+1311748|0;X=ja<<2;Y=oa+-4|0;Z=oa+-3|0;$=qa+12|0;ba=qa+4|0;ca=qa+8|0;da=qa+4|0;ea=qa+8|0;fa=qa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;O=q;f=u;a:while(1){M=O;N=t+X|0;b:while(1){L=oa-m|0;if((m+4|0)>>>0>=oa>>>0)break a;J=m>>>0<na>>>0?m:na;c[pa>>2]=0;c[P>>2]=0;c[Q>>2]=0;c[R>>2]=4240;K=m&l;A=c[pa>>2]|0;I=k+K|0;B=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;y=B&255;c[pa>>2]=0;c[S>>2]=0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;h=4240;j=0;z=0;while(1){if((z|0)==16)break;x=(c[o+(c[11372+(z<<2)>>2]<<2)>>2]|0)+(c[11436+(z<<2)>>2]|0)|0;g=m-x|0;if(!z)if(g>>>0>=m>>>0|x>>>0>J>>>0)g=A;else Ea=17;else if((g>>>0<m>>>0?(a[(g&65535)+(n+196608)>>0]|0)==y<<24>>24:0)^1|x>>>0>J>>>0)g=A;else Ea=17;if((Ea|0)==17){Ea=0;w=g&l;g=0;u=F;while(1){v=u;if(v>>>0>H>>>0)break;D=u;E=k+(w+g)|0;if((d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;g=g+4|0;u=v+4|0}while(1){if(u>>>0>=G>>>0)break;if((a[k+(w+g)>>0]|0)!=(a[u>>0]|0))break;g=g+1|0;u=u+1|0}if(g>>>0>1?(sa=(g*540|0)+(c[11500+(z<<2)>>2]|0)|0,h>>>0<sa>>>0):0){c[pa>>2]=g;c[T>>2]=x;c[U>>2]=sa;h=sa;j=1}else g=A}A=g;z=z+1|0}E=B&511;w=0;u=m-(c[n+(B<<2)>>2]|0)|0;v=c[ka>>2]|0;g=e[n+131072+(B<<1)>>1]|0;c:while(1){D=K+A|0;C=D>>>0>l>>>0;D=k+D|0;x=w;y=u;z=v;B=g;while(1){w=z;z=z+-1|0;if(!w)break c;x=x+y|0;if(x>>>0>J>>>0)break c;w=m-x&l;y=B;B=e[n+262144+(E<<11)+(B<<2)+2>>1]|0;y=e[n+262144+(E<<11)+(y<<2)>>1]|0;if(C)continue;g=w+A|0;if(g>>>0>l>>>0)continue;if((a[D>>0]|0)==(a[k+g>>0]|0)){v=0;g=F}else continue;while(1){u=g;if(u>>>0>H>>>0)break;Ga=g;Fa=k+(w+v)|0;if((d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0)!=(d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0))break;v=v+4|0;g=u+4|0}while(1){if(g>>>0>=G>>>0)break;if((a[k+(w+v)>>0]|0)!=(a[g>>0]|0))break;v=v+1|0;g=g+1|0}if(v>>>0<=3)continue;g=(v*540|0)+3840+(_((aa(x|0)|0)^31,-120)|0)|0;if(h>>>0<g>>>0)break}c[pa>>2]=v;c[T>>2]=x;c[U>>2]=g;w=x;A=v;h=g;u=y;v=z;j=1;g=B}Ga=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;G=Ga&511;H=n+1310720+(G<<1)|0;Fa=b[H>>1]|0;b[H>>1]=Fa+1<<16>>16;Fa=Fa&511;H=n+(Ga<<2)|0;F=m-(c[H>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(G<<11)+(Fa<<2)>>1]=F>>>0>65535?65535:F;Ga=n+131072+(Ga<<1)|0;b[n+262144+(G<<11)+(Fa<<2)+2>>1]=b[Ga>>1]|0;c[H>>2]=m;b[Ga>>1]=Fa;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ga=b[21084+(w<<1)>>1]|0;g=Ga&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ga<<16>>16!=0?(ta=g&31,ua=g>>>5,va=(c[11272+(ta<<2)>>2]|0)+(_(ta,ua)|0)|0,ta>>>0<=L>>>0):0){h=va+ta|0;v=280811+h|0;h=280811+(h+-4)|0;u=0;g=280811+va|0;while(1){if(g>>>0>h>>>0)break;Ga=k+(K+u)|0;if((d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;u=u+4|0;g=g+4|0}while(1){if(g>>>0>=v>>>0)break;if((a[k+(K+u)>>0]|0)!=(a[g>>0]|0))break;u=u+1|0;g=g+1|0}if(!((u+10|0)>>>0<=ta>>>0|(u|0)==0)?(wa=J+ua+1+(d[407930+(ta-u)>>0]<<d[280786+ta>>0])|0,xa=(u*540|0)+3840+(_((aa(wa|0)|0)^31,-120)|0)|0,xa>>>0>=(c[U>>2]|0)>>>0):0){c[pa>>2]=u;c[S>>2]=ta^u;c[T>>2]=wa;c[U>>2]=xa;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){M=0;x=f;break}f=f+1|0;g=m+1|0;if(g>>>0<=t>>>0){m=g;continue}if(g>>>0>N>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=g;while(1){if(m>>>0>=j>>>0)continue b;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;K=Ga&511;L=n+1310720+(K<<1)|0;Fa=b[L>>1]|0;b[L>>1]=Fa+1<<16>>16;Fa=Fa&511;L=n+(Ga<<2)|0;J=m-(c[L>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(K<<11)+(Fa<<2)>>1]=J>>>0>65535?65535:J;Ga=n+131072+(Ga<<1)|0;b[n+262144+(K<<11)+(Fa<<2)+2>>1]=b[Ga>>1]|0;c[L>>2]=m;b[Ga>>1]=Fa;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=g;while(1){if(m>>>0>=j>>>0)continue b;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;K=Ga&511;L=n+1310720+(K<<1)|0;Fa=b[L>>1]|0;b[L>>1]=Fa+1<<16>>16;Fa=Fa&511;L=n+(Ga<<2)|0;J=m-(c[L>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(K<<11)+(Fa<<2)>>1]=J>>>0>65535?65535:J;Ga=n+131072+(Ga<<1)|0;b[n+262144+(K<<11)+(Fa<<2)+2>>1]=b[Ga>>1]|0;c[L>>2]=m;b[Ga>>1]=Fa;m=m+2|0;f=f+2|0}}}while(1){L=L+-1|0;if((c[la>>2]|0)<5){f=(c[pa>>2]|0)+-1|0;f=f>>>0<L>>>0?f:L}else f=0;c[qa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;J=y>>>0<na>>>0?y:na;K=y&l;A=c[qa>>2]|0;I=k+K|0;B=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;w=B&255;c[qa>>2]=0;c[da>>2]=0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;g=4240;f=0;z=0;while(1){if((z|0)==16)break;v=(c[o+(c[11372+(z<<2)>>2]<<2)>>2]|0)+(c[11436+(z<<2)>>2]|0)|0;j=y-v|0;if(!z)if(j>>>0>=y>>>0|v>>>0>J>>>0)j=A;else Ea=71;else if((j>>>0<y>>>0?(a[(j&65535)+(n+196608)>>0]|0)==w<<24>>24:0)^1|v>>>0>J>>>0)j=A;else Ea=71;if((Ea|0)==71){Ea=0;u=j&l;j=0;t=F;while(1){h=t;if(h>>>0>H>>>0)break;Fa=t;Ga=k+(u+j)|0;if((d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;j=j+4|0;t=h+4|0}while(1){if(t>>>0>=G>>>0)break;if((a[k+(u+j)>>0]|0)!=(a[t>>0]|0))break;j=j+1|0;t=t+1|0}if(j>>>0>1?(ya=(j*540|0)+(c[11500+(z<<2)>>2]|0)|0,g>>>0<ya>>>0):0){c[qa>>2]=j;c[ea>>2]=v;c[fa>>2]=ya;g=ya;f=1}else j=A}A=j;z=z+1|0}E=B&511;u=0;t=y-(c[n+(B<<2)>>2]|0)|0;h=c[ka>>2]|0;j=e[n+131072+(B<<1)>>1]|0;d:while(1){D=K+A|0;C=D>>>0>l>>>0;D=k+D|0;v=u;w=t;z=h;B=j;while(1){Ga=z;z=z+-1|0;if(!Ga)break d;v=v+w|0;if(v>>>0>J>>>0)break d;u=y-v&l;w=B;B=e[n+262144+(E<<11)+(B<<2)+2>>1]|0;w=e[n+262144+(E<<11)+(w<<2)>>1]|0;if(C)continue;j=u+A|0;if(j>>>0>l>>>0)continue;if((a[D>>0]|0)==(a[k+j>>0]|0)){h=0;j=F}else continue;while(1){t=j;if(t>>>0>H>>>0)break;Fa=j;Ga=k+(u+h)|0;if((d[Fa>>0]|d[Fa+1>>0]<<8|d[Fa+2>>0]<<16|d[Fa+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;h=h+4|0;j=t+4|0}while(1){if(j>>>0>=G>>>0)break;if((a[k+(u+h)>>0]|0)!=(a[j>>0]|0))break;h=h+1|0;j=j+1|0}if(h>>>0<=3)continue;j=(h*540|0)+3840+(_((aa(v|0)|0)^31,-120)|0)|0;if(g>>>0<j>>>0)break}c[qa>>2]=h;c[ea>>2]=v;c[fa>>2]=j;u=v;A=h;g=j;t=w;h=z;f=1;j=B}Ga=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;H=Ga&511;N=n+1310720+(H<<1)|0;Fa=b[N>>1]|0;b[N>>1]=Fa+1<<16>>16;Fa=Fa&511;N=n+(Ga<<2)|0;G=y-(c[N>>2]|0)|0;a[(y&65535)+(n+196608)>>0]=Ga;b[n+262144+(H<<11)+(Fa<<2)>>1]=G>>>0>65535?65535:G;Ga=n+131072+(Ga<<1)|0;b[n+262144+(H<<11)+(Fa<<2)+2>>1]=b[Ga>>1]|0;c[N>>2]=y;b[Ga>>1]=Fa;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;u=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ga=b[21084+(u<<1)>>1]|0;j=Ga&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ga<<16>>16!=0?(za=j&31,Aa=j>>>5,Ba=(c[11272+(za<<2)>>2]|0)+(_(za,Aa)|0)|0,za>>>0<=L>>>0):0){g=Ba+za|0;h=280811+g|0;g=280811+(g+-4)|0;t=0;j=280811+Ba|0;while(1){if(j>>>0>g>>>0)break;Ga=k+(K+t)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24|0))break;t=t+4|0;j=j+4|0}while(1){if(j>>>0>=h>>>0)break;if((a[k+(K+t)>>0]|0)!=(a[j>>0]|0))break;t=t+1|0;j=j+1|0}if(!((t+10|0)>>>0<=za>>>0|(t|0)==0)?(Ca=J+Aa+1+(d[407930+(za-t)>>0]<<d[280786+za>>0])|0,Da=(t*540|0)+3840+(_((aa(Ca|0)|0)^31,-120)|0)|0,Da>>>0>=(c[fa>>2]|0)>>>0):0){c[qa>>2]=t;c[da>>2]=za^t;c[ea>>2]=Ca;c[fa>>2]=Da;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;u=u+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[R>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[pa>>2]=c[qa>>2];c[pa+4>>2]=c[qa+4>>2];c[pa+8>>2]=c[qa+8>>2];c[pa+12>>2]=c[qa+12>>2];M=M+1|0;if(!((M|0)<4&(m+5|0)>>>0<oa>>>0)){x=f;break}else{m=y;x=f}}m=c[pa>>2]|0;t=y+(m<<1)+ja|0;f=c[Q>>2]|0;e:do if(f>>>0<=(y>>>0<na>>>0?y:na)>>>0){h=f+3|0;Ga=c[o>>2]|0;j=h-Ga|0;g=c[ia>>2]|0;h=h-g|0;if((f|0)==(Ga|0))f=0;else{f:do if((f|0)!=(g|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(h>>>0<7){f=266017486>>>(h<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[Q>>2];m=c[pa>>2]|0}}else f=f+15|0;while(0);w=O+16|0;Ga=c[P>>2]|0;j=m^Ga;c[O>>2]=x;c[O+4>>2]=m|Ga<<24;m=O+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{N=f+-12|0;Ga=((aa(N|0)|0)^31)+-1|0;Fa=N>>>Ga&1;f=((Ga<<1)+65534|Fa)+16&65535;b[m>>1]=f;m=Ga<<24|N-((Fa|2)<<Ga)}c[O+8>>2]=m;u=f<<16>>16==0;v=O+12|0;do if(x>>>0>=6){if(x>>>0<130){Ga=x+-2|0;h=((aa(Ga|0)|0)^31)+-1|0;h=(h<<1)+(Ga>>>h)+2&65535;break}if(x>>>0<2114){h=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)h=21;else h=x>>>0<22594?22:23}else h=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ga=j+-6|0;f=((aa(Ga|0)|0)^31)+-1|0;f=(f<<1)+(Ga>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=h&65535;g=m&7|j<<3&56;if(u&(h&65535)<8&(f&65535)<16)f=((f&65535)<8?g:g|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|g&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[pa>>2]|0)|0;f=f>>>0<ma>>>0?f:ma;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ga=k+(m&l)|0;Ga=(_(d[Ga>>0]|d[Ga+1>>0]<<8|d[Ga+2>>0]<<16|d[Ga+3>>0]<<24,506832829)|0)>>>17;N=Ga&511;O=n+1310720+(N<<1)|0;Fa=b[O>>1]|0;b[O>>1]=Fa+1<<16>>16;Fa=Fa&511;O=n+(Ga<<2)|0;M=m-(c[O>>2]|0)|0;a[(m&65535)+(n+196608)>>0]=Ga;b[n+262144+(N<<11)+(Fa<<2)>>1]=M>>>0>65535?65535:M;Ga=n+131072+(Ga<<1)|0;b[n+262144+(N<<11)+(Fa<<2)+2>>1]=b[Ga>>1]|0;c[O>>2]=m;b[Ga>>1]=Fa;m=m+1|0}m=y+(c[pa>>2]|0)|0;O=w;f=0}c[p>>2]=f+L;c[r>>2]=(c[r>>2]|0)+(M-q>>4);i=ra;return}function Ua(b,d,e,f,g,h){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0;j=d+524304|0;if(!(a[j>>0]&1))return;e=1<<c[e+8>>2];c[d>>2]=e+-1;e=1-e|0;c[d+524296>>2]=e;i=0;while(1){if((i|0)==131072)break;c[d+4+(i<<2)>>2]=e;i=i+1|0}if((f|0)==0^1|h^1)g=(c[d>>2]|0)+1|0;h=d+524300|0;do if(g>>>0>(c[h>>2]|0)>>>0){i=d+524292|0;e=b+8|0;ra[c[b+4>>2]&1](c[e>>2]|0,c[i>>2]|0);c[i>>2]=0;e=sa[c[b>>2]&1](c[e>>2]|0,g<<3)|0;if(!e)oa(1);else{c[i>>2]=e;c[h>>2]=g;break}}while(0);a[j>>0]=0;return}function Va(a,b,d){a=a|0;b=b|0;d=d|0;var e=0.0,f=0,h=0,i=0.0,j=0.0,k=0;f=0;h=0;while(1){if((f|0)==(b|0))break;k=h+(c[a+(f<<2)>>2]|0)|0;f=f+1|0;h=k}if(h>>>0<256)e=+g[19516+(h<<2)>>2];else e=+Xb(+(h>>>0));j=e+2.0;h=0;while(1){if((h|0)==(b|0))break;f=c[a+(h<<2)>>2]|0;if(f){if(f>>>0<256)i=+g[19516+(f<<2)>>2];else i=+Xb(+(f>>>0));i=e-i;f=d+(h<<2)|0;g[f>>2]=i;if(i<1.0)g[f>>2]=1.0}else g[d+(h<<2)>>2]=j;h=h+1|0}return}function Wa(a){a=a|0;var b=0.0,d=0,e=0.0,f=0,h=0.0,j=0.0,k=0.0,l=0,m=0,n=0,o=0.0,p=0,q=0.0,r=0,s=0;s=i;i=i+112|0;m=s+88|0;p=s+72|0;r=s;n=c[a+1024>>2]|0;if(!n){q=12.0;i=s;return +q}else{f=0;l=0}while(1){if(l>>>0>=256){d=f;break}if(c[a+(l<<2)>>2]|0){c[m+(f<<2)>>2]=l;d=f+1|0;if((f|0)>3)break}else d=f;f=d;l=l+1|0}switch(d|0){case 2:{q=+(n>>>0)+20.0;i=s;return +q}case 3:{p=c[a+(c[m>>2]<<2)>>2]|0;n=c[a+(c[m+4>>2]<<2)>>2]|0;a=c[a+(c[m+8>>2]<<2)>>2]|0;r=n>>>0>a>>>0?n:a;q=+(p+n+a<<1>>>0)+28.0-+((p>>>0>r>>>0?p:r)>>>0);i=s;return +q}case 4:{d=0;while(1){if((d|0)==4){n=0;break}c[p+(d<<2)>>2]=c[a+(c[m+(d<<2)>>2]<<2)>>2];d=d+1|0}while(1){if((n|0)==4)break;m=p+(n<<2)|0;d=n;while(1){a=d+1|0;if((d|0)==3)break;d=p+(a<<2)|0;f=c[d>>2]|0;l=c[m>>2]|0;if(f>>>0<=l>>>0){d=a;continue}c[d>>2]=l;c[m>>2]=f;d=a}n=n+1|0}a=(c[p+8>>2]|0)+(c[p+12>>2]|0)|0;r=c[p>>2]|0;q=+((a*3|0)>>>0)+37.0+ +(r+(c[p+4>>2]|0)<<1>>>0)-+((a>>>0>r>>>0?a:r)>>>0);i=s;return +q}case 1:{q=12.0;i=s;return +q}default:{d=r;f=d+72|0;do{c[d>>2]=0;d=d+4|0}while((d|0)<(f|0));if(n>>>0<256)h=+g[19516+(n<<2)>>2];else h=+Xb(+(n>>>0));m=r+68|0;b=0.0;d=0;n=1;a:while(1){q=b;b:while(1){while(1){if(d>>>0>=256)break a;f=c[a+(d<<2)>>2]|0;if(!f){l=1;f=d}else break b;while(1){f=f+1|0;if(f>>>0>=256)break;if(c[a+(f<<2)>>2]|0)break;l=l+1|0}d=d+l|0;if((d|0)==256)break a;if(l>>>0>=3)break;c[r>>2]=(c[r>>2]|0)+l}b=q;f=l+-2|0;while(1){if(!f){q=b;continue b}c[m>>2]=(c[m>>2]|0)+1;b=b+3.0;f=f>>>3}}if(f>>>0<256){e=+(f>>>0);b=+g[19516+(f<<2)>>2]}else{b=+(f>>>0);e=b;b=+Xb(b)}b=h-b;p=~~(b+.5)>>>0;p=p>>>0>15?15:p;l=r+(p<<2)|0;c[l>>2]=(c[l>>2]|0)+1;b=q+e*b;d=d+1|0;n=p>>>0>n>>>0?p:n}o=+(((n<<1)+18|0)>>>0);m=r+72|0;d=r;b=0.0;n=0;while(1){if(d>>>0>=m>>>0)break;f=c[d>>2]|0;e=+(f>>>0);if(f>>>0<256)k=+g[19516+(f<<2)>>2];else k=+Xb(e);l=c[d+4>>2]|0;h=+(l>>>0);if(l>>>0<256)j=+g[19516+(l<<2)>>2];else j=+Xb(h);d=d+8|0;b=b-e*k-h*j;n=n+f+l|0}k=q+o;j=+(n>>>0);if(!n)h=j;else{if(n>>>0<256)e=+g[19516+(n<<2)>>2];else e=+Xb(j);h=j;b=b+j*e}q=k+(b<h?h:b);i=s;return +q}}return 0.0}function Xa(a){a=a|0;var b=0.0,d=0,e=0.0,f=0,h=0.0,j=0.0,k=0.0,l=0,m=0,n=0,o=0.0,p=0,q=0.0,r=0,s=0;s=i;i=i+112|0;m=s+88|0;p=s+72|0;r=s;n=c[a+2816>>2]|0;if(!n){q=12.0;i=s;return +q}else{f=0;l=0}while(1){if(l>>>0>=704){d=f;break}if(c[a+(l<<2)>>2]|0){c[m+(f<<2)>>2]=l;d=f+1|0;if((f|0)>3)break}else d=f;f=d;l=l+1|0}switch(d|0){case 2:{q=+(n>>>0)+20.0;i=s;return +q}case 3:{p=c[a+(c[m>>2]<<2)>>2]|0;n=c[a+(c[m+4>>2]<<2)>>2]|0;a=c[a+(c[m+8>>2]<<2)>>2]|0;r=n>>>0>a>>>0?n:a;q=+(p+n+a<<1>>>0)+28.0-+((p>>>0>r>>>0?p:r)>>>0);i=s;return +q}case 4:{d=0;while(1){if((d|0)==4){n=0;break}c[p+(d<<2)>>2]=c[a+(c[m+(d<<2)>>2]<<2)>>2];d=d+1|0}while(1){if((n|0)==4)break;m=p+(n<<2)|0;d=n;while(1){a=d+1|0;if((d|0)==3)break;d=p+(a<<2)|0;f=c[d>>2]|0;l=c[m>>2]|0;if(f>>>0<=l>>>0){d=a;continue}c[d>>2]=l;c[m>>2]=f;d=a}n=n+1|0}a=(c[p+8>>2]|0)+(c[p+12>>2]|0)|0;r=c[p>>2]|0;q=+((a*3|0)>>>0)+37.0+ +(r+(c[p+4>>2]|0)<<1>>>0)-+((a>>>0>r>>>0?a:r)>>>0);i=s;return +q}case 1:{q=12.0;i=s;return +q}default:{d=r;f=d+72|0;do{c[d>>2]=0;d=d+4|0}while((d|0)<(f|0));if(n>>>0<256)h=+g[19516+(n<<2)>>2];else h=+Xb(+(n>>>0));m=r+68|0;b=0.0;d=0;n=1;a:while(1){q=b;b:while(1){while(1){if(d>>>0>=704)break a;f=c[a+(d<<2)>>2]|0;if(!f){l=1;f=d}else break b;while(1){f=f+1|0;if(f>>>0>=704)break;if(c[a+(f<<2)>>2]|0)break;l=l+1|0}d=d+l|0;if((d|0)==704)break a;if(l>>>0>=3)break;c[r>>2]=(c[r>>2]|0)+l}b=q;f=l+-2|0;while(1){if(!f){q=b;continue b}c[m>>2]=(c[m>>2]|0)+1;b=b+3.0;f=f>>>3}}if(f>>>0<256){e=+(f>>>0);b=+g[19516+(f<<2)>>2]}else{b=+(f>>>0);e=b;b=+Xb(b)}b=h-b;p=~~(b+.5)>>>0;p=p>>>0>15?15:p;l=r+(p<<2)|0;c[l>>2]=(c[l>>2]|0)+1;b=q+e*b;d=d+1|0;n=p>>>0>n>>>0?p:n}o=+(((n<<1)+18|0)>>>0);m=r+72|0;d=r;b=0.0;n=0;while(1){if(d>>>0>=m>>>0)break;f=c[d>>2]|0;e=+(f>>>0);if(f>>>0<256)k=+g[19516+(f<<2)>>2];else k=+Xb(e);l=c[d+4>>2]|0;h=+(l>>>0);if(l>>>0<256)j=+g[19516+(l<<2)>>2];else j=+Xb(h);d=d+8|0;b=b-e*k-h*j;n=n+f+l|0}k=q+o;j=+(n>>>0);if(!n)h=j;else{if(n>>>0<256)e=+g[19516+(n<<2)>>2];else e=+Xb(j);h=j;b=b+j*e}q=k+(b<h?h:b);i=s;return +q}}return 0.0}function Ya(a){a=a|0;var b=0.0,d=0,e=0.0,f=0,h=0.0,j=0.0,k=0.0,l=0,m=0,n=0,o=0.0,p=0,q=0.0,r=0,s=0;s=i;i=i+112|0;m=s+88|0;p=s+72|0;r=s;n=c[a+2080>>2]|0;if(!n){q=12.0;i=s;return +q}else{f=0;l=0}while(1){if(l>>>0>=520){d=f;break}if(c[a+(l<<2)>>2]|0){c[m+(f<<2)>>2]=l;d=f+1|0;if((f|0)>3)break}else d=f;f=d;l=l+1|0}switch(d|0){case 2:{q=+(n>>>0)+20.0;i=s;return +q}case 3:{p=c[a+(c[m>>2]<<2)>>2]|0;n=c[a+(c[m+4>>2]<<2)>>2]|0;a=c[a+(c[m+8>>2]<<2)>>2]|0;r=n>>>0>a>>>0?n:a;q=+(p+n+a<<1>>>0)+28.0-+((p>>>0>r>>>0?p:r)>>>0);i=s;return +q}case 4:{d=0;while(1){if((d|0)==4){n=0;break}c[p+(d<<2)>>2]=c[a+(c[m+(d<<2)>>2]<<2)>>2];d=d+1|0}while(1){if((n|0)==4)break;m=p+(n<<2)|0;d=n;while(1){a=d+1|0;if((d|0)==3)break;d=p+(a<<2)|0;f=c[d>>2]|0;l=c[m>>2]|0;if(f>>>0<=l>>>0){d=a;continue}c[d>>2]=l;c[m>>2]=f;d=a}n=n+1|0}a=(c[p+8>>2]|0)+(c[p+12>>2]|0)|0;r=c[p>>2]|0;q=+((a*3|0)>>>0)+37.0+ +(r+(c[p+4>>2]|0)<<1>>>0)-+((a>>>0>r>>>0?a:r)>>>0);i=s;return +q}case 1:{q=12.0;i=s;return +q}default:{d=r;f=d+72|0;do{c[d>>2]=0;d=d+4|0}while((d|0)<(f|0));if(n>>>0<256)h=+g[19516+(n<<2)>>2];else h=+Xb(+(n>>>0));m=r+68|0;b=0.0;d=0;n=1;a:while(1){q=b;b:while(1){while(1){if(d>>>0>=520)break a;f=c[a+(d<<2)>>2]|0;if(!f){l=1;f=d}else break b;while(1){f=f+1|0;if(f>>>0>=520)break;if(c[a+(f<<2)>>2]|0)break;l=l+1|0}d=d+l|0;if((d|0)==520)break a;if(l>>>0>=3)break;c[r>>2]=(c[r>>2]|0)+l}b=q;f=l+-2|0;while(1){if(!f){q=b;continue b}c[m>>2]=(c[m>>2]|0)+1;b=b+3.0;f=f>>>3}}if(f>>>0<256){e=+(f>>>0);b=+g[19516+(f<<2)>>2]}else{b=+(f>>>0);e=b;b=+Xb(b)}b=h-b;p=~~(b+.5)>>>0;p=p>>>0>15?15:p;l=r+(p<<2)|0;c[l>>2]=(c[l>>2]|0)+1;b=q+e*b;d=d+1|0;n=p>>>0>n>>>0?p:n}o=+(((n<<1)+18|0)>>>0);m=r+72|0;d=r;b=0.0;n=0;while(1){if(d>>>0>=m>>>0)break;f=c[d>>2]|0;e=+(f>>>0);if(f>>>0<256)k=+g[19516+(f<<2)>>2];else k=+Xb(e);l=c[d+4>>2]|0;h=+(l>>>0);if(l>>>0<256)j=+g[19516+(l<<2)>>2];else j=+Xb(h);d=d+8|0;b=b-e*k-h*j;n=n+f+l|0}k=q+o;j=+(n>>>0);if(!n)h=j;else{if(n>>>0<256)e=+g[19516+(n<<2)>>2];else e=+Xb(j);h=j;b=b+j*e}q=k+(b<h?h:b);i=s;return +q}}return 0.0}function Za(f,j,k,l,m,n,o,p,q,r){f=f|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;var t=0,u=0,v=0,w=0,x=0,y=0.0,z=0.0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,aa=0,ba=0.0;aa=i;i=i+6688|0;Z=aa+2832|0;Y=aa;V=aa+6432|0;T=aa+6176|0;W=aa+5920|0;U=aa+5664|0;t=0;K=0;while(1){if((t|0)==(k|0))break;$=K+(c[j+(t<<4)>>2]|0)|0;t=t+1|0;K=$}$=f+8|0;R=sa[c[f>>2]&1](c[$>>2]|0,K)|0;if(!R)oa(1);x=n+1|0;v=0;w=0;while(1){t=m&n;if((w|0)==(k|0))break;m=c[j+(w<<4)>>2]|0;if((t+m|0)>>>0>n>>>0){Q=x-t|0;dc(R+v|0,l+t|0,Q|0)|0;t=0;u=m-Q|0;m=v+Q|0}else{u=m;m=v}if(u){dc(R+m|0,l+t|0,u|0)|0;m=m+u|0}v=m;m=t+u+(c[j+(w<<4)+4>>2]&16777215)|0;w=w+1|0}B=((K>>>0)/544|0)+1|0;B=B>>>0>100?100:B;do if(K){if(K>>>0<128){w=p+16|0;m=c[w>>2]|0;x=p+4|0;t=c[x>>2]|0;u=t+1|0;if(m>>>0<u>>>0){v=(m|0)==0?u:m;while(1){if(v>>>0>=u>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v)|0;if(!m)oa(1);u=c[w>>2]|0;t=p+8|0;if(u)dc(m|0,c[t>>2]|0,u|0)|0;ra[c[f+4>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v;t=c[x>>2]|0}w=p+20|0;m=c[w>>2]|0;u=t+1|0;if(m>>>0<u>>>0){v=(m|0)==0?u:m;while(1){if(v>>>0>=u>>>0)break;v=v<<1}t=sa[c[f>>2]&1](c[$>>2]|0,v<<2)|0;if(!t)oa(1);u=c[w>>2]|0;m=p+12|0;if(u)dc(t|0,c[m>>2]|0,u<<2|0)|0;ra[c[f+4>>2]&1](c[$>>2]|0,c[m>>2]|0);c[m>>2]=t;c[w>>2]=v;t=c[x>>2]|0}else m=p+12|0;c[p>>2]=1;a[(c[p+8>>2]|0)+t>>0]=0;c[(c[m>>2]|0)+(c[x>>2]<<2)>>2]=K;c[x>>2]=(c[x>>2]|0)+1;break}J=sa[c[f>>2]&1](c[$>>2]|0,B*1040|0)|0;if(!J)oa(1);t=0;while(1){if((t|0)==(B|0))break;ac(J+(t*1040|0)|0,0,1024)|0;c[J+(t*1040|0)+1024>>2]=0;h[J+(t*1040|0)+1032>>3]=s;t=t+1|0}w=(K>>>0)/(B>>>0)|0;x=K+-71|0;m=7;l=0;while(1){if((l|0)==(B|0))break;t=((_(K,l)|0)>>>0)/(B>>>0)|0;if(l){m=m*16807|0;m=(m|0)==0?1:m;t=t+((m>>>0)%(w>>>0)|0)|0}u=J+(l*1040|0)+1024|0;c[u>>2]=(c[u>>2]|0)+70;u=71;v=R+((t+70|0)>>>0<K>>>0?t:x)|0;while(1){t=u+-1|0;if(!t)break;u=J+(l*1040|0)+(d[v>>0]<<2)|0;c[u>>2]=(c[u>>2]|0)+1;u=t;v=v+1|0}l=l+1|0}c[Z>>2]=7;u=((K<<1>>>0)/70|0)+100+B+-1|0;u=u-((u>>>0)%(B>>>0)|0)|0;v=Y+1024|0;w=Y+1032|0;x=K>>>0>70;l=K+-69|0;n=Y+1024|0;A=0;while(1){if(A>>>0>=u>>>0)break;ac(Y|0,0,1028)|0;h[w>>3]=s;if(x){m=(c[Z>>2]|0)*16807|0;Q=(m|0)==0;c[Z>>2]=Q?1:m;t=70;m=((Q?1:m)>>>0)%(l>>>0)|0}else{t=K;m=0}c[n>>2]=(c[n>>2]|0)+t;t=t+1|0;m=R+m|0;while(1){t=t+-1|0;if(!t)break;Q=Y+(d[m>>0]<<2)|0;c[Q>>2]=(c[Q>>2]|0)+1;m=m+1|0}t=(A>>>0)%(B>>>0)|0;m=J+(t*1040|0)+1024|0;c[m>>2]=(c[m>>2]|0)+(c[v>>2]|0);m=0;while(1){if((m|0)==256)break;Q=J+(t*1040|0)+(m<<2)|0;c[Q>>2]=(c[Q>>2]|0)+(c[Y+(m<<2)>>2]|0);m=m+1|0}A=A+1|0}Q=sa[c[f>>2]&1](c[$>>2]|0,K)|0;if(!Q)oa(1);F=sa[c[f>>2]&1](c[$>>2]|0,B<<11)|0;if(!F)oa(1);G=sa[c[f>>2]&1](c[$>>2]|0,B<<3)|0;if(!G)oa(1);H=_(K,(B+7|0)>>>3)|0;H=sa[c[f>>2]&1](c[$>>2]|0,H)|0;if(!H)oa(1);I=sa[c[f>>2]&1](c[$>>2]|0,B<<1)|0;if(!I)oa(1);C=(c[o+4>>2]|0)<11?3:10;D=K+-1|0;E=Q+D|0;O=0;A=0;a:while(1){if(A>>>0>=C>>>0){l=136;break}n=(B+7|0)>>>3;if(B>>>0>=257){l=73;break}b:do if(B>>>0<2){t=0;while(1){if((t|0)==(K|0)){v=1;break b}a[Q+t>>0]=0;t=t+1|0}}else{ac(F|0,0,B<<11|0)|0;m=0;while(1){if((m|0)==(B|0)){t=256;break}t=c[J+(m*1040|0)+1024>>2]|0;if(t>>>0<256)y=+g[19516+(t<<2)>>2];else y=+Xb(+(t>>>0));h[F+(m<<3)>>3]=y;m=m+1|0}c:while(1){if(!t)break;t=t+-1|0;u=_(t,B)|0;v=0;while(1){if((v|0)==(B|0))continue c;z=+h[F+(v<<3)>>3];m=c[J+(v*1040|0)+(t<<2)>>2]|0;do if(m)if(m>>>0<256){y=+g[19516+(m<<2)>>2];break}else{y=+Xb(+(m>>>0));break}else y=-2.0;while(0);h[F+(u+v<<3)>>3]=z-y;v=v+1|0}}ac(G|0,0,B<<3|0)|0;ac(H|0,0,_(K,n)|0)|0;v=0;while(1){if(v>>>0>=K>>>0)break;t=_(d[R+v>>0]|0,B)|0;m=Q+v|0;z=1.e+99;u=0;while(1){if((u|0)==(B|0))break;O=G+(u<<3)|0;y=+h[O>>3]+ +h[F+(t+u<<3)>>3];h[O>>3]=y;if(y<z)a[m>>0]=u;else y=z;z=y;u=u+1|0}m=_(v,n)|0;if(v>>>0<2e3)y=(+(v>>>0)*.07/2.0e3+.77)*28.1;else y=28.1;u=0;while(1){if(u>>>0>=B>>>0)break;t=G+(u<<3)|0;ba=+h[t>>3]-z;h[t>>3]=ba;if(ba>=y){h[t>>3]=y;t=u>>>3;if(t>>>0>=n>>>0){l=104;break a}O=H+(m+t)|0;a[O>>0]=a[O>>0]|1<<(u&7)&255}u=u+1|0}v=v+1|0}m=_(D,n)|0;v=D;t=a[E>>0]|0;u=1;while(1){if(!v){v=u;break b}w=t&255;x=w>>>3;if(x>>>0>=n>>>0){l=111;break a}l=v+-1|0;m=m-n|0;v=Q+l|0;if((a[H+(m+x)>>0]&(1<<(w&7)&255))<<24>>24){N=a[v>>0]|0;O=t<<24>>24==N<<24>>24;t=O?t:N;u=O?u:u+1|0}a[v>>0]=t;v=l}}while(0);t=0;while(1){if((t|0)==(B|0)){t=0;u=0;break}b[I+(t<<1)>>1]=256;t=t+1|0}while(1){if(u>>>0>=K>>>0){m=0;break}m=d[Q+u>>0]|0;if(m>>>0>=B>>>0){l=120;break a}m=I+(m<<1)|0;if((b[m>>1]|0)==256){b[m>>1]=t;t=t+1<<16>>16}u=u+1|0}while(1){if(m>>>0>=K>>>0)break;N=Q+m|0;O=b[I+(d[N>>0]<<1)>>1]|0;a[N>>0]=O;if((O&255)>>>0>=B>>>0){l=126;break a}m=m+1|0}u=t&65535;if(u>>>0>B>>>0){l=129;break}t=t&65535;m=0;while(1){if((m|0)==(t|0)){t=0;break}ac(J+(m*1040|0)|0,0,1024)|0;c[J+(m*1040|0)+1024>>2]=0;h[J+(m*1040|0)+1032>>3]=s;m=m+1|0}while(1){if((t|0)==(K|0))break;O=d[Q+t>>0]|0;N=J+(O*1040|0)+(d[R+t>>0]<<2)|0;c[N>>2]=(c[N>>2]|0)+1;O=J+(O*1040|0)+1024|0;c[O>>2]=(c[O>>2]|0)+1;t=t+1|0}O=v;B=u;A=A+1|0}if((l|0)==73)pa(404083,403848,80,404281);else if((l|0)==104)pa(404124,403848,130,404281);else if((l|0)==111)pa(404145,403848,141,404281);else if((l|0)==120)pa(404005,403848,165,404260);else if((l|0)==126)pa(404005,403848,172,404260);else if((l|0)==129)pa(404057,403848,174,404260);else if((l|0)==136){N=f+4|0;ra[c[N>>2]&1](c[$>>2]|0,F);ra[c[N>>2]&1](c[$>>2]|0,G);ra[c[N>>2]&1](c[$>>2]|0,H);ra[c[N>>2]&1](c[$>>2]|0,I);ra[c[N>>2]&1](c[$>>2]|0,J);t=O<<2;L=sa[c[f>>2]&1](c[$>>2]|0,t)|0;if(!L)oa(1);M=sa[c[f>>2]&1](c[$>>2]|0,t)|0;if(!M)oa(1);x=((O<<4)+1008|0)>>>6;J=sa[c[f>>2]&1](c[$>>2]|0,x*1040|0)|0;v=J;if(!J)oa(1);J=sa[c[f>>2]&1](c[$>>2]|0,x<<2)|0;w=J;if(!J)oa(1);G=sa[c[f>>2]&1](c[$>>2]|0,(O>>>0<64?O:64)*1040|0)|0;if(!G)oa(1);H=sa[c[f>>2]&1](c[$>>2]|0,49176)|0;if(!H)oa(1);ac(V|0,0,256)|0;ac(T|0,0,256)|0;ac(W|0,0,256)|0;ac(U|0,0,256)|0;ac(M|0,0,t|0)|0;m=0;u=0;while(1){if(u>>>0>=K>>>0)break;if(m>>>0>=O>>>0){l=151;break}t=M+(m<<2)|0;c[t>>2]=(c[t>>2]|0)+1;t=u+1|0;if((t|0)!=(K|0)?(a[Q+u>>0]|0)==(a[Q+t>>0]|0):0){u=t;continue}m=m+1|0;u=t}if((l|0)==151)pa(403825,403848,231,404239);if((m|0)!=(O|0))pa(403911,403848,237,404239);C=x;B=x;J=0;t=0;F=0;while(1){if(O>>>0<=F>>>0){l=193;break}E=O-F|0;E=E>>>0<64?E:64;n=0;while(1){if((n|0)==(E|0))break;u=G+(n*1040|0)|0;ac(u|0,0,1024)|0;x=G+(n*1040|0)+1024|0;c[x>>2]=0;h[G+(n*1040|0)+1032>>3]=s;l=M+(F+n<<2)|0;m=0;while(1){if(m>>>0>=(c[l>>2]|0)>>>0)break;K=G+(n*1040|0)+(d[R+t>>0]<<2)|0;c[K>>2]=(c[K>>2]|0)+1;c[x>>2]=(c[x>>2]|0)+1;t=t+1|0;m=m+1|0}h[G+(n*1040|0)+1032>>3]=+Wa(u);c[T+(n<<2)>>2]=n;c[W+(n<<2)>>2]=n;c[V+(n<<2)>>2]=1;n=n+1|0}D=pb(G,V,W,T,H,E,E,64,2048)|0;m=J+D|0;if(C>>>0<m>>>0){u=(C|0)==0;x=u?m:C;while(1){if(x>>>0>=m>>>0)break;x=x<<1}m=sa[c[f>>2]&1](c[$>>2]|0,x*1040|0)|0;if(!m){l=170;break}if(!u)dc(m|0,v|0,C*1040|0)|0;ra[c[N>>2]&1](c[$>>2]|0,v);v=m;C=x}m=J+D|0;if(B>>>0<m>>>0){u=(B|0)==0;x=u?m:B;while(1){if(x>>>0>=m>>>0)break;x=x<<1}m=sa[c[f>>2]&1](c[$>>2]|0,x<<2)|0;if(!m){l=179;break}if(!u)dc(m|0,w|0,B<<2|0)|0;ra[c[N>>2]&1](c[$>>2]|0,w);w=m;B=x}m=v;u=w;A=D+J|0;x=J;l=J;n=0;while(1){if((n|0)==(D|0)){m=0;break}K=T+(n<<2)|0;I=c[K>>2]|0;dc(m+(x*1040|0)|0,G+(I*1040|0)|0,1040)|0;c[u+(l<<2)>>2]=c[V+(I<<2)>>2];c[U+(c[K>>2]<<2)>>2]=n;x=x+1|0;l=l+1|0;n=n+1|0}while(1){if((m|0)==(E|0))break;c[L+(F+m<<2)>>2]=J+(c[U+(c[W+(m<<2)>>2]<<2)>>2]|0);m=m+1|0}m=D+J|0;if((J+D|0)!=(m|0)){l=189;break}if((m|0)!=(A|0)){l=191;break}J=m;F=F+64|0}if((l|0)==170)oa(1);else if((l|0)==179)oa(1);else if((l|0)==189)pa(403935,403848,273,404239);else if((l|0)==191)pa(403969,403848,274,404239);else if((l|0)==193){ra[c[N>>2]&1](c[$>>2]|0,G);K=J<<6;u=_(J>>>1,J)|0;u=K>>>0<u>>>0?K:u;do if((u+1|0)>>>0>2049){ra[c[N>>2]&1](c[$>>2]|0,H);t=sa[c[f>>2]&1](c[$>>2]|0,(u*24|0)+24|0)|0;if(t){P=t;break}oa(1)}else P=H;while(0);m=J<<2;I=sa[c[f>>2]&1](c[$>>2]|0,m)|0;if(!I)oa(1);t=0;while(1){if((t|0)==(J|0))break;c[I+(t<<2)>>2]=t;t=t+1|0}H=v;G=pb(H,w,L,I,P,J,O,256,u)|0;ra[c[N>>2]&1](c[$>>2]|0,P);ra[c[N>>2]&1](c[$>>2]|0,w);K=sa[c[f>>2]&1](c[$>>2]|0,m)|0;if(!K)oa(1);t=0;while(1){if((t|0)==(J|0))break;c[K+(t<<2)>>2]=-1;t=t+1|0}B=Y+1024|0;C=Y+1032|0;D=Z+1024|0;E=Z+1024|0;t=0;m=0;F=0;while(1){if((F|0)==(O|0))break;ac(Y|0,0,1028)|0;h[C>>3]=s;u=M+(F<<2)|0;w=0;A=m;m=0;while(1){if(m>>>0>=(c[u>>2]|0)>>>0)break;w=Y+(d[R+A>>0]<<2)|0;c[w>>2]=(c[w>>2]|0)+1;w=(c[B>>2]|0)+1|0;c[B>>2]=w;A=A+1|0;m=m+1|0}u=c[((F|0)==0?L:L+(F+-1<<2)|0)>>2]|0;n=(w|0)==0;if(n)y=0.0;else{dc(Z|0,Y|0,1040)|0;c[E>>2]=(c[E>>2]|0)+(c[H+(u*1040|0)+1024>>2]|0);m=0;while(1){if((m|0)==256)break;P=Z+(m<<2)|0;c[P>>2]=(c[P>>2]|0)+(c[H+(u*1040|0)+(m<<2)>>2]|0);m=m+1|0}y=+Wa(Z);y=y-+h[H+(u*1040|0)+1032>>3]}m=u;l=0;while(1){if((l|0)==(G|0))break;x=I+(l<<2)|0;u=c[x>>2]|0;if(n)z=0.0;else{dc(Z|0,Y|0,1040)|0;c[D>>2]=(c[D>>2]|0)+(c[H+(u*1040|0)+1024>>2]|0);w=0;while(1){if((w|0)==256)break;P=Z+(w<<2)|0;c[P>>2]=(c[P>>2]|0)+(c[H+(u*1040|0)+(w<<2)>>2]|0);w=w+1|0}z=+Wa(Z);z=z-+h[H+(u*1040|0)+1032>>3]}if(z<y){y=z;m=c[x>>2]|0}l=l+1|0}c[L+(F<<2)>>2]=m;m=K+(m<<2)|0;if((c[m>>2]|0)==-1){c[m>>2]=t;t=t+1|0}m=A;F=F+1|0}ra[c[N>>2]&1](c[$>>2]|0,I);ra[c[N>>2]&1](c[$>>2]|0,v);w=p+16|0;t=c[w>>2]|0;if(t>>>0<O>>>0){v=(t|0)==0?O:t;while(1){if(v>>>0>=O>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v)|0;if(!m)oa(1);u=c[w>>2]|0;t=p+8|0;if(u)dc(m|0,c[t>>2]|0,u|0)|0;ra[c[N>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v}w=p+20|0;t=c[w>>2]|0;if(t>>>0<O>>>0){v=(t|0)==0?O:t;while(1){if(v>>>0>=O>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v<<2)|0;if(!m)oa(1);u=c[w>>2]|0;t=p+12|0;if(u)dc(m|0,c[t>>2]|0,u<<2|0)|0;ra[c[N>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v}else t=p+12|0;w=p+8|0;l=0;m=0;n=0;u=0;while(1){if((u|0)==(O|0))break;v=m+(c[M+(u<<2)>>2]|0)|0;x=u+1|0;m=c[L+(u<<2)>>2]|0;if((x|0)!=(O|0)?(m|0)==(c[L+(x<<2)>>2]|0):0){m=v;u=x;continue}u=c[K+(m<<2)>>2]|0;P=u&255;a[(c[w>>2]|0)+l>>0]=P;c[(c[t>>2]|0)+(l<<2)>>2]=v;l=l+1|0;m=0;n=((n&255)>(P&255)?n&255:u&255)&255;u=x}c[p+4>>2]=l;c[p>>2]=(n&255)+1;ra[c[N>>2]&1](c[$>>2]|0,K);ra[c[N>>2]&1](c[$>>2]|0,M);ra[c[N>>2]&1](c[$>>2]|0,L);ra[c[N>>2]&1](c[$>>2]|0,Q);break}}}else c[p>>2]=1;while(0);p=f+4|0;ra[c[p>>2]&1](c[$>>2]|0,R);P=k<<1;Q=sa[c[f>>2]&1](c[$>>2]|0,P)|0;if(!Q)oa(1);t=0;while(1){if((t|0)==(k|0))break;b[Q+(t<<1)>>1]=b[j+(t<<4)+12>>1]|0;t=t+1|0}C=((k>>>0)/530|0)+1|0;C=C>>>0>50?50:C;do if(k){if(k>>>0<128){w=q+16|0;m=c[w>>2]|0;x=q+4|0;t=c[x>>2]|0;u=t+1|0;if(m>>>0<u>>>0){v=(m|0)==0?u:m;while(1){if(v>>>0>=u>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v)|0;if(!m)oa(1);u=c[w>>2]|0;t=q+8|0;if(u)dc(m|0,c[t>>2]|0,u|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v;t=c[x>>2]|0}w=q+20|0;m=c[w>>2]|0;u=t+1|0;if(m>>>0<u>>>0){v=(m|0)==0?u:m;while(1){if(v>>>0>=u>>>0)break;v=v<<1}t=sa[c[f>>2]&1](c[$>>2]|0,v<<2)|0;if(!t)oa(1);u=c[w>>2]|0;m=q+12|0;if(u)dc(t|0,c[m>>2]|0,u<<2|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[m>>2]|0);c[m>>2]=t;c[w>>2]=v;t=c[x>>2]|0}else m=q+12|0;c[q>>2]=1;a[(c[q+8>>2]|0)+t>>0]=0;c[(c[m>>2]|0)+(c[x>>2]<<2)>>2]=k;c[x>>2]=(c[x>>2]|0)+1;break}J=sa[c[f>>2]&1](c[$>>2]|0,C*2832|0)|0;if(!J)oa(1);t=0;while(1){if((t|0)==(C|0))break;ac(J+(t*2832|0)|0,0,2816)|0;c[J+(t*2832|0)+2816>>2]=0;h[J+(t*2832|0)+2824>>3]=s;t=t+1|0}v=(k>>>0)/(C>>>0)|0;w=k+-41|0;m=7;x=0;while(1){if((x|0)==(C|0))break;t=((_(x,k)|0)>>>0)/(C>>>0)|0;if(x){m=m*16807|0;m=(m|0)==0?1:m;t=t+((m>>>0)%(v>>>0)|0)|0}u=J+(x*2832|0)+2816|0;c[u>>2]=(c[u>>2]|0)+40;u=Q+(((t+40|0)>>>0<k>>>0?t:w)<<1)|0;t=41;while(1){t=t+-1|0;if(!t)break;R=J+(x*2832|0)+(e[u>>1]<<2)|0;c[R>>2]=(c[R>>2]|0)+1;u=u+2|0}x=x+1|0}c[Z>>2]=7;v=((P>>>0)/40|0)+100+C+-1|0;v=v-((v>>>0)%(C>>>0)|0)|0;w=Y+2816|0;x=Y+2824|0;l=k>>>0>40;n=k+-39|0;A=Y+2816|0;B=0;while(1){if(B>>>0>=v>>>0)break;ac(Y|0,0,2820)|0;h[x>>3]=s;if(l){t=(c[Z>>2]|0)*16807|0;R=(t|0)==0;c[Z>>2]=R?1:t;m=40;t=((R?1:t)>>>0)%(n>>>0)|0}else{m=k;t=0}c[A>>2]=(c[A>>2]|0)+m;u=Q+(t<<1)|0;t=m+1|0;while(1){t=t+-1|0;if(!t)break;R=Y+(e[u>>1]<<2)|0;c[R>>2]=(c[R>>2]|0)+1;u=u+2|0}t=(B>>>0)%(C>>>0)|0;m=J+(t*2832|0)+2816|0;c[m>>2]=(c[m>>2]|0)+(c[w>>2]|0);m=0;while(1){if((m|0)==704)break;R=J+(t*2832|0)+(m<<2)|0;c[R>>2]=(c[R>>2]|0)+(c[Y+(m<<2)>>2]|0);m=m+1|0}B=B+1|0}O=sa[c[f>>2]&1](c[$>>2]|0,k)|0;if(!O)oa(1);F=sa[c[f>>2]&1](c[$>>2]|0,C*5632|0)|0;if(!F)oa(1);G=sa[c[f>>2]&1](c[$>>2]|0,C<<3)|0;if(!G)oa(1);H=_((C+7|0)>>>3,k)|0;H=sa[c[f>>2]&1](c[$>>2]|0,H)|0;if(!H)oa(1);I=sa[c[f>>2]&1](c[$>>2]|0,C<<1)|0;if(!I)oa(1);B=(c[o+4>>2]|0)<11?3:10;D=k+-1|0;E=O+D|0;N=0;A=0;d:while(1){if(A>>>0>=B>>>0){l=382;break}n=(C+7|0)>>>3;if(C>>>0>=257){l=319;break}e:do if(C>>>0<2){t=0;while(1){if((t|0)==(k|0)){v=1;break e}a[O+t>>0]=0;t=t+1|0}}else{ac(F|0,0,C*5632|0)|0;m=0;while(1){if((m|0)==(C|0)){t=704;break}t=c[J+(m*2832|0)+2816>>2]|0;if(t>>>0<256)y=+g[19516+(t<<2)>>2];else y=+Xb(+(t>>>0));h[F+(m<<3)>>3]=y;m=m+1|0}f:while(1){if(!t)break;t=t+-1|0;u=_(t,C)|0;v=0;while(1){if((v|0)==(C|0))continue f;z=+h[F+(v<<3)>>3];m=c[J+(v*2832|0)+(t<<2)>>2]|0;do if(m)if(m>>>0<256){y=+g[19516+(m<<2)>>2];break}else{y=+Xb(+(m>>>0));break}else y=-2.0;while(0);h[F+(u+v<<3)>>3]=z-y;v=v+1|0}}ac(G|0,0,C<<3|0)|0;ac(H|0,0,_(n,k)|0)|0;v=0;while(1){if(v>>>0>=k>>>0)break;t=_(e[Q+(v<<1)>>1]|0,C)|0;m=O+v|0;z=1.e+99;u=0;while(1){if((u|0)==(C|0))break;R=G+(u<<3)|0;y=+h[R>>3]+ +h[F+(t+u<<3)>>3];h[R>>3]=y;if(y<z)a[m>>0]=u;else y=z;z=y;u=u+1|0}m=_(v,n)|0;if(v>>>0<2e3)y=(+(v>>>0)*.07/2.0e3+.77)*13.5;else y=13.5;u=0;while(1){if(u>>>0>=C>>>0)break;t=G+(u<<3)|0;ba=+h[t>>3]-z;h[t>>3]=ba;if(ba>=y){h[t>>3]=y;t=u>>>3;if(t>>>0>=n>>>0){l=350;break d}R=H+(m+t)|0;a[R>>0]=a[R>>0]|1<<(u&7)&255}u=u+1|0}v=v+1|0}m=_(D,n)|0;v=D;t=a[E>>0]|0;u=1;while(1){if(!v){v=u;break e}w=t&255;x=w>>>3;if(x>>>0>=n>>>0){l=357;break d}l=v+-1|0;m=m-n|0;v=O+l|0;if((a[H+(m+x)>>0]&(1<<(w&7)&255))<<24>>24){N=a[v>>0]|0;R=t<<24>>24==N<<24>>24;t=R?t:N;u=R?u:u+1|0}a[v>>0]=t;v=l}}while(0);t=0;while(1){if((t|0)==(C|0)){t=0;u=0;break}b[I+(t<<1)>>1]=256;t=t+1|0}while(1){if(u>>>0>=k>>>0){m=0;break}m=d[O+u>>0]|0;if(m>>>0>=C>>>0){l=366;break d}m=I+(m<<1)|0;if((b[m>>1]|0)==256){b[m>>1]=t;t=t+1<<16>>16}u=u+1|0}while(1){if(m>>>0>=k>>>0)break;N=O+m|0;R=b[I+(d[N>>0]<<1)>>1]|0;a[N>>0]=R;if((R&255)>>>0>=C>>>0){l=372;break d}m=m+1|0}u=t&65535;if(u>>>0>C>>>0){l=375;break}t=t&65535;m=0;while(1){if((m|0)==(t|0)){t=0;break}ac(J+(m*2832|0)|0,0,2816)|0;c[J+(m*2832|0)+2816>>2]=0;h[J+(m*2832|0)+2824>>3]=s;m=m+1|0}while(1){if((t|0)==(k|0))break;R=d[O+t>>0]|0;N=J+(R*2832|0)+(e[Q+(t<<1)>>1]<<2)|0;c[N>>2]=(c[N>>2]|0)+1;R=J+(R*2832|0)+2816|0;c[R>>2]=(c[R>>2]|0)+1;t=t+1|0}N=v;C=u;A=A+1|0}if((l|0)==319)pa(404083,403848,80,404221);else if((l|0)==350)pa(404124,403848,130,404221);else if((l|0)==357)pa(404145,403848,141,404221);else if((l|0)==366)pa(404005,403848,165,404200);else if((l|0)==372)pa(404005,403848,172,404200);else if((l|0)==375)pa(404057,403848,174,404200);else if((l|0)==382){ra[c[p>>2]&1](c[$>>2]|0,F);ra[c[p>>2]&1](c[$>>2]|0,G);ra[c[p>>2]&1](c[$>>2]|0,H);ra[c[p>>2]&1](c[$>>2]|0,I);ra[c[p>>2]&1](c[$>>2]|0,J);t=N<<2;L=sa[c[f>>2]&1](c[$>>2]|0,t)|0;if(!L)oa(1);M=sa[c[f>>2]&1](c[$>>2]|0,t)|0;if(!M)oa(1);x=((N<<4)+1008|0)>>>6;R=sa[c[f>>2]&1](c[$>>2]|0,x*2832|0)|0;v=R;if(!R)oa(1);R=sa[c[f>>2]&1](c[$>>2]|0,x<<2)|0;w=R;if(!R)oa(1);G=sa[c[f>>2]&1](c[$>>2]|0,(N>>>0<64?N:64)*2832|0)|0;if(!G)oa(1);H=sa[c[f>>2]&1](c[$>>2]|0,49176)|0;if(!H)oa(1);ac(V|0,0,256)|0;ac(T|0,0,256)|0;ac(W|0,0,256)|0;ac(U|0,0,256)|0;ac(M|0,0,t|0)|0;m=0;u=0;while(1){if(u>>>0>=k>>>0)break;if(m>>>0>=N>>>0){l=397;break}t=M+(m<<2)|0;c[t>>2]=(c[t>>2]|0)+1;t=u+1|0;if((t|0)!=(k|0)?(a[O+u>>0]|0)==(a[O+t>>0]|0):0){u=t;continue}m=m+1|0;u=t}if((l|0)==397)pa(403825,403848,231,404179);if((m|0)!=(N|0))pa(403911,403848,237,404179);C=x;B=x;J=0;t=0;F=0;while(1){if(N>>>0<=F>>>0){l=439;break}E=N-F|0;E=E>>>0<64?E:64;n=0;while(1){if((n|0)==(E|0))break;u=G+(n*2832|0)|0;ac(u|0,0,2816)|0;x=G+(n*2832|0)+2816|0;c[x>>2]=0;h[G+(n*2832|0)+2824>>3]=s;l=M+(F+n<<2)|0;m=0;while(1){if(m>>>0>=(c[l>>2]|0)>>>0)break;R=G+(n*2832|0)+(e[Q+(t<<1)>>1]<<2)|0;c[R>>2]=(c[R>>2]|0)+1;c[x>>2]=(c[x>>2]|0)+1;t=t+1|0;m=m+1|0}h[G+(n*2832|0)+2824>>3]=+Xa(u);c[T+(n<<2)>>2]=n;c[W+(n<<2)>>2]=n;c[V+(n<<2)>>2]=1;n=n+1|0}D=rb(G,V,W,T,H,E,E,64,2048)|0;m=J+D|0;if(C>>>0<m>>>0){u=(C|0)==0;x=u?m:C;while(1){if(x>>>0>=m>>>0)break;x=x<<1}m=sa[c[f>>2]&1](c[$>>2]|0,x*2832|0)|0;if(!m){l=416;break}if(!u)dc(m|0,v|0,C*2832|0)|0;ra[c[p>>2]&1](c[$>>2]|0,v);v=m;C=x}m=J+D|0;if(B>>>0<m>>>0){u=(B|0)==0;x=u?m:B;while(1){if(x>>>0>=m>>>0)break;x=x<<1}m=sa[c[f>>2]&1](c[$>>2]|0,x<<2)|0;if(!m){l=425;break}if(!u)dc(m|0,w|0,B<<2|0)|0;ra[c[p>>2]&1](c[$>>2]|0,w);w=m;B=x}m=v;u=w;A=D+J|0;x=J;l=J;n=0;while(1){if((n|0)==(D|0)){m=0;break}R=T+(n<<2)|0;K=c[R>>2]|0;dc(m+(x*2832|0)|0,G+(K*2832|0)|0,2832)|0;c[u+(l<<2)>>2]=c[V+(K<<2)>>2];c[U+(c[R>>2]<<2)>>2]=n;x=x+1|0;l=l+1|0;n=n+1|0}while(1){if((m|0)==(E|0))break;c[L+(F+m<<2)>>2]=J+(c[U+(c[W+(m<<2)>>2]<<2)>>2]|0);m=m+1|0}m=D+J|0;if((J+D|0)!=(m|0)){l=435;break}if((m|0)!=(A|0)){l=437;break}J=m;F=F+64|0}if((l|0)==416)oa(1);else if((l|0)==425)oa(1);else if((l|0)==435)pa(403935,403848,273,404179);else if((l|0)==437)pa(403969,403848,274,404179);else if((l|0)==439){ra[c[p>>2]&1](c[$>>2]|0,G);R=J<<6;u=_(J>>>1,J)|0;u=R>>>0<u>>>0?R:u;do if((u+1|0)>>>0>2049){ra[c[p>>2]&1](c[$>>2]|0,H);t=sa[c[f>>2]&1](c[$>>2]|0,(u*24|0)+24|0)|0;if(t){S=t;break}oa(1)}else S=H;while(0);m=J<<2;I=sa[c[f>>2]&1](c[$>>2]|0,m)|0;if(!I)oa(1);t=0;while(1){if((t|0)==(J|0))break;c[I+(t<<2)>>2]=t;t=t+1|0}H=v;G=rb(H,w,L,I,S,J,N,256,u)|0;ra[c[p>>2]&1](c[$>>2]|0,S);ra[c[p>>2]&1](c[$>>2]|0,w);K=sa[c[f>>2]&1](c[$>>2]|0,m)|0;if(!K)oa(1);t=0;while(1){if((t|0)==(J|0))break;c[K+(t<<2)>>2]=-1;t=t+1|0}B=Y+2816|0;C=Y+2824|0;D=Z+2816|0;E=Z+2816|0;t=0;m=0;F=0;while(1){if((F|0)==(N|0))break;ac(Y|0,0,2820)|0;h[C>>3]=s;u=M+(F<<2)|0;w=0;A=m;m=0;while(1){if(m>>>0>=(c[u>>2]|0)>>>0)break;w=Y+(e[Q+(A<<1)>>1]<<2)|0;c[w>>2]=(c[w>>2]|0)+1;w=(c[B>>2]|0)+1|0;c[B>>2]=w;A=A+1|0;m=m+1|0}u=c[((F|0)==0?L:L+(F+-1<<2)|0)>>2]|0;n=(w|0)==0;if(n)y=0.0;else{dc(Z|0,Y|0,2832)|0;c[E>>2]=(c[E>>2]|0)+(c[H+(u*2832|0)+2816>>2]|0);m=0;while(1){if((m|0)==704)break;S=Z+(m<<2)|0;c[S>>2]=(c[S>>2]|0)+(c[H+(u*2832|0)+(m<<2)>>2]|0);m=m+1|0}y=+Xa(Z);y=y-+h[H+(u*2832|0)+2824>>3]}m=u;l=0;while(1){if((l|0)==(G|0))break;x=I+(l<<2)|0;u=c[x>>2]|0;if(n)z=0.0;else{dc(Z|0,Y|0,2832)|0;c[D>>2]=(c[D>>2]|0)+(c[H+(u*2832|0)+2816>>2]|0);w=0;while(1){if((w|0)==704)break;S=Z+(w<<2)|0;c[S>>2]=(c[S>>2]|0)+(c[H+(u*2832|0)+(w<<2)>>2]|0);w=w+1|0}z=+Xa(Z);z=z-+h[H+(u*2832|0)+2824>>3]}if(z<y){y=z;m=c[x>>2]|0}l=l+1|0}c[L+(F<<2)>>2]=m;m=K+(m<<2)|0;if((c[m>>2]|0)==-1){c[m>>2]=t;t=t+1|0}m=A;F=F+1|0}ra[c[p>>2]&1](c[$>>2]|0,I);ra[c[p>>2]&1](c[$>>2]|0,v);w=q+16|0;t=c[w>>2]|0;if(t>>>0<N>>>0){v=(t|0)==0?N:t;while(1){if(v>>>0>=N>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v)|0;if(!m)oa(1);u=c[w>>2]|0;t=q+8|0;if(u)dc(m|0,c[t>>2]|0,u|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v}w=q+20|0;t=c[w>>2]|0;if(t>>>0<N>>>0){v=(t|0)==0?N:t;while(1){if(v>>>0>=N>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v<<2)|0;if(!m)oa(1);u=c[w>>2]|0;t=q+12|0;if(u)dc(m|0,c[t>>2]|0,u<<2|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v}else t=q+12|0;w=q+8|0;l=0;m=0;n=0;u=0;while(1){if((u|0)==(N|0))break;v=m+(c[M+(u<<2)>>2]|0)|0;x=u+1|0;m=c[L+(u<<2)>>2]|0;if((x|0)!=(N|0)?(m|0)==(c[L+(x<<2)>>2]|0):0){m=v;u=x;continue}u=c[K+(m<<2)>>2]|0;S=u&255;a[(c[w>>2]|0)+l>>0]=S;c[(c[t>>2]|0)+(l<<2)>>2]=v;l=l+1|0;m=0;n=((n&255)>(S&255)?n&255:u&255)&255;u=x}c[q+4>>2]=l;c[q>>2]=(n&255)+1;ra[c[p>>2]&1](c[$>>2]|0,K);ra[c[p>>2]&1](c[$>>2]|0,M);ra[c[p>>2]&1](c[$>>2]|0,L);ra[c[p>>2]&1](c[$>>2]|0,O);break}}}else c[q>>2]=1;while(0);ra[c[p>>2]&1](c[$>>2]|0,Q);P=sa[c[f>>2]&1](c[$>>2]|0,P)|0;if(!P)oa(1);K=0;m=0;while(1){if((m|0)==(k|0))break;if((c[j+(m<<4)+4>>2]&16777215|0)!=0?(e[j+(m<<4)+12>>1]|0)>127:0){b[P+(K<<1)>>1]=b[j+(m<<4)+14>>1]|0;t=K+1|0}else t=K;K=t;m=m+1|0}C=((K>>>0)/544|0)+1|0;C=C>>>0>50?50:C;if(!K){c[r>>2]=1;f=c[p>>2]|0;r=c[$>>2]|0;ra[f&1](r,P);i=aa;return}if(K>>>0<128){w=r+16|0;m=c[w>>2]|0;x=r+4|0;t=c[x>>2]|0;u=t+1|0;if(m>>>0<u>>>0){v=(m|0)==0?u:m;while(1){if(v>>>0>=u>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v)|0;if(!m)oa(1);u=c[w>>2]|0;t=r+8|0;if(u)dc(m|0,c[t>>2]|0,u|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v;t=c[x>>2]|0}w=r+20|0;m=c[w>>2]|0;u=t+1|0;if(m>>>0<u>>>0){v=(m|0)==0?u:m;while(1){if(v>>>0>=u>>>0)break;v=v<<1}t=sa[c[f>>2]&1](c[$>>2]|0,v<<2)|0;if(!t)oa(1);u=c[w>>2]|0;m=r+12|0;if(u)dc(t|0,c[m>>2]|0,u<<2|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[m>>2]|0);c[m>>2]=t;c[w>>2]=v;t=c[x>>2]|0}else m=r+12|0;c[r>>2]=1;a[(c[r+8>>2]|0)+t>>0]=0;c[(c[m>>2]|0)+(c[x>>2]<<2)>>2]=K;c[x>>2]=(c[x>>2]|0)+1;f=c[p>>2]|0;r=c[$>>2]|0;ra[f&1](r,P);i=aa;return}J=sa[c[f>>2]&1](c[$>>2]|0,C*2096|0)|0;if(!J)oa(1);t=0;while(1){if((t|0)==(C|0))break;ac(J+(t*2096|0)|0,0,2080)|0;c[J+(t*2096|0)+2080>>2]=0;h[J+(t*2096|0)+2088>>3]=s;t=t+1|0}v=(K>>>0)/(C>>>0)|0;w=K+-41|0;m=7;x=0;while(1){if((x|0)==(C|0))break;t=((_(K,x)|0)>>>0)/(C>>>0)|0;if(x){m=m*16807|0;m=(m|0)==0?1:m;t=t+((m>>>0)%(v>>>0)|0)|0}u=J+(x*2096|0)+2080|0;c[u>>2]=(c[u>>2]|0)+40;u=P+(((t+40|0)>>>0<K>>>0?t:w)<<1)|0;t=41;while(1){t=t+-1|0;if(!t)break;k=J+(x*2096|0)+(e[u>>1]<<2)|0;c[k>>2]=(c[k>>2]|0)+1;u=u+2|0}x=x+1|0}c[Z>>2]=7;v=((K<<1>>>0)/40|0)+100+C+-1|0;v=v-((v>>>0)%(C>>>0)|0)|0;w=Y+2080|0;x=Y+2088|0;l=K>>>0>40;n=K+-39|0;A=Y+2080|0;B=0;while(1){if(B>>>0>=v>>>0)break;ac(Y|0,0,2084)|0;h[x>>3]=s;if(l){t=(c[Z>>2]|0)*16807|0;k=(t|0)==0;c[Z>>2]=k?1:t;m=40;t=((k?1:t)>>>0)%(n>>>0)|0}else{m=K;t=0}c[A>>2]=(c[A>>2]|0)+m;u=P+(t<<1)|0;t=m+1|0;while(1){t=t+-1|0;if(!t)break;k=Y+(e[u>>1]<<2)|0;c[k>>2]=(c[k>>2]|0)+1;u=u+2|0}t=(B>>>0)%(C>>>0)|0;m=J+(t*2096|0)+2080|0;c[m>>2]=(c[m>>2]|0)+(c[w>>2]|0);m=0;while(1){if((m|0)==520)break;k=J+(t*2096|0)+(m<<2)|0;c[k>>2]=(c[k>>2]|0)+(c[Y+(m<<2)>>2]|0);m=m+1|0}B=B+1|0}O=sa[c[f>>2]&1](c[$>>2]|0,K)|0;if(!O)oa(1);F=sa[c[f>>2]&1](c[$>>2]|0,C*4160|0)|0;if(!F)oa(1);G=sa[c[f>>2]&1](c[$>>2]|0,C<<3)|0;if(!G)oa(1);H=_(K,(C+7|0)>>>3)|0;H=sa[c[f>>2]&1](c[$>>2]|0,H)|0;if(!H)oa(1);I=sa[c[f>>2]&1](c[$>>2]|0,C<<1)|0;if(!I)oa(1);B=(c[o+4>>2]|0)<11?3:10;D=K+-1|0;E=O+D|0;N=0;A=0;g:while(1){if(A>>>0>=B>>>0){l=631;break}n=(C+7|0)>>>3;if(C>>>0>=257){l=568;break}h:do if(C>>>0<2){t=0;while(1){if((t|0)==(K|0)){v=1;break h}a[O+t>>0]=0;t=t+1|0}}else{ac(F|0,0,C*4160|0)|0;m=0;while(1){if((m|0)==(C|0)){t=520;break}t=c[J+(m*2096|0)+2080>>2]|0;if(t>>>0<256)y=+g[19516+(t<<2)>>2];else y=+Xb(+(t>>>0));h[F+(m<<3)>>3]=y;m=m+1|0}i:while(1){if(!t)break;t=t+-1|0;u=_(t,C)|0;v=0;while(1){if((v|0)==(C|0))continue i;z=+h[F+(v<<3)>>3];m=c[J+(v*2096|0)+(t<<2)>>2]|0;do if(m)if(m>>>0<256){y=+g[19516+(m<<2)>>2];break}else{y=+Xb(+(m>>>0));break}else y=-2.0;while(0);h[F+(u+v<<3)>>3]=z-y;v=v+1|0}}ac(G|0,0,C<<3|0)|0;ac(H|0,0,_(K,n)|0)|0;v=0;while(1){if(v>>>0>=K>>>0)break;t=_(e[P+(v<<1)>>1]|0,C)|0;m=O+v|0;z=1.e+99;u=0;while(1){if((u|0)==(C|0))break;o=G+(u<<3)|0;y=+h[o>>3]+ +h[F+(t+u<<3)>>3];h[o>>3]=y;if(y<z)a[m>>0]=u;else y=z;z=y;u=u+1|0}m=_(v,n)|0;if(v>>>0<2e3)y=(+(v>>>0)*.07/2.0e3+.77)*14.6;else y=14.6;u=0;while(1){if(u>>>0>=C>>>0)break;t=G+(u<<3)|0;ba=+h[t>>3]-z;h[t>>3]=ba;if(ba>=y){h[t>>3]=y;t=u>>>3;if(t>>>0>=n>>>0){l=599;break g}o=H+(m+t)|0;a[o>>0]=a[o>>0]|1<<(u&7)&255}u=u+1|0}v=v+1|0}m=_(D,n)|0;v=D;t=a[E>>0]|0;u=1;while(1){if(!v){v=u;break h}w=t&255;x=w>>>3;if(x>>>0>=n>>>0){l=606;break g}l=v+-1|0;m=m-n|0;v=O+l|0;if((a[H+(m+x)>>0]&(1<<(w&7)&255))<<24>>24){k=a[v>>0]|0;o=t<<24>>24==k<<24>>24;t=o?t:k;u=o?u:u+1|0}a[v>>0]=t;v=l}}while(0);t=0;while(1){if((t|0)==(C|0)){t=0;u=0;break}b[I+(t<<1)>>1]=256;t=t+1|0}while(1){if(u>>>0>=K>>>0){m=0;break}m=d[O+u>>0]|0;if(m>>>0>=C>>>0){l=615;break g}m=I+(m<<1)|0;if((b[m>>1]|0)==256){b[m>>1]=t;t=t+1<<16>>16}u=u+1|0}while(1){if(m>>>0>=K>>>0)break;k=O+m|0;o=b[I+(d[k>>0]<<1)>>1]|0;a[k>>0]=o;if((o&255)>>>0>=C>>>0){l=621;break g}m=m+1|0}u=t&65535;if(u>>>0>C>>>0){l=624;break}t=t&65535;m=0;while(1){if((m|0)==(t|0)){t=0;break}ac(J+(m*2096|0)|0,0,2080)|0;c[J+(m*2096|0)+2080>>2]=0;h[J+(m*2096|0)+2088>>3]=s;m=m+1|0}while(1){if((t|0)==(K|0))break;o=d[O+t>>0]|0;k=J+(o*2096|0)+(e[P+(t<<1)>>1]<<2)|0;c[k>>2]=(c[k>>2]|0)+1;o=J+(o*2096|0)+2080|0;c[o>>2]=(c[o>>2]|0)+1;t=t+1|0}N=v;C=u;A=A+1|0}if((l|0)==568)pa(404083,403848,80,404105);else if((l|0)==599)pa(404124,403848,130,404105);else if((l|0)==606)pa(404145,403848,141,404105);else if((l|0)==615)pa(404005,403848,165,404035);else if((l|0)==621)pa(404005,403848,172,404035);else if((l|0)==624)pa(404057,403848,174,404035);else if((l|0)==631){ra[c[p>>2]&1](c[$>>2]|0,F);ra[c[p>>2]&1](c[$>>2]|0,G);ra[c[p>>2]&1](c[$>>2]|0,H);ra[c[p>>2]&1](c[$>>2]|0,I);ra[c[p>>2]&1](c[$>>2]|0,J);t=N<<2;L=sa[c[f>>2]&1](c[$>>2]|0,t)|0;if(!L)oa(1);M=sa[c[f>>2]&1](c[$>>2]|0,t)|0;if(!M)oa(1);x=((N<<4)+1008|0)>>>6;o=sa[c[f>>2]&1](c[$>>2]|0,x*2096|0)|0;v=o;if(!o)oa(1);o=sa[c[f>>2]&1](c[$>>2]|0,x<<2)|0;w=o;if(!o)oa(1);G=sa[c[f>>2]&1](c[$>>2]|0,(N>>>0<64?N:64)*2096|0)|0;if(!G)oa(1);H=sa[c[f>>2]&1](c[$>>2]|0,49176)|0;if(!H)oa(1);ac(V|0,0,256)|0;ac(T|0,0,256)|0;ac(W|0,0,256)|0;ac(U|0,0,256)|0;ac(M|0,0,t|0)|0;m=0;u=0;while(1){if(u>>>0>=K>>>0)break;if(m>>>0>=N>>>0){l=646;break}t=M+(m<<2)|0;c[t>>2]=(c[t>>2]|0)+1;t=u+1|0;if((t|0)!=(K|0)?(a[O+u>>0]|0)==(a[O+t>>0]|0):0){u=t;continue}m=m+1|0;u=t}if((l|0)==646)pa(403825,403848,231,403889);if((m|0)!=(N|0))pa(403911,403848,237,403889);C=x;B=x;K=0;t=0;F=0;while(1){if(N>>>0<=F>>>0){l=688;break}E=N-F|0;E=E>>>0<64?E:64;n=0;while(1){if((n|0)==(E|0))break;u=G+(n*2096|0)|0;ac(u|0,0,2080)|0;x=G+(n*2096|0)+2080|0;c[x>>2]=0;h[G+(n*2096|0)+2088>>3]=s;l=M+(F+n<<2)|0;m=0;while(1){if(m>>>0>=(c[l>>2]|0)>>>0)break;o=G+(n*2096|0)+(e[P+(t<<1)>>1]<<2)|0;c[o>>2]=(c[o>>2]|0)+1;c[x>>2]=(c[x>>2]|0)+1;t=t+1|0;m=m+1|0}h[G+(n*2096|0)+2088>>3]=+Ya(u);c[T+(n<<2)>>2]=n;c[W+(n<<2)>>2]=n;c[V+(n<<2)>>2]=1;n=n+1|0}D=tb(G,V,W,T,H,E,E,64,2048)|0;m=K+D|0;if(C>>>0<m>>>0){u=(C|0)==0;x=u?m:C;while(1){if(x>>>0>=m>>>0)break;x=x<<1}m=sa[c[f>>2]&1](c[$>>2]|0,x*2096|0)|0;if(!m){l=665;break}if(!u)dc(m|0,v|0,C*2096|0)|0;ra[c[p>>2]&1](c[$>>2]|0,v);v=m;C=x}m=K+D|0;if(B>>>0<m>>>0){u=(B|0)==0;x=u?m:B;while(1){if(x>>>0>=m>>>0)break;x=x<<1}m=sa[c[f>>2]&1](c[$>>2]|0,x<<2)|0;if(!m){l=674;break}if(!u)dc(m|0,w|0,B<<2|0)|0;ra[c[p>>2]&1](c[$>>2]|0,w);w=m;B=x}m=v;u=w;A=D+K|0;x=K;l=K;n=0;while(1){if((n|0)==(D|0)){m=0;break}o=T+(n<<2)|0;k=c[o>>2]|0;dc(m+(x*2096|0)|0,G+(k*2096|0)|0,2096)|0;c[u+(l<<2)>>2]=c[V+(k<<2)>>2];c[U+(c[o>>2]<<2)>>2]=n;x=x+1|0;l=l+1|0;n=n+1|0}while(1){if((m|0)==(E|0))break;c[L+(F+m<<2)>>2]=K+(c[U+(c[W+(m<<2)>>2]<<2)>>2]|0);m=m+1|0}m=D+K|0;if((K+D|0)!=(m|0)){l=684;break}if((m|0)!=(A|0)){l=686;break}K=m;F=F+64|0}if((l|0)==665)oa(1);else if((l|0)==674)oa(1);else if((l|0)==684)pa(403935,403848,273,403889);else if((l|0)==686)pa(403969,403848,274,403889);else if((l|0)==688){ra[c[p>>2]&1](c[$>>2]|0,G);W=K<<6;u=_(K>>>1,K)|0;u=W>>>0<u>>>0?W:u;do if((u+1|0)>>>0>2049){ra[c[p>>2]&1](c[$>>2]|0,H);t=sa[c[f>>2]&1](c[$>>2]|0,(u*24|0)+24|0)|0;if(t){X=t;break}oa(1)}else X=H;while(0);m=K<<2;I=sa[c[f>>2]&1](c[$>>2]|0,m)|0;if(!I)oa(1);t=0;while(1){if((t|0)==(K|0))break;c[I+(t<<2)>>2]=t;t=t+1|0}H=v;G=tb(H,w,L,I,X,K,N,256,u)|0;ra[c[p>>2]&1](c[$>>2]|0,X);ra[c[p>>2]&1](c[$>>2]|0,w);J=sa[c[f>>2]&1](c[$>>2]|0,m)|0;if(!J)oa(1);t=0;while(1){if((t|0)==(K|0))break;c[J+(t<<2)>>2]=-1;t=t+1|0}B=Y+2080|0;C=Y+2088|0;D=Z+2080|0;E=Z+2080|0;t=0;m=0;F=0;while(1){if((F|0)==(N|0))break;ac(Y|0,0,2084)|0;h[C>>3]=s;u=M+(F<<2)|0;w=0;A=m;m=0;while(1){if(m>>>0>=(c[u>>2]|0)>>>0)break;w=Y+(e[P+(A<<1)>>1]<<2)|0;c[w>>2]=(c[w>>2]|0)+1;w=(c[B>>2]|0)+1|0;c[B>>2]=w;A=A+1|0;m=m+1|0}u=c[((F|0)==0?L:L+(F+-1<<2)|0)>>2]|0;n=(w|0)==0;if(n)y=0.0;else{dc(Z|0,Y|0,2096)|0;c[E>>2]=(c[E>>2]|0)+(c[H+(u*2096|0)+2080>>2]|0);m=0;while(1){if((m|0)==520)break;X=Z+(m<<2)|0;c[X>>2]=(c[X>>2]|0)+(c[H+(u*2096|0)+(m<<2)>>2]|0);m=m+1|0}y=+Ya(Z);y=y-+h[H+(u*2096|0)+2088>>3]}m=u;l=0;while(1){if((l|0)==(G|0))break;x=I+(l<<2)|0;u=c[x>>2]|0;if(n)z=0.0;else{dc(Z|0,Y|0,2096)|0;c[D>>2]=(c[D>>2]|0)+(c[H+(u*2096|0)+2080>>2]|0);w=0;while(1){if((w|0)==520)break;X=Z+(w<<2)|0;c[X>>2]=(c[X>>2]|0)+(c[H+(u*2096|0)+(w<<2)>>2]|0);w=w+1|0}z=+Ya(Z);z=z-+h[H+(u*2096|0)+2088>>3]}if(z<y){y=z;m=c[x>>2]|0}l=l+1|0}c[L+(F<<2)>>2]=m;m=J+(m<<2)|0;if((c[m>>2]|0)==-1){c[m>>2]=t;t=t+1|0}m=A;F=F+1|0}ra[c[p>>2]&1](c[$>>2]|0,I);ra[c[p>>2]&1](c[$>>2]|0,v);w=r+16|0;t=c[w>>2]|0;if(t>>>0<N>>>0){v=(t|0)==0?N:t;while(1){if(v>>>0>=N>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v)|0;if(!m)oa(1);u=c[w>>2]|0;t=r+8|0;if(u)dc(m|0,c[t>>2]|0,u|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v}w=r+20|0;t=c[w>>2]|0;if(t>>>0<N>>>0){v=(t|0)==0?N:t;while(1){if(v>>>0>=N>>>0)break;v=v<<1}m=sa[c[f>>2]&1](c[$>>2]|0,v<<2)|0;if(!m)oa(1);u=c[w>>2]|0;t=r+12|0;if(u)dc(m|0,c[t>>2]|0,u<<2|0)|0;ra[c[p>>2]&1](c[$>>2]|0,c[t>>2]|0);c[t>>2]=m;c[w>>2]=v}else t=r+12|0;w=r+8|0;l=0;m=0;n=0;u=0;while(1){if((u|0)==(N|0))break;v=m+(c[M+(u<<2)>>2]|0)|0;x=u+1|0;m=c[L+(u<<2)>>2]|0;if((x|0)!=(N|0)?(m|0)==(c[L+(x<<2)>>2]|0):0){m=v;u=x;continue}u=c[J+(m<<2)>>2]|0;f=u&255;a[(c[w>>2]|0)+l>>0]=f;c[(c[t>>2]|0)+(l<<2)>>2]=v;l=l+1|0;m=0;n=((n&255)>(f&255)?n&255:u&255)&255;u=x}c[r+4>>2]=l;c[r>>2]=(n&255)+1;ra[c[p>>2]&1](c[$>>2]|0,J);ra[c[p>>2]&1](c[$>>2]|0,M);ra[c[p>>2]&1](c[$>>2]|0,L);ra[c[p>>2]&1](c[$>>2]|0,O);f=c[p>>2]|0;r=c[$>>2]|0;ra[f&1](r,P);i=aa;return}}}function _a(b,f,g,h,j){b=b|0;f=f|0;g=g|0;h=h|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0;A=i;i=i+1552|0;y=A+840|0;z=A+136|0;v=A+72|0;w=A+112|0;x=A+76|0;t=A;c[v>>2]=0;l=w;m=l+18|0;do{a[l>>0]=0;l=l+1|0}while((l|0)<(m|0));l=t;m=l+72|0;do{c[l>>2]=0;l=l+4|0}while((l|0)<(m|0));if(f>>>0<705){s=f;k=0}else pa(404299,404333,308,404371);while(1){if(k>>>0>=f>>>0)break;if(a[b+(f-k+-1)>>0]|0)break;s=s+-1|0;k=k+1|0}if(f>>>0>50){m=1;f=1;q=0;n=0;o=0;while(1){if(q>>>0>=s>>>0)break;l=a[b+q>>0]|0;p=1;k=q;while(1){k=k+1|0;if(k>>>0>=s>>>0)break;if((a[b+k>>0]|0)!=l<<24>>24)break;p=p+1|0}if(p>>>0>2){r=l<<24>>24==0;f=r?f+1|0:f;k=r?o+p|0:o;if(p>>>0>3?!r:0){m=m+1|0;l=n+p|0}else l=n}else{l=n;k=o}q=q+p|0;n=l;o=k}k=n>>>0>m<<1>>>0&1;f=o>>>0>f<<1>>>0&1}else{k=0;f=0}r=f<<24>>24==0;p=k<<24>>24==0;n=8;q=0;a:while(1){if(q>>>0>=s>>>0)break;o=a[b+q>>0]|0;l=o<<24>>24==0;if(l)if(r){f=1;u=30}else{k=q;f=1;u=23}else if(p){f=1;u=43}else{k=q;f=1;u=23}b:do if((u|0)==23){while(1){u=0;k=k+1|0;if(k>>>0>=s>>>0)break;if((a[b+k>>0]|0)!=o<<24>>24)break;f=f+1|0;u=23}if(!l)if(!f){u=42;break a}else{u=43;break}if((f|0)!=11)if(f>>>0<3){u=30;break}else m=f;else{a[y+(c[v>>2]|0)>>0]=0;a[z+(c[v>>2]|0)>>0]=0;c[v>>2]=(c[v>>2]|0)+1;m=10}l=c[v>>2]|0;k=l;m=m+-3|0;while(1){a[y+k>>0]=17;a[z+(c[v>>2]|0)>>0]=m&7;k=(c[v>>2]|0)+1|0;c[v>>2]=k;m=m>>>3;if(!m){m=l;break}m=m+-1|0}while(1){k=k+-1|0;if(m>>>0>=k>>>0)break;D=y+m|0;B=a[D>>0]|0;o=y+k|0;a[D>>0]=a[o>>0]|0;a[o>>0]=B;m=m+1|0}k=c[v>>2]|0;while(1){k=k+-1|0;if(l>>>0>=k>>>0){k=n;break b}o=z+l|0;B=a[o>>0]|0;D=z+k|0;a[o>>0]=a[D>>0]|0;a[D>>0]=B;l=l+1|0}}while(0);c:do if((u|0)==30){u=0;k=0;while(1){if((k|0)==(f|0)){k=n;break c}a[y+(c[v>>2]|0)>>0]=0;a[z+(c[v>>2]|0)>>0]=0;c[v>>2]=(c[v>>2]|0)+1;k=k+1|0}}else if((u|0)==43){u=0;if(n<<24>>24==o<<24>>24)k=f;else{a[y+(c[v>>2]|0)>>0]=o;a[z+(c[v>>2]|0)>>0]=0;c[v>>2]=(c[v>>2]|0)+1;k=f+-1|0}if((k|0)==7){a[y+(c[v>>2]|0)>>0]=o;a[z+(c[v>>2]|0)>>0]=0;c[v>>2]=(c[v>>2]|0)+1;k=k+-1|0}if(k>>>0<3){l=0;while(1){if((l|0)==(k|0)){k=o;break c}a[y+(c[v>>2]|0)>>0]=o;a[z+(c[v>>2]|0)>>0]=0;c[v>>2]=(c[v>>2]|0)+1;l=l+1|0}}l=c[v>>2]|0;n=l;m=k+-3|0;while(1){a[y+n>>0]=16;a[z+(c[v>>2]|0)>>0]=m&3;k=(c[v>>2]|0)+1|0;c[v>>2]=k;m=m>>>2;if(!m){m=l;break}n=k;m=m+-1|0}while(1){k=k+-1|0;if(m>>>0>=k>>>0)break;n=y+m|0;B=a[n>>0]|0;D=y+k|0;a[n>>0]=a[D>>0]|0;a[D>>0]=B;m=m+1|0}k=c[v>>2]|0;while(1){k=k+-1|0;if(l>>>0>=k>>>0){k=o;break c}n=z+l|0;B=a[n>>0]|0;D=z+k|0;a[n>>0]=a[D>>0]|0;a[D>>0]=B;l=l+1|0}}while(0);n=k;q=q+f|0}if((u|0)==42)pa(406724,406674,168,406740);f=c[v>>2]|0;k=0;while(1){if((k|0)==(f|0)){r=0;k=0;l=0;break}D=t+(d[y+k>>0]<<2)|0;c[D>>2]=(c[D>>2]|0)+1;k=k+1|0}while(1){if(l>>>0>=18){q=k;break}if(c[t+(l<<2)>>2]|0)if(k)if((k|0)==1){q=2;break}else f=r;else{f=l;k=1}else f=r;r=f;l=l+1|0}Hb(t,18,5,g,w);Jb(w,18,x);d:do if((q|0)>1){k=18;while(1){if(!k)break d;f=k+-1|0;if(!(a[w+(d[405371+f>>0]|0)>>0]|0))k=f;else break}}else k=18;while(0);if((a[w+1>>0]|0)==0?(a[w+2>>0]|0)==0:0)f=(a[w+3>>0]|0)==0?3:2;else f=0;g=c[h>>2]|0;p=j+(g>>>3)|0;B=d[p>>0]|0;g=cc(f|0,0,g&7|0)|0;D=C;g=B|g;B=p;a[B>>0]=g;a[B+1>>0]=g>>8;a[B+2>>0]=g>>16;a[B+3>>0]=g>>24;p=p+4|0;a[p>>0]=D;a[p+1>>0]=D>>8;a[p+2>>0]=D>>16;a[p+3>>0]=D>>24;p=(c[h>>2]|0)+2|0;c[h>>2]=p;while(1){if(f>>>0>=k>>>0)break;m=d[w+(d[405371+f>>0]|0)>>0]|0;l=a[405389+m>>0]|0;m=d[405395+m>>0]|0;n=j+(p>>>3)|0;o=d[n>>0]|0;D=bc(m|0,0,l&255|0)|0;if(!((D|0)==0&(C|0)==0)){u=76;break}t=cc(m|0,0,p&7|0)|0;B=C;t=o|t;D=n;g=D;a[g>>0]=t;a[g+1>>0]=t>>8;a[g+2>>0]=t>>16;a[g+3>>0]=t>>24;D=D+4|0;a[D>>0]=B;a[D+1>>0]=B>>8;a[D+2>>0]=B>>16;a[D+3>>0]=B>>24;D=(c[h>>2]|0)+(l&255)|0;c[h>>2]=D;p=D;f=f+1|0}if((u|0)==76)pa(406196,406218,54,406251);if((q|0)==1)a[w+r>>0]=0;p=c[v>>2]|0;q=0;e:while(1){if(q>>>0>=p>>>0){u=94;break}o=d[y+q>>0]|0;f=a[w+o>>0]|0;k=e[x+(o<<1)>>1]|0;l=c[h>>2]|0;m=j+(l>>>3)|0;n=d[m>>0]|0;D=bc(k|0,0,f&255|0)|0;if(!((D|0)==0&(C|0)==0)){u=83;break}if((f&255)>=57){u=85;break}v=cc(k|0,0,l&7|0)|0;D=C;v=n|v;B=m;a[B>>0]=v;a[B+1>>0]=v>>8;a[B+2>>0]=v>>16;a[B+3>>0]=v>>24;m=m+4|0;a[m>>0]=D;a[m+1>>0]=D>>8;a[m+2>>0]=D>>16;a[m+3>>0]=D>>24;m=(c[h>>2]|0)+(f&255)|0;c[h>>2]=m;switch(o|0){case 16:{f=d[z+q>>0]|0;k=j+(m>>>3)|0;l=d[k>>0]|0;if(!((f&252|0)==0&0==0)){u=88;break e}u=cc(f|0,0,m&7|0)|0;B=C;u=l|u;D=k;v=D;a[v>>0]=u;a[v+1>>0]=u>>8;a[v+2>>0]=u>>16;a[v+3>>0]=u>>24;D=D+4|0;a[D>>0]=B;a[D+1>>0]=B>>8;a[D+2>>0]=B>>16;a[D+3>>0]=B>>24;c[h>>2]=(c[h>>2]|0)+2;break}case 17:{f=d[z+q>>0]|0;k=j+(m>>>3)|0;l=d[k>>0]|0;if(!((f&248|0)==0&0==0)){u=91;break e}u=cc(f|0,0,m&7|0)|0;B=C;u=l|u;D=k;v=D;a[v>>0]=u;a[v+1>>0]=u>>8;a[v+2>>0]=u>>16;a[v+3>>0]=u>>24;D=D+4|0;a[D>>0]=B;a[D+1>>0]=B>>8;a[D+2>>0]=B>>16;a[D+3>>0]=B>>24;c[h>>2]=(c[h>>2]|0)+3;break}default:{}}q=q+1|0}if((u|0)==83)pa(406196,406218,54,406251);else if((u|0)==85)pa(406267,406218,55,406251);else if((u|0)==88)pa(406196,406218,54,406251);else if((u|0)==91)pa(406196,406218,54,406251);else if((u|0)==94){i=A;return}}
function ta(a){a=a|0;var b=0;b=i;i=i+a|0;i=i+15&-16;return b|0}function ua(){return i|0}function va(a){a=a|0;i=a}function wa(a,b){a=a|0;b=b|0;i=a;j=b}function xa(a,b){a=a|0;b=b|0;if(!n){n=a;o=b}}function ya(b){b=b|0;a[k>>0]=a[b>>0];a[k+1>>0]=a[b+1>>0];a[k+2>>0]=a[b+2>>0];a[k+3>>0]=a[b+3>>0]}function za(b){b=b|0;a[k>>0]=a[b>>0];a[k+1>>0]=a[b+1>>0];a[k+2>>0]=a[b+2>>0];a[k+3>>0]=a[b+3>>0];a[k+4>>0]=a[b+4>>0];a[k+5>>0]=a[b+5>>0];a[k+6>>0]=a[b+6>>0];a[k+7>>0]=a[b+7>>0]}function Aa(a){a=a|0;C=a}function Ba(){return C|0}function Ca(e,f,h,j,k,l,m){e=e|0;f=f|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0,p=0,q=0,r=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,qa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0;Oa=i;i=i+224|0;Ha=Oa+200|0;Ca=Oa+184|0;Ka=Oa+168|0;Ja=Oa+152|0;Fa=Oa+144|0;Ia=Oa+140|0;La=Oa+136|0;Ga=Oa+24|0;q=Oa+20|0;u=Oa+16|0;r=Oa+12|0;v=Oa+8|0;w=Oa+4|0;Na=Oa;c[Na>>2]=l;p=k;z=m;Ma=j>>>24;Ma=(Ma<<2|2)+((j-(Ma<<24)|0)>>>0>1048576?4:3)+1+j|0;n=(j|0)==0;Ma=n?1:Ma>>>0<j>>>0?0:Ma;if(!l){k=0;Na=c[Na>>2]|0;Na=k?Na:-1;i=Oa;return Na|0}if(n){c[Na>>2]=1;a[m>>0]=6;k=1;Na=c[Na>>2]|0;Na=k?Na:-1;i=Oa;return Na|0}do if((e|0)==10){if((f|0)>=16)if((f|0)>24)f=24;else x=7;else{f=16;x=7}q=1<<f;va=q+-16|0;c[Ca>>2]=c[4721];c[Ca+4>>2]=c[4722];c[Ca+8>>2]=c[4723];c[Ca+12>>2]=c[4724];c[Ka>>2]=c[4721];c[Ka+4>>2]=c[4722];c[Ka+8>>2]=c[4723];c[Ka+12>>2]=c[4724];wa=c[Na>>2]|0;q=q>>>0>j>>>0?j:q;xa=1<<((f|0)>23?24:f+1|0);ya=xa>>>3;c[Ja>>2]=0;za=Ja+4|0;c[za>>2]=10;n=Ja+8|0;c[n>>2]=f;Aa=Ja+12|0;c[Aa>>2]=0;c[za>>2]=10;za=(f|0)>16?((f|0)>18?18:f):16;c[Aa>>2]=za;za=1<<za;c[Ha>>2]=1;Aa=Ha+4|0;c[Aa>>2]=1;Ba=Ha+8|0;c[Ba>>2]=0;if(j>>>0>=2147483649)pa(406392,406053,1025,406415);if((f|0)!=16)if((f|0)==17){r=1;h=7}else{r=((f<<1)+222|1)&255;h=4}else{r=0;h=1}ua=Lb(0,524308)|0;if(!ua)oa(1);c[ua+524292>>2]=0;c[ua+524300>>2]=0;a[ua+524304>>0]=1;p=ua+524304|0;f=1<<c[n>>2];c[ua>>2]=f+-1;f=1-f|0;ta=ua+524296|0;c[ta>>2]=f;n=0;while(1){if((n|0)==131072)break;c[ua+4+(n<<2)>>2]=f;n=n+1|0}f=ua+524300|0;do if(q>>>0>(c[f>>2]|0)>>>0){n=ua+524292|0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[n>>2]|0);c[n>>2]=0;o=sa[c[Ha>>2]&1](c[Ba>>2]|0,q<<3)|0;if(!o)oa(1);else{c[n>>2]=o;c[f>>2]=q;break}}while(0);a[p>>0]=0;W=ua+524292|0;X=Ga+8|0;Y=Ga+12|0;Z=Ga+28|0;$=Ga+32|0;ba=Ga+36|0;ca=Ga+48|0;da=Ga+52|0;ea=Ga+56|0;fa=Ga+60|0;ga=Ga+72|0;ha=Ga+80|0;ia=Ga+88|0;ja=Ga+96|0;ka=Ga+104|0;la=Ga+8|0;ma=Ga+32|0;qa=Ga+56|0;n=z;U=0;V=1;T=0;t=0;u=0;a:while(1){f=(V&1)==0;if(!((f^1)&U>>>0<j>>>0)){x=93;break}R=U+xa|0;R=R>>>0>j>>>0?j:R;P=(((R-U|0)>>>0)/12|0)+16|0;c[Fa>>2]=0;c[Ia>>2]=0;f=0;Q=U;L=0;N=0;v=0;S=0;while(1){if(R>>>0<=Q>>>0){p=f;z=N;e=v;o=S;break}O=R-Q|0;O=O>>>0<za>>>0?O:za;M=sa[c[Ha>>2]&1](c[Ba>>2]|0,(O<<4)+16|0)|0;if(!M){x=27;break a}f=O+1|0;o=0;while(1){if((o|0)==(f|0))break;c[M+(o<<4)>>2]=1;c[M+(o<<4)+4>>2]=0;c[M+(o<<4)+8>>2]=0;g[M+(o<<4)+12>>2]=s;o=o+1|0}b:do if(O>>>0>2&Q>>>0>127){K=Q+-127|0;J=K+O|0;J=Q>>>0<J>>>0?Q:J;while(1){if(K>>>0>=J>>>0)break b;p=c[ua>>2]|0;F=Q-K|0;F=p-(F>>>0<15?15:F)|0;G=K&2147483647;H=k+G|0;D=ua+4+((_(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24,506832829)|0)>>>15<<2)|0;B=c[D>>2]|0;p=(K&p)<<1;c[D>>2]=K;D=0;q=0;E=0;o=p;p=p|1;I=64;while(1){A=B&2147483647;if((K|0)==(B|0)|(K-B|0)>>>0>F>>>0|(I|0)==0){x=36;break}z=q>>>0<E>>>0?q:E;if(z>>>0>=129){x=38;break a}x=G+z|0;y=k+(A+128)|0;e=k+(A+124)|0;w=0;f=k+(A+z)|0;while(1){if(f>>>0>e>>>0)break;Pa=k+(x+w)|0;if((d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24|0)!=(d[Pa>>0]|d[Pa+1>>0]<<8|d[Pa+2>>0]<<16|d[Pa+3>>0]<<24|0))break;w=w+4|0;f=f+4|0}while(1){if(f>>>0>=y>>>0)break;if((a[k+(x+w)>>0]|0)!=(a[f>>0]|0))break;w=w+1|0;f=f+1|0}z=z+w|0;c:do if(z){f=z;x=H;y=k+A|0;while(1){e=a[x>>0]|0;w=a[y>>0]|0;if(e<<24>>24!=w<<24>>24)break;f=f+-1|0;if(!f)break c;else{x=x+1|0;y=y+1|0}}if(e<<24>>24!=w<<24>>24){x=51;break a}}while(0);if(D){x=53;break a}if(z>>>0>=128){x=55;break}f=c[W>>2]|0;if((d[k+(G+z)>>0]|0)>(d[k+(A+z)>>0]|0)){c[f+(o<<2)>>2]=B;e=(B&c[ua>>2])<<1|1;q=z;f=E;o=e;e=(c[W>>2]|0)+(e<<2)|0}else{c[f+(p<<2)>>2]=B;e=(B&c[ua>>2])<<1;f=z;p=e;e=(c[W>>2]|0)+(e<<2)|0}D=0;E=f;B=c[e>>2]|0;I=I+-1|0}if((x|0)==36){c[(c[W>>2]|0)+(o<<2)>>2]=c[ta>>2];c[(c[W>>2]|0)+(p<<2)>>2]=c[ta>>2]}else if((x|0)==55){Pa=c[W>>2]|0;c[Pa+(o<<2)>>2]=c[Pa+((B&c[ua>>2])<<1<<2)>>2];Pa=c[W>>2]|0;c[Pa+(p<<2)>>2]=c[Pa+(((B&c[ua>>2])<<1|1)<<2)>>2]}K=K+1|0}}while(0);o=S+(Ea(Ha,O,Q,k,2147483647,Ja,va,Ca,ua,M)|0)|0;p=o+1|0;p=P>>>0>p>>>0?P:p;if((L|0)!=(p|0)){f=sa[c[Ha>>2]&1](c[Ba>>2]|0,p<<4)|0;q=f;if(!f){x=63;break a}if(N){Pa=N;dc(f|0,Pa|0,S<<4|0)|0;ra[c[Aa>>2]&1](c[Ba>>2]|0,Pa)}}else{p=L;q=N}Da(O,Q,va,M,Ca,Fa,q+(S<<4)|0,Ia);v=v+O|0;ra[c[Aa>>2]&1](c[Ba>>2]|0,M);f=c[Ia>>2]|0;if(f>>>0>ya>>>0){p=f;z=q;e=v;break}if(o>>>0>ya>>>0){p=f;z=q;e=v;break}else{Q=Q+O|0;L=p;N=q;S=o}}f=c[Fa>>2]|0;if(!f)f=p;else{p=o+1|0;Pa=z;c[Pa+(o<<4)>>2]=f;c[Pa+(o<<4)+4>>2]=67108864;c[Pa+(o<<4)+8>>2]=0;b[Pa+(o<<4)+14>>1]=16;o=Pa+(o<<4)+12|0;do if(f>>>0>=6){if(f>>>0<130){Pa=f+-2|0;f=((aa(Pa|0)|0)^31)+-1|0;f=(f<<1)+(Pa>>>f)+2|0;break}if(f>>>0<2114){f=((aa(f+-66|0)|0)^31)+10|0;break}if(f>>>0<6210)f=21;else f=f>>>0<22594?22:23}while(0);f=f&65535;b[o>>1]=b[88156+((f>>>3)*3<<1)>>1]|(f<<3&56|2)&65535;f=(c[Ia>>2]|0)+(c[Fa>>2]|0)|0;c[Ia>>2]=f;o=p}w=U+e|0;v=(w|0)==(j|0);h=h&255;c[La>>2]=h;do if(!e){f=sa[c[Ha>>2]&1](c[Ba>>2]|0,16)|0;if(!f){x=79;break a}a[f>>0]=r;Pa=c[La>>2]|0;U=f+(Pa>>>3)|0;S=d[U>>0]|0;R=cc(3,0,Pa&7|0)|0;T=C;R=S|R;S=U;a[S>>0]=R;a[S+1>>0]=R>>8;a[S+2>>0]=R>>16;a[S+3>>0]=R>>24;U=U+4|0;a[U>>0]=T;a[U+1>>0]=T>>8;a[U+2>>0]=T>>16;a[U+3>>0]=T>>24;c[La>>2]=Pa+9&-8}else{if(!(Fb(k,2147483647,U,0,e,f,o)|0)){c[Ca>>2]=c[Ka>>2];c[Ca+4>>2]=c[Ka+4>>2];c[Ca+8>>2]=c[Ka+8>>2];c[Ca+12>>2]=c[Ka+12>>2];f=sa[c[Ha>>2]&1](c[Ba>>2]|0,e+16|0)|0;if(!f){x=83;break a}a[f>>0]=r;bb(v,k,U,2147483647,e,La,f);break};c[Ga>>2]=0;c[Ga+4>>2]=0;c[Ga+8>>2]=0;c[Ga+12>>2]=0;c[Ga+16>>2]=0;c[Ga+20>>2]=0;c[Ga+24>>2]=0;c[Z>>2]=0;c[Z+4>>2]=0;c[Z+8>>2]=0;c[Z+12>>2]=0;c[Z+16>>2]=0;c[ca>>2]=0;c[da>>2]=0;c[da+4>>2]=0;c[da+8>>2]=0;c[da+12>>2]=0;c[da+16>>2]=0;x=ga;y=x+40|0;do{c[x>>2]=0;x=x+4|0}while((x|0)<(y|0));p=Wb(k,U,2147483647,e)|0;p=p?2:3;q=z;Nb(Ha,k,U,2147483647,Ja,T,t,q,o,p,Ga);Ob(0,0,Ga);f=sa[c[Ha>>2]&1](c[Ba>>2]|0,(e<<1)+502|0)|0;if(!f){x=86;break a}a[f>>0]=r;ab(Ha,k,U,e,2147483647,T,t,v,0,0,p,q,o,Ga,La,f);if((e+4|0)>>>0<(c[La>>2]|0)>>>3>>>0){c[Ca>>2]=c[Ka>>2];c[Ca+4>>2]=c[Ka+4>>2];c[Ca+8>>2]=c[Ka+8>>2];c[Ca+12>>2]=c[Ka+12>>2];a[f>>0]=r;c[La>>2]=h;bb(v,k,U,2147483647,e,La,f)}ra[c[Aa>>2]&1](c[Ba>>2]|0,c[la>>2]|0);c[X>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[Y>>2]|0);c[Y>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ma>>2]|0);c[$>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ba>>2]|0);c[ba>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[qa>>2]|0);c[ea>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[fa>>2]|0);c[fa>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ga>>2]|0);c[ga>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ha>>2]|0);c[ha>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ia>>2]|0);c[ia>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ja>>2]|0);c[ja>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[ka>>2]|0);c[ka>>2]=0}while(0);p=c[La>>2]|0;o=p>>>3;r=a[f+o>>0]|0;q=a[k+(w+-1)>>0]|0;t=a[k+(w+-2)>>0]|0;c[Ka>>2]=c[Ca>>2];c[Ka+4>>2]=c[Ca+4>>2];c[Ka+8>>2]=c[Ca+8>>2];c[Ka+12>>2]=c[Ca+12>>2];u=u+o|0;if(u>>>0>wa>>>0)o=0;else{dc(n|0,f|0,o|0)|0;n=n+o|0;o=V}ra[c[Aa>>2]&1](c[Ba>>2]|0,f);ra[c[Aa>>2]&1](c[Ba>>2]|0,z);h=p&7;U=w;V=o;T=q}if((x|0)==27)oa(1);else if((x|0)==38)pa(405873,405905,373,405932);else if((x|0)==51)pa(405955,405905,378,405932);else if((x|0)==53)na();else if((x|0)==63)oa(1);else if((x|0)==79)oa(1);else if((x|0)==83)oa(1);else if((x|0)==86)oa(1);else if((x|0)==93){c[Na>>2]=u;ra[c[Aa>>2]&1](c[Ba>>2]|0,c[W>>2]|0);c[W>>2]=0;ra[c[Aa>>2]&1](c[Ba>>2]|0,ua);if(f){x=107;break}if((Ma|0)!=0?(c[Na>>2]|0)>>>0>Ma>>>0:0){x=106;break}else f=1;Pa=c[Na>>2]|0;Pa=f?Pa:-1;i=Oa;return Pa|0}}else{t=Yb(5240)|0;if(!t){k=0;Pa=c[Na>>2]|0;Pa=k?Pa:-1;i=Oa;return Pa|0}c[t+16>>2]=1;c[t+20>>2]=1;c[t+24>>2]=0;c[t>>2]=0;c[t+4>>2]=11;c[t+8>>2]=22;c[t+12>>2]=0;x=t+80|0;c[x>>2]=0;c[x+4>>2]=0;c[t+128>>2]=0;c[t+132>>2]=0;c[t+136>>2]=0;x=t+144|0;c[t+4300>>2]=0;c[t+4304>>2]=0;n=t+5204|0;c[t+5232>>2]=0;a[t+5236>>0]=0;a[t+5237>>0]=0;c[t+28>>2]=0;o=t+32|0;c[x>>2]=0;c[x+4>>2]=0;c[x+8>>2]=0;c[x+12>>2]=0;x=t+194|0;y=x+10|0;do{a[x>>0]=0;x=x+1|0}while((x|0)<(y|0));c[n>>2]=0;c[n+4>>2]=0;c[n+8>>2]=0;c[n+12>>2]=0;c[n+16>>2]=0;c[n+20>>2]=0;n=t+104|0;x=o;y=x+44|0;do{c[x>>2]=0;x=x+4|0}while((x|0)<(y|0));c[n>>2]=0;c[t+108>>2]=0;c[t+112>>2]=0;c[t+116>>2]=0;c[t+124>>2]=0;c[t+120>>2]=0;c[t+160>>2]=4;c[t+164>>2]=11;c[t+168>>2]=15;c[t+172>>2]=16;Pa=t+176|0;La=t+160|0;c[Pa>>2]=c[La>>2];c[Pa+4>>2]=c[La+4>>2];c[Pa+8>>2]=c[La+8>>2];c[Pa+12>>2]=c[La+12>>2];if(!t){k=0;Pa=c[Na>>2]|0;Pa=k?Pa:-1;i=Oa;return Pa|0}c[q>>2]=j;c[u>>2]=p;c[r>>2]=c[Na>>2];c[v>>2]=z;c[w>>2]=0;if(!(a[t+5237>>0]&1)){c[t+4>>2]=e;c[t+8>>2]=f;c[t>>2]=h}f=Ab(t,q,u,r,v,w)|0;if((c[t+5232>>2]|0)==2?(c[t+5220>>2]|0)==0:0)h=f&1;else h=0;c[Na>>2]=c[w>>2];q=t+200|0;f=c[t+20>>2]|0;n=c[t+24>>2]|0;ra[f&1](n,c[q>>2]|0);c[q>>2]=0;q=t+124|0;o=t+20|0;p=t+24|0;ra[c[o>>2]&1](c[p>>2]|0,c[q>>2]|0);c[q>>2]=0;ra[c[o>>2]&1](c[p>>2]|0,c[t+112>>2]|0);c[t+112>>2]=0;q=t+28|0;r=c[t+72>>2]|0;if(r){Pa=r+524292|0;ra[c[o>>2]&1](c[p>>2]|0,c[Pa>>2]|0);c[Pa>>2]=0}Pa=t+72|0;ra[c[o>>2]&1](c[p>>2]|0,c[q>>2]|0);c[q>>2]=0;La=t+32|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+36|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+40|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+44|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+48|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+52|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+56|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+60|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+64|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;La=t+68|0;ra[c[o>>2]&1](c[p>>2]|0,c[La>>2]|0);c[La>>2]=0;ra[c[o>>2]&1](c[p>>2]|0,c[Pa>>2]|0);c[Pa>>2]=0;Pa=t+4300|0;ra[c[o>>2]&1](c[p>>2]|0,c[Pa>>2]|0);c[Pa>>2]=0;Pa=t+5208|0;ra[c[o>>2]&1](c[p>>2]|0,c[Pa>>2]|0);c[Pa>>2]=0;Pa=t+5212|0;ra[c[o>>2]&1](c[p>>2]|0,c[Pa>>2]|0);c[Pa>>2]=0;ra[f&1](n,t);if(h<<24>>24)if((Ma|0)!=0?(c[Na>>2]|0)>>>0>Ma>>>0:0)x=106;else{k=1;Pa=c[Na>>2]|0;Pa=k?Pa:-1;i=Oa;return Pa|0}else x=107}while(0);if((x|0)==106){c[Na>>2]=0;if(Ma>>>0>l>>>0){k=0;Pa=c[Na>>2]|0;Pa=k?Pa:-1;i=Oa;return Pa|0}}else if((x|0)==107?(c[Na>>2]=0,(Ma|0)==0|Ma>>>0>l>>>0):0){k=0;Pa=c[Na>>2]|0;Pa=k?Pa:-1;i=Oa;return Pa|0}a[m>>0]=33;a[m+1>>0]=3;h=0;q=2;r=j;while(1){if(!r)break;if(r>>>0<=16777216)if(r>>>0>65536){f=r;if(f>>>0>1048576)x=113;else p=1}else{f=r;p=0}else{f=16777216;x=113}if((x|0)==113){x=0;p=2}o=p<<1|(f<<3)+-8|1<<(p<<2|19);a[m+q>>0]=o;a[m+(q+1)>>0]=o>>>8;n=q+3|0;a[m+(q+2)>>0]=o>>>16;if((p|0)==2){a[m+n>>0]=o>>>24;n=q+4|0}dc(m+n|0,k+h|0,f|0)|0;h=h+f|0;q=n+f|0;r=r-f|0}a[m+q>>0]=3;c[Na>>2]=q+1;k=1;Pa=c[Na>>2]|0;Pa=k?Pa:-1;i=Oa;return Pa|0}function Da(a,d,e,f,g,h,i,j){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;x=g+8|0;y=g+12|0;z=g+4|0;k=c[f+12>>2]|0;l=0;B=0;while(1){if((k|0)==-1)break;n=l+k|0;o=f+(n<<4)|0;p=c[o>>2]|0;A=p&16777215;m=c[f+(n<<4)+8>>2]|0;w=l+m|0;k=c[f+(n<<4)+12>>2]|0;if(!B){m=m+(c[h>>2]|0)|0;c[h>>2]=0}u=c[f+(n<<4)+4>>2]|0;v=u&33554431;o=c[o>>2]|0;o=(o&16777215)+9-(o>>>24)|0;t=w+d|0;t=v>>>0>(t>>>0<e>>>0?t:e)>>>0;u=u>>>25;u=(u|0)==0?v+15|0:u+-1|0;c[i+(B<<4)>>2]=m;c[i+(B<<4)+4>>2]=A|(o^p)<<24;l=i+(B<<4)+14|0;if(u>>>0<16){n=u&65535;b[l>>1]=n;l=n;n=0}else{r=u+-12|0;n=((aa(r|0)|0)^31)+-1|0;s=r>>>n&1;q=((n<<1)+65534|s)+16&65535;b[l>>1]=q;l=q;n=n<<24|r-((s|2)<<n)}c[i+(B<<4)+8>>2]=n;r=l<<16>>16==0;s=i+(B<<4)+12|0;do if(m>>>0>=6){if(m>>>0<130){p=m+-2|0;q=((aa(p|0)|0)^31)+-1|0;q=(q<<1)+(p>>>q)+2&65535;break}if(m>>>0<2114){q=((aa(m+-66|0)|0)^31)+10&65535;break}if(m>>>0<6210)q=21;else q=m>>>0<22594?22:23}else q=m&65535;while(0);do if(o>>>0>=10){if(o>>>0<134){p=o+-6|0;l=((aa(p|0)|0)^31)+-1|0;l=(l<<1)+(p>>>l)+4&65535;break}if(o>>>0<2118)l=((aa(o+-70|0)|0)^31)+12&65535;else l=23}else l=o+65534&65535;while(0);n=l&65535;o=q&65535;p=n&7|o<<3&56;if(r&(q&65535)<8&(l&65535)<16)l=((l&65535)<8?p:p|64)&65535;else l=b[88156+((n>>>3)+((o>>>3)*3|0)<<1)>>1]|p&65535;b[s>>1]=l;if(!(t|(u|0)==0)){c[y>>2]=c[x>>2];c[x>>2]=c[z>>2];c[z>>2]=c[g>>2];c[g>>2]=v}c[j>>2]=(c[j>>2]|0)+m;l=w+A|0;B=B+1|0}c[h>>2]=(c[h>>2]|0)+(a-l);return}function Ea(b,e,f,h,j,k,l,m,n,o){b=b|0;e=e|0;f=f|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;var p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,aa=0,ba=0,ca=0;ba=i;i=i+6320|0;Y=ba+6160|0;$=ba+1252|0;aa=ba+1024|0;Z=ba;W=(c[k+4>>2]|0)<11?150:325;X=e>>>0>127?f+e+-127|0:f;c[o>>2]=0;g[o+12>>2]=0.0;c[$+4904>>2]=e;U=b+8|0;p=sa[c[b>>2]&1](c[U>>2]|0,(e<<2)+8|0)|0;if(!p)oa(1);V=$+4896|0;c[V>>2]=p;Fa($,f,h,j);N=aa+224|0;c[N>>2]=0;O=Z;P=k+4|0;Q=n+524292|0;R=n+524296|0;S=Z;T=Z+4|0;p=0;a:while(1){if((p+3|0)>>>0>=e>>>0){w=98;break}M=p+f|0;L=M>>>0<l>>>0?M:l;J=e-p|0;I=M&j;A=(c[P>>2]|0)!=11?16:64;A=M>>>0<A>>>0?0:M-A|0;K=h+I|0;x=h+(I+1)|0;z=I+J|0;y=h+z|0;z=h+(z+-4)|0;q=O;s=1;r=M;while(1){r=r+-1|0;if(!(r>>>0>A>>>0&s>>>0<3))break;v=M-r|0;if(v>>>0>L>>>0)break;w=r&j;if((a[K>>0]|0)!=(a[h+w>>0]|0)){G=s;H=q;s=G;q=H;continue}if((a[x>>0]|0)==(a[h+(w+1)>>0]|0)){t=0;u=K}else{G=s;H=q;s=G;q=H;continue}while(1){if(u>>>0>z>>>0)break;H=h+(w+t)|0;if((d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24|0)!=(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24|0))break;t=t+4|0;u=u+4|0}while(1){if(u>>>0>=y>>>0)break;if((a[h+(w+t)>>0]|0)!=(a[u>>0]|0))break;t=t+1|0;u=u+1|0}if(t>>>0<=s>>>0){G=s;H=q;s=G;q=H;continue}s=q;c[s>>2]=v;c[s+4>>2]=t<<5;q=s+8|0;s=t}do if(s>>>0<J>>>0){G=J>>>0<128?J:128;H=J>>>0>127;r=n+4+((_(d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24,506832829)|0)>>>15<<2)|0;v=c[r>>2]|0;t=(M&c[n>>2])<<1;u=t|1;if(H){c[r>>2]=M;E=0;F=0;D=64}else{E=0;F=0;D=64}while(1){B=M-v|0;C=v&j;if((M|0)==(v|0)|B>>>0>L>>>0|(D|0)==0){w=22;break}A=E>>>0<F>>>0?E:F;if(A>>>0>=129){w=25;break a}y=I+A|0;w=C+J|0;z=h+w|0;w=h+(w+-4)|0;x=0;r=h+(C+A)|0;while(1){if(r>>>0>w>>>0)break;ca=h+(y+x)|0;if((d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24|0)!=(d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0))break;x=x+4|0;r=r+4|0}while(1){if(r>>>0>=z>>>0)break;if((a[h+(y+x)>>0]|0)!=(a[r>>0]|0))break;x=x+1|0;r=r+1|0}A=A+x|0;b:do if(A){r=A;y=K;z=h+C|0;while(1){w=a[y>>0]|0;x=a[z>>0]|0;if(w<<24>>24!=x<<24>>24)break;r=r+-1|0;if(!r)break b;else{y=y+1|0;z=z+1|0}}if(w<<24>>24!=x<<24>>24){w=38;break a}}while(0);r=q;if((q|0)!=0&A>>>0>s>>>0){c[r>>2]=B;c[r+4>>2]=A<<5;q=r+8|0;s=A}if(A>>>0>=G>>>0){w=42;break}if((d[h+(I+A)>>0]|0)>(d[h+(C+A)>>0]|0)){if(H)c[(c[Q>>2]|0)+(t<<2)>>2]=v;w=(v&c[n>>2])<<1|1;v=A;r=F;t=w;w=(c[Q>>2]|0)+(w<<2)|0}else{if(H)c[(c[Q>>2]|0)+(u<<2)>>2]=v;w=(v&c[n>>2])<<1;v=E;r=A;u=w;w=(c[Q>>2]|0)+(w<<2)|0}E=v;F=r;v=c[w>>2]|0;D=D+-1|0}if((w|0)==22){if(!H)break;c[(c[Q>>2]|0)+(t<<2)>>2]=c[R>>2];c[(c[Q>>2]|0)+(u<<2)>>2]=c[R>>2];break}else if((w|0)==42){if(!H)break;ca=c[Q>>2]|0;c[ca+(t<<2)>>2]=c[ca+((v&c[n>>2])<<1<<2)>>2];ca=c[Q>>2]|0;c[ca+(u<<2)>>2]=c[ca+(((v&c[n>>2])<<1|1)<<2)>>2];break}}while(0);r=0;while(1){if((r|0)==38)break;c[Y+(r<<2)>>2]=268435455;r=r+1|0}s=s+1|0;s=s>>>0<4?4:s;c:do if(Vb(K,s,J,Y)|0){t=J>>>0>37?37:J;while(1){if(s>>>0>t>>>0)break c;r=c[Y+(s<<2)>>2]|0;if(r>>>0<268435455){ca=r&31;c[q>>2]=L+(r>>>5)+1;c[q+4>>2]=s<<5|((s|0)==(ca|0)?0:ca);q=q+8|0}s=s+1|0}}while(0);q=q-S>>3;do if(q){r=q+-1|0;if((c[Z+(r<<3)+4>>2]|0)>>>5>>>0<=W>>>0){ca=q;Ga(e,f,p,h,j,k,l,m,ca,Z,$,aa,o);if((ca|0)!=1)break}else{K=Z+(r<<3)|0;L=c[K+4>>2]|0;ca=Z;c[ca>>2]=c[K>>2];c[ca+4>>2]=L;Ga(e,f,p,h,j,k,l,m,1,Z,$,aa,o)}q=(c[T>>2]|0)>>>5;if(q>>>0>W>>>0){I=M+q|0;I=I>>>0<X>>>0?I:X;H=(M+64|0)>>>0>I>>>0?M+1|0:I+-63|0;while(1){if(H>>>0>=I>>>0)break;s=c[n>>2]|0;D=s+-15|0;E=H&j;F=h+E|0;B=n+4+((_(d[F>>0]|d[F+1>>0]<<8|d[F+2>>0]<<16|d[F+3>>0]<<24,506832829)|0)>>>15<<2)|0;A=c[B>>2]|0;s=(H&s)<<1;c[B>>2]=H;B=0;t=0;C=0;r=s;s=s|1;G=64;while(1){z=A&j;if((H|0)==(A|0)|(H-A|0)>>>0>D>>>0|(G|0)==0){w=71;break}y=t>>>0<C>>>0?t:C;if(y>>>0>=129){w=73;break a}w=E+y|0;x=h+(z+128)|0;u=h+(z+124)|0;v=0;q=h+(z+y)|0;while(1){if(q>>>0>u>>>0)break;ca=h+(w+v)|0;if((d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24|0)!=(d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0))break;v=v+4|0;q=q+4|0}while(1){if(q>>>0>=x>>>0)break;if((a[h+(w+v)>>0]|0)!=(a[q>>0]|0))break;v=v+1|0;q=q+1|0}y=y+v|0;d:do if(y){q=y;w=F;x=h+z|0;while(1){u=a[w>>0]|0;v=a[x>>0]|0;if(u<<24>>24!=v<<24>>24)break;q=q+-1|0;if(!q)break d;else{w=w+1|0;x=x+1|0}}if(u<<24>>24!=v<<24>>24){w=86;break a}}while(0);if(B){w=88;break a}if(y>>>0>=128){w=90;break}q=c[Q>>2]|0;if((d[h+(E+y)>>0]|0)>(d[h+(z+y)>>0]|0)){c[q+(r<<2)>>2]=A;u=(A&c[n>>2])<<1|1;t=y;q=C;r=u;u=(c[Q>>2]|0)+(u<<2)|0}else{c[q+(s<<2)>>2]=A;u=(A&c[n>>2])<<1;q=y;s=u;u=(c[Q>>2]|0)+(u<<2)|0}B=0;C=q;A=c[u>>2]|0;G=G+-1|0}if((w|0)==71){c[(c[Q>>2]|0)+(r<<2)>>2]=c[R>>2];c[(c[Q>>2]|0)+(s<<2)>>2]=c[R>>2]}else if((w|0)==90){ca=c[Q>>2]|0;c[ca+(r<<2)>>2]=c[ca+((A&c[n>>2])<<1<<2)>>2];ca=c[Q>>2]|0;c[ca+(s<<2)>>2]=c[ca+(((A&c[n>>2])<<1|1)<<2)>>2]}H=H+1|0}p=p+(((c[T>>2]|0)>>>5)+-1)|0;c[N>>2]=0}}else Ga(e,f,p,h,j,k,l,m,0,Z,$,aa,o);while(0);p=p+1|0}if((w|0)==25)pa(405873,405905,373,405932);else if((w|0)==38)pa(405955,405905,378,405932);else if((w|0)==73)pa(405873,405905,373,405932);else if((w|0)==86)pa(405955,405905,378,405932);else if((w|0)==88)na();else if((w|0)==98){ra[c[b+4>>2]&1](c[U>>2]|0,c[V>>2]|0);c[V>>2]=0;while(1){if(c[o+(e<<4)+8>>2]|0)break;if((c[o+(e<<4)>>2]|0)!=1)break;e=e+-1|0}c[o+(e<<4)+12>>2]=-1;p=0;while(1){if(!e)break;j=(c[o+(e<<4)>>2]&16777215)+(c[o+(e<<4)+8>>2]|0)|0;ca=e-j|0;c[o+(ca<<4)+12>>2]=j;e=ca;p=p+1|0}i=ba;return p|0}return 0}function Fa(b,e,f,h){b=b|0;e=e|0;f=f|0;h=h|0;var j=0,k=0.0,l=0,m=0.0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;w=i;i=i+4128|0;p=w+4112|0;s=w+1040|0;t=w+1024|0;o=w;u=c[b+4904>>2]|0;v=c[b+4896>>2]|0;a:do if(Wb(f,e,h,u)|0){c[p>>2]=0;c[p+4>>2]=0;c[p+8>>2]=0;l=0;o=0;while(1){if((o|0)==(u|0))break;j=a[f+(o+e&h)>>0]|0;n=j&255;if(j<<24>>24<=-1)if((j&255)>191)j=1;else j=l>>>0<224?0:2;else j=0;l=p+(j<<2)|0;c[l>>2]=(c[l>>2]|0)+1;l=n;o=o+1|0}r=((c[p+4>>2]|0)+(c[p+8>>2]|0)|0)>>>0<25?0:1;ac(s|0,0,3072)|0;n=u>>>0>495?495:u;c[t>>2]=0;c[t+4>>2]=0;c[t+8>>2]=0;p=0;q=0;j=0;while(1){if((q|0)==(n|0)){o=0;break}l=a[f+(q+e&h)>>0]|0;o=l&255;x=s+(j<<10)+(o<<2)|0;c[x>>2]=(c[x>>2]|0)+1;j=t+(j<<2)|0;c[j>>2]=(c[j>>2]|0)+1;if(l<<24>>24<=-1)if((l&255)>191)j=r;else j=p>>>0<224?0:r;else j=0;p=o;q=q+1|0}while(1){if((o|0)==(u|0))break;if(o>>>0>=495){if(o>>>0>=496){j=o+e|0;l=d[f+(j+-496&h)>>0]|0;if(o>>>0<497)j=0;else j=d[f+(j+-497&h)>>0]|0;if(l>>>0>=128)if(l>>>0>191)j=r;else j=j>>>0<224?0:r;else j=0}else j=0;x=s+(j<<10)+((d[f+(o+e+-495&h)>>0]|0)<<2)|0;c[x>>2]=(c[x>>2]|0)+-1;x=t+(j<<2)|0;c[x>>2]=(c[x>>2]|0)+-1}if((o+495|0)>>>0<u>>>0){l=o+e|0;n=l+495|0;j=a[f+(l+494&h)>>0]|0;l=a[f+(l+493&h)>>0]|0;if(j<<24>>24<=-1)if((j&255)>191)j=r;else j=(l&255)<224?0:r;else j=0;x=s+(j<<10)+((d[f+(n&h)>>0]|0)<<2)|0;c[x>>2]=(c[x>>2]|0)+1;x=t+(j<<2)|0;c[x>>2]=(c[x>>2]|0)+1}if(o){j=o+e|0;l=d[f+(j+-1&h)>>0]|0;if(o>>>0<2)j=0;else j=d[f+(j+-2&h)>>0]|0;if(l>>>0>=128)if(l>>>0>191)j=r;else j=j>>>0<224?0:r;else j=0}else j=0;l=c[s+(j<<10)+((d[f+(o+e&h)>>0]|0)<<2)>>2]|0;l=(l|0)==0?1:l;j=c[t+(j<<2)>>2]|0;if(j>>>0<256)m=+g[19516+(j<<2)>>2];else m=+Xb(+(j>>>0));if(l>>>0<256)k=+g[19516+(l<<2)>>2];else k=+Xb(+(l>>>0));k=m-k+.02905;k=k<1.0?k*.5+.5:k;if(o>>>0<2e3)k=k+(.7-+((2e3-o|0)>>>0)/2.0e3*.35);g[v+(o+1<<2)>>2]=k;o=o+1|0}}else{ac(o|0,0,1024)|0;j=u>>>0>2e3?2e3:u;l=0;while(1){if((l|0)==(j|0)){n=0;break}x=o+((d[f+(l+e&h)>>0]|0)<<2)|0;c[x>>2]=(c[x>>2]|0)+1;l=l+1|0}while(1){if((n|0)==(u|0))break a;if(n>>>0>=2e3){x=o+((d[f+(n+e+-2e3&h)>>0]|0)<<2)|0;c[x>>2]=(c[x>>2]|0)+-1;j=j+-1|0}l=n+e|0;if((n+2e3|0)>>>0<u>>>0){x=o+((d[f+(l+2e3&h)>>0]|0)<<2)|0;c[x>>2]=(c[x>>2]|0)+1;j=j+1|0}l=c[o+((d[f+(l&h)>>0]|0)<<2)>>2]|0;l=(l|0)==0?1:l;if(j>>>0<256)m=+g[19516+(j<<2)>>2];else m=+Xb(+(j>>>0));if(l>>>0<256)k=+g[19516+(l<<2)>>2];else k=+Xb(+(l>>>0));m=m-k+.029;g[v+(n+1<<2)>>2]=m<1.0?m*.5+.5:m;n=n+1|0}}while(0);g[v>>2]=0.0;k=0.0;j=0;while(1){if((j|0)==(u|0)){l=0;break}x=j+1|0;h=v+(x<<2)|0;m=+g[h>>2]+k;g[h>>2]=m;k=m;j=x}while(1){if((l|0)==704){l=0;break}j=l+11|0;if(j>>>0<256)k=+g[19516+(j<<2)>>2];else k=+Xb(+(j>>>0));g[b+(l<<2)>>2]=k;l=l+1|0}while(1){if((l|0)==520)break;j=l+20|0;if(j>>>0<256)k=+g[19516+(j<<2)>>2];else k=+Xb(+(j>>>0));g[b+2816+(l<<2)>>2]=k;l=l+1|0}g[b+4900>>2]=3.4594316482543945;i=w;return}function Ga(e,f,h,j,l,m,n,o,p,q,r,s,t){e=e|0;f=f|0;h=h|0;j=j|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;t=t|0;var u=0.0,v=0,w=0,x=0.0,y=0,z=0,A=0,B=0,C=0,D=0,E=0.0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0;Z=i;i=i+64|0;A=Z+36|0;z=Z+8|0;Y=Z;S=f+h|0;T=S&l;R=S>>>0<n>>>0?S:n;U=e-h|0;Q=(c[m+4>>2]|0)<11;P=Q?150:325;Q=Q?1:5;y=c[t+(h<<4)+12>>2]|0;f=c[t+(h<<4)>>2]&16777215;m=c[t+(h<<4)+8>>2]|0;v=c[t+(h<<4)+4>>2]|0;w=v&33554431;if(h)if((w+f|0)>>>0>S>>>0|w>>>0>n>>>0|(v>>>25|0)==1)f=c[t+(h-f-m<<4)+12>>2]|0;else f=h;else f=0;v=t+(h<<4)+12|0;c[v>>2]=f;x=(c[k>>2]=y,+g[k>>2]);O=r+4896|0;m=c[O>>2]|0;u=+g[m+(h<<2)>>2];if(!(x<=u-+g[m>>2]))y=s+224|0;else{c[z>>2]=h;c[z+24>>2]=y;f=c[O>>2]|0;g[z+20>>2]=x-(+g[f+(h<<2)>>2]-+g[f>>2]);f=0;m=v;while(1){m=c[m>>2]|0;if(!((f|0)<4&(m|0)!=0)){m=o;break}N=c[t+(m<<4)+8>>2]|0;M=c[t+(m<<4)>>2]&16777215;c[z+4+(f<<2)>>2]=c[t+(m<<4)+4>>2]&33554431;f=f+1|0;m=t+(m-M-N<<4)+12|0}while(1){if((f|0)>=4)break;c[z+4+(f<<2)>>2]=c[m>>2];m=m+4|0;f=f+1|0}y=s+224|0;f=c[y>>2]|0;n=f+1|0;c[y>>2]=n;f=f&7^7;n=n>>>0<8?n:8;w=s+(f*28|0)|0;c[w>>2]=c[z>>2];c[w+4>>2]=c[z+4>>2];c[w+8>>2]=c[z+8>>2];c[w+12>>2]=c[z+12>>2];c[w+16>>2]=c[z+16>>2];c[w+20>>2]=c[z+20>>2];c[w+24>>2]=c[z+24>>2];w=1;while(1){if(w>>>0>=n>>>0)break;v=f&7;f=f+1|0;m=f&7;if(+g[s+(v*28|0)+20>>2]>+g[s+(m*28|0)+20>>2]){M=s+(v*28|0)|0;c[A>>2]=c[M>>2];c[A+4>>2]=c[M+4>>2];c[A+8>>2]=c[M+8>>2];c[A+12>>2]=c[M+12>>2];c[A+16>>2]=c[M+16>>2];c[A+20>>2]=c[M+20>>2];c[A+24>>2]=c[M+24>>2];N=s+(m*28|0)|0;c[M>>2]=c[N>>2];c[M+4>>2]=c[N+4>>2];c[M+8>>2]=c[N+8>>2];c[M+12>>2]=c[N+12>>2];c[M+16>>2]=c[N+16>>2];c[M+20>>2]=c[N+20>>2];c[M+24>>2]=c[N+24>>2];c[N>>2]=c[A>>2];c[N+4>>2]=c[A+4>>2];c[N+8>>2]=c[A+8>>2];c[N+12>>2]=c[A+12>>2];c[N+16>>2]=c[A+16>>2];c[N+20>>2]=c[A+20>>2];c[N+24>>2]=c[A+24>>2]}w=w+1|0}N=c[O>>2]|0;m=N;u=+g[N+(h<<2)>>2]}v=0-(c[y>>2]|0)&7;f=2;u=+g[s+(v*28|0)+24>>2]+ +g[r+4900>>2]+(u-+g[m+(c[s+(v*28|0)>>2]<<2)>>2]);v=4;w=10;a:while(1){do{m=f+h|0;if(m>>>0>e>>>0)break a;if(!(+g[t+(m<<4)+12>>2]<=u))break a;f=f+1|0}while((f|0)!=(w|0));N=w+v|0;f=w;u=u+1.0;v=v<<1;w=N}H=f+-1|0;I=j+T|0;K=T+U|0;J=j+K|0;K=j+(K+-4)|0;L=Y+4|0;M=Y+4|0;N=0;while(1){if(N>>>0>=Q>>>0){f=82;break}m=c[y>>2]|0;if(N>>>0>=(m>>>0<8?m:8)>>>0){f=82;break}C=N-m&7;D=h-(c[s+(C*28|0)>>2]|0)|0;do if(D>>>0>=6){if(D>>>0<130){G=D+-2|0;m=((aa(G|0)|0)^31)+-1|0;m=(m<<1)+(G>>>m)+2&65535;break}if(D>>>0<2114){m=((aa(D+-66|0)|0)^31)+10&65535;break}if(D>>>0<6210)m=21;else m=D>>>0<22594?22:23}else m=D&65535;while(0);G=m&65535;F=c[O>>2]|0;E=+g[s+(C*28|0)+20>>2]+ +((c[17268+(G<<2)>>2]|0)>>>0)+(+g[F+(h<<2)>>2]-+g[F>>2]);F=G<<3&56;G=(G>>>3)*3|0;e=(m&65535)<8;m=H;B=0;while(1){if(!(B>>>0<16&m>>>0<U>>>0))break;n=(c[s+(C*28|0)+4+(c[11372+(B<<2)>>2]<<2)>>2]|0)+(c[11436+(B<<2)>>2]|0)|0;v=S-n|0;b:do if(((!(v>>>0>=S>>>0|n>>>0>R>>>0)?(V=v&l,W=T+m|0,W>>>0<=l>>>0):0)?(X=V+m|0,X>>>0<=l>>>0):0)?(a[j+W>>0]|0)==(a[j+X>>0]|0):0){v=0;w=I;while(1){if(w>>>0>K>>>0)break;A=j+(V+v)|0;if((d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24|0)!=(d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0))break;v=v+4|0;w=w+4|0}while(1){if(w>>>0>=J>>>0)break;if((a[j+(V+v)>>0]|0)!=(a[w>>0]|0))break;v=v+1|0;w=w+1|0}x=E+ +g[r+2816+(B<<2)>>2];A=(B|0)==0;z=n|(B<<25)+33554432;while(1){o=m+1|0;if(o>>>0>v>>>0)break b;do if(o>>>0>=10){if(o>>>0<134){n=m+-5|0;m=((aa(n|0)|0)^31)+-1|0;m=(m<<1)+(n>>>m)+4&65535;break}if(o>>>0<2118)m=((aa(m+-69|0)|0)^31)+12&65535;else m=23}else m=m+65535&65535;while(0);n=m&65535;w=n&7|F;if(((A^1|e^1)^1)&(m&65535)<16)m=((m&65535)<8?w:w|64)&65535;else m=b[88156+((n>>>3)+G<<1)>>1]|w&65535;u=((m&65535)<128?E:x)+ +((c[17460+(n<<2)>>2]|0)>>>0)+ +g[r+((m&65535)<<2)>>2];m=o+h|0;if(!(u<+g[t+(m<<4)+12>>2])){m=o;continue}c[t+(m<<4)>>2]=o|150994944;c[t+(m<<4)+4>>2]=z;c[t+(m<<4)+8>>2]=D;g[t+(m<<4)+12>>2]=u;m=o}}while(0);B=B+1|0}c:do if(N>>>0<=1){o=f;C=0;while(1){if((C|0)==(p|0))break c;z=q+(C<<3)|0;e=c[z>>2]|0;z=c[z+4>>2]|0;n=Y;c[n>>2]=e;c[n+4>>2]=z;n=e>>>0>R>>>0;m=n&1;v=e+15|0;B=bc(e|0,z|0,37)|0;if(v>>>0<16)w=0;else{_=e+3|0;w=((aa(_|0)|0)^31)+-1|0;A=_>>>w&1;v=((w<<1)+65534|A)+16|0;w=w<<24|_-((A|2)<<w)}x=E+ +(w>>>24>>>0)+ +g[r+2816+((v&65535)<<2)>>2];if(o>>>0<B>>>0){_=n|B>>>0>P>>>0;m=_?m:0;o=_?B:o}A=m<<24>>24==0;w=bc(e|0,z|0,37)|0;w=w+1|0;n=o>>>0>w>>>0;z=o;while(1){if(z>>>0>B>>>0)break;if(!A){m=c[L>>2]&31;if(!m)m=(c[M>>2]|0)>>>5}else m=z;do if(m>>>0>=10){if(m>>>0<134){_=m+-6|0;v=((aa(_|0)|0)^31)+-1|0;v=(v<<1)+(_>>>v)+4|0;break}if(m>>>0<2118)v=((aa(m+-70|0)|0)^31)+12|0;else v=23}else v=m+65534|0;while(0);_=v&65535;u=x+ +((c[17460+(_<<2)>>2]|0)>>>0)+ +g[r+(((b[88156+((_>>>3)+G<<1)>>1]|(v&7|F)&65535)&65535)<<2)>>2];v=z+h|0;if(u<+g[t+(v<<4)+12>>2]){c[t+(v<<4)>>2]=z|z+9-m<<24;c[t+(v<<4)+4>>2]=e;c[t+(v<<4)+8>>2]=D;g[t+(v<<4)+12>>2]=u}z=z+1|0}o=n?o:w;C=C+1|0}}while(0);N=N+1|0}if((f|0)==82){i=Z;return}}function Ha(b,e,f,h,i,j,k,l,m,n,o,p,q){b=b|0;e=e|0;f=f|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;var r=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0;M=(1<<c[k+8>>2])+-16|0;Ua(b,l,k,f,e,h);a:do if(e>>>0>2&f>>>0>127){L=f+-127|0;K=L+e|0;K=K>>>0>f>>>0?f:K;J=l+524292|0;I=l+524296|0;b:while(1){if(L>>>0>=K>>>0)break a;t=c[l>>2]|0;E=f-L|0;E=t-(E>>>0<15?15:E)|0;F=L&j;G=i+F|0;C=l+4+((_(d[G>>0]|d[G+1>>0]<<8|d[G+2>>0]<<16|d[G+3>>0]<<24,506832829)|0)>>>15<<2)|0;B=c[C>>2]|0;t=(L&t)<<1;c[C>>2]=L;C=0;u=0;D=0;r=t;t=t|1;H=64;while(1){A=B&j;if((L|0)==(B|0)|(L-B|0)>>>0>E>>>0|(H|0)==0){h=6;break}z=u>>>0<D>>>0?u:D;if(z>>>0>=129){h=8;break b}x=F+z|0;y=i+(A+128)|0;v=i+(A+124)|0;w=0;h=i+(A+z)|0;while(1){if(h>>>0>v>>>0)break;N=i+(x+w)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[N>>0]|d[N+1>>0]<<8|d[N+2>>0]<<16|d[N+3>>0]<<24|0))break;w=w+4|0;h=h+4|0}while(1){if(h>>>0>=y>>>0)break;if((a[i+(x+w)>>0]|0)!=(a[h>>0]|0))break;w=w+1|0;h=h+1|0}z=z+w|0;c:do if(z){h=z;x=G;y=i+A|0;while(1){v=a[x>>0]|0;w=a[y>>0]|0;if(v<<24>>24!=w<<24>>24)break;h=h+-1|0;if(!h)break c;else{x=x+1|0;y=y+1|0}}if(v<<24>>24!=w<<24>>24){h=21;break b}}while(0);if(C){h=23;break b}if(z>>>0>=128){h=25;break}h=c[J>>2]|0;if((d[i+(F+z)>>0]|0)>(d[i+(A+z)>>0]|0)){c[h+(r<<2)>>2]=B;v=(B&c[l>>2])<<1|1;u=z;h=D;r=v;v=(c[J>>2]|0)+(v<<2)|0}else{c[h+(t<<2)>>2]=B;v=(B&c[l>>2])<<1;h=z;t=v;v=(c[J>>2]|0)+(v<<2)|0}C=0;D=h;B=c[v>>2]|0;H=H+-1|0}if((h|0)==6){c[(c[J>>2]|0)+(r<<2)>>2]=c[I>>2];c[(c[J>>2]|0)+(t<<2)>>2]=c[I>>2]}else if((h|0)==25){N=c[J>>2]|0;c[N+(r<<2)>>2]=c[N+((B&c[l>>2])<<1<<2)>>2];N=c[J>>2]|0;c[N+(t<<2)>>2]=c[N+(((B&c[l>>2])<<1|1)<<2)>>2]}L=L+1|0}if((h|0)==8)pa(405873,405905,373,405932);else if((h|0)==21)pa(405955,405905,378,405932);else if((h|0)==23)na()}while(0);h=b+8|0;r=sa[c[b>>2]&1](c[h>>2]|0,(e<<4)+16|0)|0;if(!r)oa(1);t=e+1|0;u=0;while(1){if((u|0)==(t|0))break;c[r+(u<<4)>>2]=1;c[r+(u<<4)+4>>2]=0;c[r+(u<<4)+8>>2]=0;g[r+(u<<4)+12>>2]=s;u=u+1|0}N=Ea(b,e,f,i,j,k,M,m,l,r)|0;c[p>>2]=(c[p>>2]|0)+N;Da(e,f,M,r,m,n,o,q);ra[c[b+4>>2]&1](c[h>>2]|0,r);return}function Ia(e,f,h,j,l,m,n,o,p,q,r,t,u){e=e|0;f=f|0;h=h|0;j=j|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;t=t|0;u=u|0;var v=0,w=0,x=0,y=0.0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,qa=0,ta=0,ua=0,va=0,wa=0,xa=0.0;va=i;i=i+12256|0;fa=va+12028|0;ea=va+12024|0;qa=va+11e3|0;la=va+8184|0;ma=va+6104|0;ka=va+5080|0;aa=va+4928|0;ua=va+4912|0;ta=va;ga=(1<<c[n+8>>2])+-16|0;da=f<<2;ia=e+8|0;ja=sa[c[e>>2]&1](c[ia>>2]|0,da)|0;if(!ja)oa(1);$=f>>>0>127?h+f+-127|0:h;ca=sa[c[e>>2]&1](c[ia>>2]|0,f<<5)|0;Q=ca;if(!ca)oa(1);Ua(e,o,n,h,f,j);a:do if(f>>>0>2&h>>>0>127){P=h+-127|0;O=P+f|0;O=O>>>0>h>>>0?h:O;M=o+524292|0;N=o+524296|0;b:while(1){if(P>>>0>=O>>>0)break a;w=c[o>>2]|0;I=h-P|0;I=w-(I>>>0<15?15:I)|0;J=P&m;K=l+J|0;G=o+4+((_(d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24,506832829)|0)>>>15<<2)|0;F=c[G>>2]|0;w=(P&w)<<1;c[G>>2]=P;G=0;x=0;H=0;v=w;w=w|1;L=64;while(1){E=F&m;if((P|0)==(F|0)|(P-F|0)>>>0>I>>>0|(L|0)==0){x=11;break}D=x>>>0<H>>>0?x:H;if(D>>>0>=129){x=13;break b}B=J+D|0;C=l+(E+128)|0;z=l+(E+124)|0;A=0;j=l+(E+D)|0;while(1){if(j>>>0>z>>>0)break;ca=l+(B+A)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0))break;A=A+4|0;j=j+4|0}while(1){if(j>>>0>=C>>>0)break;if((a[l+(B+A)>>0]|0)!=(a[j>>0]|0))break;A=A+1|0;j=j+1|0}D=D+A|0;c:do if(D){j=D;B=K;C=l+E|0;while(1){z=a[B>>0]|0;A=a[C>>0]|0;if(z<<24>>24!=A<<24>>24)break;j=j+-1|0;if(!j)break c;else{B=B+1|0;C=C+1|0}}if(z<<24>>24!=A<<24>>24){x=26;break b}}while(0);if(G){x=28;break b}if(D>>>0>=128){x=30;break}j=c[M>>2]|0;if((d[l+(J+D)>>0]|0)>(d[l+(E+D)>>0]|0)){c[j+(v<<2)>>2]=F;z=(F&c[o>>2])<<1|1;x=D;j=H;v=z;z=(c[M>>2]|0)+(z<<2)|0}else{c[j+(w<<2)>>2]=F;z=(F&c[o>>2])<<1;j=D;w=z;z=(c[M>>2]|0)+(z<<2)|0}G=0;H=j;F=c[z>>2]|0;L=L+-1|0}if((x|0)==11){c[(c[M>>2]|0)+(v<<2)>>2]=c[N>>2];c[(c[M>>2]|0)+(w<<2)>>2]=c[N>>2]}else if((x|0)==30){ca=c[M>>2]|0;c[ca+(v<<2)>>2]=c[ca+((F&c[o>>2])<<1<<2)>>2];ca=c[M>>2]|0;c[ca+(w<<2)>>2]=c[ca+(((F&c[o>>2])<<1|1)<<2)>>2]}P=P+1|0}if((x|0)==13)pa(405873,405905,373,405932);else if((x|0)==26)pa(405955,405905,378,405932);else if((x|0)==28)na()}while(0);ba=e+4|0;ca=n+4|0;Y=o+524292|0;Z=o+524296|0;W=0;A=da;v=0;d:while(1){if((v+3|0)>>>0>=f>>>0){x=142;break}V=v+h|0;U=V>>>0<ga>>>0?V:ga;R=f-v|0;j=W+128|0;if(A>>>0<j>>>0){x=(A|0)==0;z=x?j:A;while(1){if(z>>>0>=j>>>0)break;z=z<<1}w=sa[c[e>>2]&1](c[ia>>2]|0,z<<3)|0;if(!w){x=42;break}j=Q;if(!x)dc(w|0,j|0,A<<3|0)|0;ra[c[ba>>2]&1](c[ia>>2]|0,j);X=z}else{w=Q;X=A}S=w;T=S+(W<<3)|0;P=V&m;E=(c[ca>>2]|0)!=11?16:64;E=V>>>0<E>>>0?0:V-E|0;Q=l+P|0;F=l+(P+1)|0;H=P+R|0;G=l+H|0;H=l+(H+-4)|0;j=T;z=1;x=V;while(1){x=x+-1|0;if(!(x>>>0>E>>>0&z>>>0<3))break;C=V-x|0;if(C>>>0>U>>>0)break;D=x&m;if((a[Q>>0]|0)!=(a[l+D>>0]|0)){N=z;O=j;z=N;j=O;continue}if((a[F>>0]|0)==(a[l+(D+1)>>0]|0)){A=0;B=Q}else{N=z;O=j;z=N;j=O;continue}while(1){if(B>>>0>H>>>0)break;O=l+(D+A)|0;if((d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24|0)!=(d[O>>0]|d[O+1>>0]<<8|d[O+2>>0]<<16|d[O+3>>0]<<24|0))break;A=A+4|0;B=B+4|0}while(1){if(B>>>0>=G>>>0)break;if((a[l+(D+A)>>0]|0)!=(a[B>>0]|0))break;A=A+1|0;B=B+1|0}if(A>>>0<=z>>>0){N=z;O=j;z=N;j=O;continue}z=j;c[z>>2]=C;c[z+4>>2]=A<<5;j=z+8|0;z=A}do if(z>>>0<R>>>0){N=R>>>0<128?R:128;O=R>>>0>127;x=o+4+((_(d[Q>>0]|d[Q+1>>0]<<8|d[Q+2>>0]<<16|d[Q+3>>0]<<24,506832829)|0)>>>15<<2)|0;C=c[x>>2]|0;A=(V&c[o>>2])<<1;B=A|1;if(O){c[x>>2]=V;L=0;M=0;K=64}else{L=0;M=0;K=64}while(1){I=V-C|0;J=C&m;if((V|0)==(C|0)|I>>>0>U>>>0|(K|0)==0){x=63;break}H=L>>>0<M>>>0?L:M;if(H>>>0>=129){x=66;break d}F=P+H|0;D=J+R|0;G=l+D|0;D=l+(D+-4)|0;E=0;x=l+(J+H)|0;while(1){if(x>>>0>D>>>0)break;wa=l+(F+E)|0;if((d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24|0)!=(d[wa>>0]|d[wa+1>>0]<<8|d[wa+2>>0]<<16|d[wa+3>>0]<<24|0))break;E=E+4|0;x=x+4|0}while(1){if(x>>>0>=G>>>0)break;if((a[l+(F+E)>>0]|0)!=(a[x>>0]|0))break;E=E+1|0;x=x+1|0}H=H+E|0;e:do if(H){x=H;F=Q;G=l+J|0;while(1){D=a[F>>0]|0;E=a[G>>0]|0;if(D<<24>>24!=E<<24>>24)break;x=x+-1|0;if(!x)break e;else{F=F+1|0;G=G+1|0}}if(D<<24>>24!=E<<24>>24){x=79;break d}}while(0);x=j;if((j|0)!=0&H>>>0>z>>>0){c[x>>2]=I;c[x+4>>2]=H<<5;j=x+8|0;z=H}if(H>>>0>=N>>>0){x=83;break}if((d[l+(P+H)>>0]|0)>(d[l+(J+H)>>0]|0)){if(O)c[(c[Y>>2]|0)+(A<<2)>>2]=C;D=(C&c[o>>2])<<1|1;C=H;x=M;A=D;D=(c[Y>>2]|0)+(D<<2)|0}else{if(O)c[(c[Y>>2]|0)+(B<<2)>>2]=C;D=(C&c[o>>2])<<1;C=L;x=H;B=D;D=(c[Y>>2]|0)+(D<<2)|0}L=C;M=x;C=c[D>>2]|0;K=K+-1|0}if((x|0)==63){if(!O)break;c[(c[Y>>2]|0)+(A<<2)>>2]=c[Z>>2];c[(c[Y>>2]|0)+(B<<2)>>2]=c[Z>>2];break}else if((x|0)==83){if(!O)break;wa=c[Y>>2]|0;c[wa+(A<<2)>>2]=c[wa+((C&c[o>>2])<<1<<2)>>2];wa=c[Y>>2]|0;c[wa+(B<<2)>>2]=c[wa+(((C&c[o>>2])<<1|1)<<2)>>2];break}}while(0);x=0;while(1){if((x|0)==38)break;c[aa+(x<<2)>>2]=268435455;x=x+1|0}z=z+1|0;z=z>>>0<4?4:z;f:do if(Vb(Q,z,R,aa)|0){A=R>>>0>37?37:R;while(1){if(z>>>0>A>>>0)break f;x=c[aa+(z<<2)>>2]|0;if(x>>>0<268435455){wa=x&31;c[j>>2]=U+(x>>>5)+1;c[j+4>>2]=z<<5|((z|0)==(wa|0)?0:wa);j=j+8|0}z=z+1|0}}while(0);A=j-T>>3;j=W+A|0;z=W;while(1){x=z;z=z+1|0;if(z>>>0>=j>>>0)break;if((c[S+(x<<3)+4>>2]|0)>>>5>>>0>=(c[S+(z<<3)+4>>2]|0)>>>5>>>0){x=105;break d}x=c[S+(x<<3)>>2]|0;if(x>>>0>U>>>0)continue;if(x>>>0>(c[S+(z<<3)>>2]|0)>>>0){x=108;break d}}B=ja+(v<<2)|0;c[B>>2]=A;if(A){x=j+-1|0;wa=c[S+(x<<3)+4>>2]|0;z=wa>>>5;if(wa>>>0>10431){R=z+-1|0;j=W+1|0;wa=S+(x<<3)|0;P=c[wa+4>>2]|0;Q=T;c[Q>>2]=c[wa>>2];c[Q+4>>2]=P;c[B>>2]=1;Q=V+z|0;Q=Q>>>0<$>>>0?Q:$;P=(V+64|0)>>>0>Q>>>0?V+1|0:Q+-63|0;while(1){if(P>>>0>=Q>>>0)break;A=c[o>>2]|0;L=A+-15|0;M=P&m;N=l+M|0;J=o+4+((_(d[N>>0]|d[N+1>>0]<<8|d[N+2>>0]<<16|d[N+3>>0]<<24,506832829)|0)>>>15<<2)|0;I=c[J>>2]|0;A=(P&A)<<1;c[J>>2]=P;J=0;B=0;K=0;z=A;A=A|1;O=64;while(1){H=I&m;if((P|0)==(I|0)|(P-I|0)>>>0>L>>>0|(O|0)==0){x=115;break}G=B>>>0<K>>>0?B:K;if(G>>>0>=129){x=117;break d}E=M+G|0;F=l+(H+128)|0;C=l+(H+124)|0;D=0;x=l+(H+G)|0;while(1){if(x>>>0>C>>>0)break;wa=l+(E+D)|0;if((d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24|0)!=(d[wa>>0]|d[wa+1>>0]<<8|d[wa+2>>0]<<16|d[wa+3>>0]<<24|0))break;D=D+4|0;x=x+4|0}while(1){if(x>>>0>=F>>>0)break;if((a[l+(E+D)>>0]|0)!=(a[x>>0]|0))break;D=D+1|0;x=x+1|0}G=G+D|0;g:do if(G){x=G;E=N;F=l+H|0;while(1){C=a[E>>0]|0;D=a[F>>0]|0;if(C<<24>>24!=D<<24>>24)break;x=x+-1|0;if(!x)break g;else{E=E+1|0;F=F+1|0}}if(C<<24>>24!=D<<24>>24){x=130;break d}}while(0);if(J){x=132;break d}if(G>>>0>=128){x=134;break}x=c[Y>>2]|0;if((d[l+(M+G)>>0]|0)>(d[l+(H+G)>>0]|0)){c[x+(z<<2)>>2]=I;C=(I&c[o>>2])<<1|1;B=G;x=K;z=C;C=(c[Y>>2]|0)+(C<<2)|0}else{c[x+(A<<2)>>2]=I;C=(I&c[o>>2])<<1;x=G;A=C;C=(c[Y>>2]|0)+(C<<2)|0}J=0;K=x;I=c[C>>2]|0;O=O+-1|0}if((x|0)==115){c[(c[Y>>2]|0)+(z<<2)>>2]=c[Z>>2];c[(c[Y>>2]|0)+(A<<2)>>2]=c[Z>>2]}else if((x|0)==134){wa=c[Y>>2]|0;c[wa+(z<<2)>>2]=c[wa+((I&c[o>>2])<<1<<2)>>2];wa=c[Y>>2]|0;c[wa+(A<<2)>>2]=c[wa+(((I&c[o>>2])<<1|1)<<2)>>2]}P=P+1|0}ac(ja+(v+1<<2)|0,0,R<<2|0)|0;v=v+R|0}}else j=W;W=j;Q=w;A=X;v=v+1|0}if((x|0)==42)oa(1);else if((x|0)==66)pa(405873,405905,373,405932);else if((x|0)==79)pa(405955,405905,378,405932);else if((x|0)==105)pa(403595,403667,793,403707);else if((x|0)==108)pa(403740,403667,795,403707);else if((x|0)==117)pa(405873,405905,373,405932);else if((x|0)==130)pa(405955,405905,378,405932);else if((x|0)==132)na();else if((x|0)==142){K=c[u>>2]|0;L=c[q>>2]|0;c[ua>>2]=c[p>>2];c[ua+4>>2]=c[p+4>>2];c[ua+8>>2]=c[p+8>>2];c[ua+12>>2]=c[p+12>>2];M=c[t>>2]|0;N=sa[c[e>>2]&1](c[ia>>2]|0,(f<<4)+16|0)|0;if(!N)oa(1);O=ta+4904|0;c[O>>2]=f;j=sa[c[e>>2]&1](c[ia>>2]|0,da+8|0)|0;if(!j)oa(1);J=ta+4896|0;c[J>>2]=j;B=N+12|0;C=fa+224|0;D=Q;E=h-L|0;F=ta+2816|0;G=ta+4900|0;H=f+1|0;I=0;while(1){if((I|0)==2)break;else j=0;while(1){if((j|0)==(H|0))break;c[N+(j<<4)>>2]=1;c[N+(j<<4)+4>>2]=0;c[N+(j<<4)+8>>2]=0;g[N+(j<<4)+12>>2]=s;j=j+1|0}if(!I)Fa(ta,h,l,m);else{v=c[t>>2]|0;ac(qa|0,0,1024)|0;ac(la|0,0,2816)|0;ac(ma|0,0,2080)|0;v=v-M|0;z=E;A=0;while(1){if((A|0)==(v|0))break;w=c[r+(A<<4)>>2]|0;x=c[r+(A<<4)+4>>2]&16777215;j=b[r+(A<<4)+14>>1]|0;wa=b[r+(A<<4)+12>>1]|0;e=la+((wa&65535)<<2)|0;c[e>>2]=(c[e>>2]|0)+1;if((wa&65535)>127){j=ma+((j&65535)<<2)|0;c[j>>2]=(c[j>>2]|0)+1;j=0}else j=0;while(1){if((j|0)==(w|0))break;wa=qa+(d[l+(z+j&m)>>0]<<2)|0;c[wa>>2]=(c[wa>>2]|0)+1;j=j+1|0}z=z+(w+x)|0;A=A+1|0}Va(qa,256,ka);Va(la,704,ta);Va(ma,520,F);j=2139095040;v=0;while(1){if((v|0)==704)break;wa=c[ta+(v<<2)>>2]|0;c[fa>>2]=j;c[ea>>2]=wa;y=(c[k>>2]=j,+g[k>>2]);j=c[(y<(c[k>>2]=wa,+g[k>>2])?fa:ea)>>2]|0;v=v+1|0}c[G>>2]=j;v=c[O>>2]|0;j=c[J>>2]|0;g[j>>2]=0.0;y=0.0;w=0;while(1){if((w|0)==(v|0))break;xa=y+ +g[ka+(d[l+(w+h&m)>>0]<<2)>>2];wa=w+1|0;g[j+(wa<<2)>>2]=xa;y=xa;w=wa}}c[t>>2]=M;c[u>>2]=K;c[q>>2]=L;c[p>>2]=c[ua>>2];c[p+4>>2]=c[ua+4>>2];c[p+8>>2]=c[ua+8>>2];c[p+12>>2]=c[ua+12>>2];w=(c[ca>>2]|0)<11?150:325;c[N>>2]=0;g[B>>2]=0.0;c[C>>2]=0;j=0;v=0;while(1){if((v+3|0)>>>0>=f>>>0){j=f;break}wa=ja+(v<<2)|0;Ga(f,h,v,l,m,n,ga,p,c[wa>>2]|0,D+(j<<3)|0,ta,fa,N);wa=c[wa>>2]|0;j=j+wa|0;if((wa|0)==1?(ha=(c[D+(j+-1<<3)+4>>2]|0)>>>5,ha>>>0>w>>>0):0){c[C>>2]=0;v=v+(ha+-1)|0}v=v+1|0}while(1){if(c[N+(j<<4)+8>>2]|0)break;if((c[N+(j<<4)>>2]|0)!=1)break;j=j+-1|0}c[N+(j<<4)+12>>2]=-1;v=0;while(1){if(!j)break;e=(c[N+(j<<4)>>2]&16777215)+(c[N+(j<<4)+8>>2]|0)|0;wa=j-e|0;c[N+(wa<<4)+12>>2]=e;j=wa;v=v+1|0}c[t>>2]=(c[t>>2]|0)+v;Da(f,h,ga,N,p,q,r,u);I=I+1|0}ra[c[ba>>2]&1](c[ia>>2]|0,c[J>>2]|0);c[J>>2]=0;ra[c[ba>>2]&1](c[ia>>2]|0,N);ra[c[ba>>2]&1](c[ia>>2]|0,Q);ra[c[ba>>2]&1](c[ia>>2]|0,ja);i=va;return}}function Ja(e,f,g,h,j,k,l,m,n,o,p,q,r){e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;var s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0;ga=i;i=i+32|0;ea=ga+16|0;fa=ga;ca=(1<<c[l+8>>2])+-16|0;t=c[o>>2]|0;da=g+f|0;ba=f>>>0>7?da+-7|0:g;$=l+4|0;Z=(c[$>>2]|0)<9?64:512;s=Z+g|0;if((((g|0)==0^1|h^1)^1)&f>>>0<2049){e=0;while(1){if((e|0)==(f|0))break;Y=j+e|0;X=Y;Y=Y+4|0;Y=lc(d[X>>0]|d[X+1>>0]<<8|d[X+2>>0]<<16|d[X+3>>0]<<24|0,d[Y>>0]|d[Y+1>>0]<<8|d[Y+2>>0]<<16|d[Y+3>>0]<<24|0,-1124073472,1979815)|0;Y=bc(Y|0,C|0,48)|0;c[m+(Y<<2)>>2]=0;e=e+1|0}if(f){a[m+262148>>0]=0;ta=8}}else if(!(a[m+262148>>0]&1))ta=8;else{ac(m|0,0,262149)|0;ta=8}if((ta|0)==8?f>>>0>6&g>>>0>2:0){X=g+-3|0;Y=j+(X&k)|0;W=Y;Y=Y+4|0;Y=lc(d[W>>0]|d[W+1>>0]<<8|d[W+2>>0]<<16|d[W+3>>0]<<24|0,d[Y>>0]|d[Y+1>>0]<<8|d[Y+2>>0]<<16|d[Y+3>>0]<<24|0,-1124073472,1979815)|0;Y=bc(Y|0,C|0,48)|0;c[m+(Y<<2)>>2]=X;Y=g+-2|0;X=j+(Y&k)|0;W=X;X=X+4|0;X=lc(d[W>>0]|d[W+1>>0]<<8|d[W+2>>0]<<16|d[W+3>>0]<<24|0,d[X>>0]|d[X+1>>0]<<8|d[X+2>>0]<<16|d[X+3>>0]<<24|0,-1124073472,1979815)|0;X=bc(X|0,C|0,48)|0;c[m+(X<<2)>>2]=Y;X=g+-1|0;Y=j+(X&k)|0;W=Y;Y=Y+4|0;Y=lc(d[W>>0]|d[W+1>>0]<<8|d[W+2>>0]<<16|d[W+3>>0]<<24|0,d[Y>>0]|d[Y+1>>0]<<8|d[Y+2>>0]<<16|d[Y+3>>0]<<24|0,-1124073472,1979815)|0;Y=bc(Y|0,C|0,48)|0;c[m+(Y<<2)>>2]=X}G=ea+4|0;H=ea+8|0;I=ea+12|0;J=ea+4|0;K=m+262156|0;L=m+262152|0;M=ea+12|0;N=ea+8|0;O=Z<<2;P=da+-7|0;Q=fa+4|0;R=fa+8|0;S=fa+12|0;T=fa+4|0;U=fa+8|0;V=fa+12|0;W=n+8|0;X=n+12|0;Y=n+4|0;l=g;F=p;e=t;a:while(1){B=F;E=s+O|0;b:while(1){D=da-l|0;if((l+8|0)>>>0>=da>>>0)break a;z=l>>>0<ca>>>0?l:ca;c[ea>>2]=0;c[G>>2]=0;c[H>>2]=0;c[I>>2]=4240;A=l&k;x=j+A|0;u=x;w=u;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;u=u+4|0;u=lc(w|0,d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24|0,-1124073472,1979815)|0;u=bc(u|0,C|0,48)|0;v=c[n>>2]|0;h=l-v|0;c[J>>2]=0;if(h>>>0<l>>>0?(ha=h&k,(a[j+ha>>0]|0)==(w&255)<<24>>24):0){f=A+D|0;t=j+f|0;f=j+(f+-4)|0;g=0;h=x;while(1){if(h>>>0>f>>>0)break;y=j+(ha+g)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24|0))break;g=g+4|0;h=h+4|0}while(1){if(h>>>0>=t>>>0)break;if((a[j+(ha+g)>>0]|0)!=(a[h>>0]|0))break;g=g+1|0;h=h+1|0}if(g>>>0>3){ta=22;break}}y=m+(u<<2)|0;t=c[y>>2]|0;c[y>>2]=l;u=l-t|0;v=t&k;if(!(((l|0)==(t|0)?1:(a[j+v>>0]|0)!=(w&255)<<24>>24)|u>>>0>z>>>0)){f=A+D|0;t=j+f|0;f=j+(f+-4)|0;g=0;h=x;while(1){if(h>>>0>f>>>0)break;w=j+(v+g)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24|0))break;g=g+4|0;h=h+4|0}while(1){if(h>>>0>=t>>>0)break;if((a[j+(v+g)>>0]|0)!=(a[h>>0]|0))break;g=g+1|0;h=h+1|0}if(g>>>0>3){ta=32;break}if((c[K>>2]|0)>>>0<(c[L>>2]|0)>>>7>>>0)h=0;else{w=0;h=0;v=(_(d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((w|0)==1)break;x=b[21084+(v<<1)>>1]|0;f=x&65535;c[L>>2]=(c[L>>2]|0)+1;if(x<<16>>16!=0?(ia=f&31,ja=f>>>5,ka=(c[11272+(ia<<2)>>2]|0)+(_(ia,ja)|0)|0,ia>>>0<=D>>>0):0){g=ka+ia|0;u=280811+g|0;g=280811+(g+-4)|0;t=0;f=280811+ka|0;while(1){if(f>>>0>g>>>0)break;x=j+(A+t)|0;if((d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24|0)!=(d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24|0))break;t=t+4|0;f=f+4|0}while(1){if(f>>>0>=u>>>0)break;if((a[j+(A+t)>>0]|0)!=(a[f>>0]|0))break;t=t+1|0;f=f+1|0}if(!((t+10|0)>>>0<=ia>>>0|(t|0)==0)?(la=z+ja+1+(d[407930+(ia-t)>>0]<<d[280786+ia>>0])|0,ma=(t*540|0)+3840+(_((aa(la|0)|0)^31,-120)|0)|0,ma>>>0>=(c[M>>2]|0)>>>0):0){c[ea>>2]=t;c[J>>2]=ia^t;c[N>>2]=la;c[M>>2]=ma;c[K>>2]=(c[K>>2]|0)+1;h=1}}w=w+1|0;v=v+1|0}h=(h&1)!=0}c[y>>2]=l;if(h&1){E=0;break}}e=e+1|0;f=l+1|0;if(f>>>0<=s>>>0){l=f;continue}if(f>>>0>E>>>0){h=l+17|0;h=h>>>0<P>>>0?h:P;l=f;while(1){if(l>>>0>=h>>>0)continue b;D=j+(l&k)|0;A=D;D=D+4|0;D=lc(d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0,d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0,-1124073472,1979815)|0;D=bc(D|0,C|0,48)|0;c[m+(D<<2)>>2]=l;l=l+4|0;e=e+4|0}}else{h=l+9|0;h=h>>>0<P>>>0?h:P;l=f;while(1){if(l>>>0>=h>>>0)continue b;D=j+(l&k)|0;A=D;D=D+4|0;D=lc(d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0,d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0,-1124073472,1979815)|0;D=bc(D|0,C|0,48)|0;c[m+(D<<2)>>2]=l;l=l+2|0;e=e+2|0}}}if((ta|0)==22){ta=0;c[ea>>2]=g;c[N>>2]=v;c[M>>2]=(g*540|0)+3900;c[m+(u<<2)>>2]=l;E=0}else if((ta|0)==32){ta=0;c[ea>>2]=g;c[N>>2]=u;c[M>>2]=(g*540|0)+3840+(_((aa(u|0)|0)^31,-120)|0);E=0}c:while(1){D=D+-1|0;if((c[$>>2]|0)<5){v=(c[ea>>2]|0)+-1|0;v=v>>>0<D>>>0?v:D}else v=0;c[fa>>2]=v;c[Q>>2]=0;c[R>>2]=0;c[S>>2]=4240;x=l+1|0;A=x>>>0<ca>>>0?x:ca;B=x&k;y=j+B|0;t=y;w=t;t=t+4|0;t=lc(d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24|0,d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0,-1124073472,1979815)|0;t=bc(t|0,C|0,48)|0;w=a[j+(B+v)>>0]|0;u=c[n>>2]|0;h=x-u|0;c[T>>2]=0;if(h>>>0<x>>>0?(na=h&k,w<<24>>24==(a[j+(na+v)>>0]|0)):0){f=B+D|0;g=j+f|0;f=j+(f+-4)|0;s=0;h=y;while(1){if(h>>>0>f>>>0)break;z=j+(na+s)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24|0))break;s=s+4|0;h=h+4|0}while(1){if(h>>>0>=g>>>0)break;if((a[j+(na+s)>>0]|0)!=(a[h>>0]|0))break;s=s+1|0;h=h+1|0}if(s>>>0>3){c[fa>>2]=s;c[U>>2]=u;c[V>>2]=(s*540|0)+3900;c[m+(t<<2)>>2]=x}else ta=64}else ta=64;do if((ta|0)==64){ta=0;z=m+(t<<2)|0;g=c[z>>2]|0;c[z>>2]=x;t=x-g|0;u=g&k;if(((x|0)==(g|0)?1:w<<24>>24!=(a[j+(u+v)>>0]|0))|t>>>0>A>>>0){x=l;w=e;break c}f=B+D|0;g=j+f|0;f=j+(f+-4)|0;s=0;h=y;while(1){if(h>>>0>f>>>0)break;w=j+(u+s)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24|0))break;s=s+4|0;h=h+4|0}while(1){if(h>>>0>=g>>>0)break;if((a[j+(u+s)>>0]|0)!=(a[h>>0]|0))break;s=s+1|0;h=h+1|0}if(s>>>0>3){c[fa>>2]=s;c[U>>2]=t;c[V>>2]=(s*540|0)+3840+(_((aa(t|0)|0)^31,-120)|0);break}if((c[K>>2]|0)>>>0<(c[L>>2]|0)>>>7>>>0)h=0;else{v=0;h=0;u=(_(d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==1)break;y=b[21084+(u<<1)>>1]|0;f=y&65535;c[L>>2]=(c[L>>2]|0)+1;if(y<<16>>16!=0?(oa=f&31,pa=f>>>5,qa=(c[11272+(oa<<2)>>2]|0)+(_(oa,pa)|0)|0,oa>>>0<=D>>>0):0){s=qa+oa|0;t=280811+s|0;s=280811+(s+-4)|0;g=0;f=280811+qa|0;while(1){if(f>>>0>s>>>0)break;y=j+(B+g)|0;if((d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24|0)!=(d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24|0))break;g=g+4|0;f=f+4|0}while(1){if(f>>>0>=t>>>0)break;if((a[j+(B+g)>>0]|0)!=(a[f>>0]|0))break;g=g+1|0;f=f+1|0}if(!((g+10|0)>>>0<=oa>>>0|(g|0)==0)?(ra=A+pa+1+(d[407930+(oa-g)>>0]<<d[280786+oa>>0])|0,sa=(g*540|0)+3840+(_((aa(ra|0)|0)^31,-120)|0)|0,sa>>>0>=(c[V>>2]|0)>>>0):0){c[fa>>2]=g;c[T>>2]=oa^g;c[U>>2]=ra;c[V>>2]=sa;c[K>>2]=(c[K>>2]|0)+1;h=1}}v=v+1|0;u=u+1|0}h=(h&1)!=0}c[z>>2]=x;if(!(h&1)){x=l;w=e;break c}}while(0);if((c[S>>2]|0)>>>0<((c[I>>2]|0)+700|0)>>>0){x=l;w=e;break}e=e+1|0;c[ea>>2]=c[fa>>2];c[ea+4>>2]=c[fa+4>>2];c[ea+8>>2]=c[fa+8>>2];c[ea+12>>2]=c[fa+12>>2];E=E+1|0;if(!((E|0)<4&(l+9|0)>>>0<da>>>0)){w=e;break}else l=x}l=c[ea>>2]|0;s=x+(l<<1)+Z|0;e=c[H>>2]|0;d:do if(e>>>0<=(x>>>0<ca>>>0?x:ca)>>>0){g=e+3|0;E=c[n>>2]|0;h=g-E|0;f=c[Y>>2]|0;g=g-f|0;if((e|0)==(E|0))e=0;else{e:do if((e|0)!=(f|0)){do if(h>>>0<7)e=158663784>>>(h<<2)&15;else{if(g>>>0<7){e=266017486>>>(g<<2)&15;break}if((e|0)==(c[W>>2]|0)){e=2;break e}if((e|0)==(c[X>>2]|0)){e=3;break e}e=e+15|0}while(0);if(!e)break d}else e=1;while(0);c[X>>2]=c[W>>2];c[W>>2]=c[Y>>2];c[Y>>2]=c[n>>2];c[n>>2]=c[H>>2];l=c[ea>>2]|0}}else e=e+15|0;while(0);v=F+16|0;E=c[G>>2]|0;h=l^E;c[F>>2]=w;c[F+4>>2]=l|E<<24;l=F+14|0;if(e>>>0<16){e=e&65535;b[l>>1]=e;l=0}else{B=e+-12|0;E=((aa(B|0)|0)^31)+-1|0;D=B>>>E&1;e=((E<<1)+65534|D)+16&65535;b[l>>1]=e;l=E<<24|B-((D|2)<<E)}c[F+8>>2]=l;t=e<<16>>16==0;u=F+12|0;do if(w>>>0>=6){if(w>>>0<130){F=w+-2|0;g=((aa(F|0)|0)^31)+-1|0;g=(g<<1)+(F>>>g)+2&65535;break}if(w>>>0<2114){g=((aa(w+-66|0)|0)^31)+10&65535;break}if(w>>>0<6210)g=21;else g=w>>>0<22594?22:23}else g=w&65535;while(0);do if(h>>>0>=10){if(h>>>0<134){F=h+-6|0;e=((aa(F|0)|0)^31)+-1|0;e=(e<<1)+(F>>>e)+4&65535;break}if(h>>>0<2118)e=((aa(h+-70|0)|0)^31)+12&65535;else e=23}else e=h+65534&65535;while(0);l=e&65535;h=g&65535;f=l&7|h<<3&56;if(t&(g&65535)<8&(e&65535)<16)e=((e&65535)<8?f:f|64)&65535;else e=b[88156+((l>>>3)+((h>>>3)*3|0)<<1)>>1]|f&65535;b[u>>1]=e;c[r>>2]=(c[r>>2]|0)+w;e=x+(c[ea>>2]|0)|0;e=e>>>0<ba>>>0?e:ba;l=x+2|0;while(1){if(l>>>0>=e>>>0)break;F=j+(l&k)|0;E=F;F=F+4|0;F=lc(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0,d[F>>0]|d[F+1>>0]<<8|d[F+2>>0]<<16|d[F+3>>0]<<24|0,-1124073472,1979815)|0;F=bc(F|0,C|0,48)|0;c[m+(F<<2)>>2]=l;l=l+1|0}l=x+(c[ea>>2]|0)|0;F=v;e=0}c[o>>2]=e+D;c[q>>2]=(c[q>>2]|0)+(B-p>>4);i=ga;return}function Ka(e,f,g,h,i,j,k,l,m,n,o,p,q){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;var r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0;X=(1<<c[k+8>>2])+-16|0;s=c[n>>2]|0;Y=g+f|0;W=f>>>0>7?Y+-7|0:g;V=k+4|0;U=(c[V>>2]|0)<9?64:512;k=U+g|0;if((((g|0)==0^1|h^1)^1)&f>>>0<2049){e=0;while(1){if((e|0)==(f|0))break;T=i+e|0;S=T;T=T+4|0;T=lc(d[S>>0]|d[S+1>>0]<<8|d[S+2>>0]<<16|d[S+3>>0]<<24|0,d[T>>0]|d[T+1>>0]<<8|d[T+2>>0]<<16|d[T+3>>0]<<24|0,-1124073472,1979815)|0;T=bc(T|0,C|0,48)|0;T=l+(T<<2)|0;c[T>>2]=0;c[T+4>>2]=0;e=e+1|0}if(f){a[l+262152>>0]=0;r=8}}else if(!(a[l+262152>>0]&1))r=8;else{ac(l|0,0,262153)|0;r=8}if((r|0)==8?f>>>0>6&g>>>0>2:0){T=g+-3|0;S=i+(T&j)|0;R=S;S=S+4|0;S=lc(d[R>>0]|d[R+1>>0]<<8|d[R+2>>0]<<16|d[R+3>>0]<<24|0,d[S>>0]|d[S+1>>0]<<8|d[S+2>>0]<<16|d[S+3>>0]<<24|0,-1124073472,1979815)|0;S=bc(S|0,C|0,48)|0;c[l+(S+(T>>>3&1)<<2)>>2]=T;T=g+-2|0;S=i+(T&j)|0;R=S;S=S+4|0;S=lc(d[R>>0]|d[R+1>>0]<<8|d[R+2>>0]<<16|d[R+3>>0]<<24|0,d[S>>0]|d[S+1>>0]<<8|d[S+2>>0]<<16|d[S+3>>0]<<24|0,-1124073472,1979815)|0;S=bc(S|0,C|0,48)|0;c[l+(S+(T>>>3&1)<<2)>>2]=T;T=g+-1|0;S=i+(T&j)|0;R=S;S=S+4|0;S=lc(d[R>>0]|d[R+1>>0]<<8|d[R+2>>0]<<16|d[R+3>>0]<<24|0,d[S>>0]|d[S+1>>0]<<8|d[S+2>>0]<<16|d[S+3>>0]<<24|0,-1124073472,1979815)|0;S=bc(S|0,C|0,48)|0;c[l+(S+(T>>>3&1)<<2)>>2]=T}P=U<<2;Q=Y+-7|0;R=m+8|0;S=m+12|0;T=m+4|0;h=g;O=o;v=k;e=s;a:while(1){J=O;K=v+P|0;b:while(1){M=Y-h|0;if((h+8|0)>>>0>=Y>>>0)break a;F=h>>>0<X>>>0?h:X;G=h&j;H=i+G|0;I=H;k=I;k=d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24;I=I+4|0;I=lc(k|0,d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24|0,-1124073472,1979815)|0;I=bc(I|0,C|0,48)|0;t=k&255;f=c[m>>2]|0;r=h-f|0;if(r>>>0<h>>>0?(Z=r&j,(a[i+Z>>0]|0)==(k&255)<<24>>24):0){r=G+M|0;s=i+r|0;r=i+(r+-4)|0;g=0;k=H;while(1){if(k>>>0>r>>>0)break;N=i+(Z+g)|0;if((d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24|0)!=(d[N>>0]|d[N+1>>0]<<8|d[N+2>>0]<<16|d[N+3>>0]<<24|0))break;g=g+4|0;k=k+4|0}while(1){if(k>>>0>=s>>>0)break;if((a[i+(Z+g)>>0]|0)!=(a[k>>0]|0))break;g=g+1|0;k=k+1|0}if(g>>>0>3){t=d[i+(G+g)>>0]|0;k=1;s=g;g=(g*540|0)+3900|0}else{k=0;s=0;f=0;g=4240}}else{k=0;s=0;f=0;g=4240}D=G+M|0;B=i+D|0;D=i+(D+-4)|0;x=s;u=g;E=l+(I+1<<2)|0;y=0;r=l+(I<<2)|0;z=s;A=g;while(1){r=c[r>>2]|0;if((y|0)==2)break;w=h-r|0;s=r&j;if(!(((h|0)==(r|0)?1:(t|0)!=(d[i+(s+x)>>0]|0))|w>>>0>F>>>0)){r=0;g=H;while(1){if(g>>>0>D>>>0)break;N=i+(s+r)|0;if((d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24|0)!=(d[N>>0]|d[N+1>>0]<<8|d[N+2>>0]<<16|d[N+3>>0]<<24|0))break;r=r+4|0;g=g+4|0}while(1){if(g>>>0>=B>>>0)break;if((a[i+(s+r)>>0]|0)!=(a[g>>0]|0))break;r=r+1|0;g=g+1|0}if(r>>>0>3?($=(r*540|0)+3840+(_((aa(w|0)|0)^31,-120)|0)|0,u>>>0<$>>>0):0){s=r;u=$;t=d[i+(G+r)>>0]|0;k=1;g=r;f=w;r=$}else{s=x;g=z;r=A}}else{s=x;g=z;r=A}N=E;x=s;E=E+4|0;y=y+1|0;z=g;A=r;r=N}c[l+(I+(h>>>3&1)<<2)>>2]=h;if(k&1){N=0;break}e=e+1|0;f=h+1|0;if(f>>>0<=v>>>0){h=f;continue}if(f>>>0>K>>>0){k=h+17|0;k=k>>>0<Q>>>0?k:Q;h=f;while(1){if(h>>>0>=k>>>0)continue b;N=i+(h&j)|0;M=N;N=N+4|0;N=lc(d[M>>0]|d[M+1>>0]<<8|d[M+2>>0]<<16|d[M+3>>0]<<24|0,d[N>>0]|d[N+1>>0]<<8|d[N+2>>0]<<16|d[N+3>>0]<<24|0,-1124073472,1979815)|0;N=bc(N|0,C|0,48)|0;c[l+(N+(h>>>3&1)<<2)>>2]=h;h=h+4|0;e=e+4|0}}else{k=h+9|0;k=k>>>0<Q>>>0?k:Q;h=f;while(1){if(h>>>0>=k>>>0)continue b;N=i+(h&j)|0;M=N;N=N+4|0;N=lc(d[M>>0]|d[M+1>>0]<<8|d[M+2>>0]<<16|d[M+3>>0]<<24|0,d[N>>0]|d[N+1>>0]<<8|d[N+2>>0]<<16|d[N+3>>0]<<24|0,-1124073472,1979815)|0;N=bc(N|0,C|0,48)|0;c[l+(N+(h>>>3&1)<<2)>>2]=h;h=h+2|0;e=e+2|0}}}while(1){M=M+-1|0;if((c[V>>2]|0)<5){u=z+-1|0;u=u>>>0<M>>>0?u:M}else u=0;x=h+1|0;I=x>>>0<X>>>0?x:X;J=x&j;K=i+J|0;L=K;k=L;L=L+4|0;L=lc(d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24|0,d[L>>0]|d[L+1>>0]<<8|d[L+2>>0]<<16|d[L+3>>0]<<24|0,-1124073472,1979815)|0;L=bc(L|0,C|0,48)|0;k=a[i+(J+u)>>0]|0;t=k&255;v=c[m>>2]|0;r=x-v|0;if(r>>>0<x>>>0?(ba=r&j,k<<24>>24==(a[i+(ba+u)>>0]|0)):0){r=J+M|0;s=i+r|0;r=i+(r+-4)|0;g=0;k=K;while(1){if(k>>>0>r>>>0)break;H=i+(ba+g)|0;if((d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24|0)!=(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24|0))break;g=g+4|0;k=k+4|0}while(1){if(k>>>0>=s>>>0)break;if((a[i+(ba+g)>>0]|0)!=(a[k>>0]|0))break;g=g+1|0;k=k+1|0}if(g>>>0>3){t=d[i+(J+g)>>0]|0;k=1;u=g;r=(g*540|0)+3900|0}else{k=0;r=4240;v=0}}else{k=0;r=4240;v=0}G=J+M|0;F=i+G|0;G=i+(G+-4)|0;B=u;D=r;H=l+(L+1<<2)|0;E=0;s=k;k=l+(L<<2)|0;y=r;while(1){k=c[k>>2]|0;if((E|0)==2)break;w=x-k|0;g=k&j;if(!(((x|0)==(k|0)?1:(t|0)!=(d[i+(g+B)>>0]|0))|w>>>0>I>>>0)){k=0;r=K;while(1){if(r>>>0>G>>>0)break;da=i+(g+k)|0;if((d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24|0)!=(d[da>>0]|d[da+1>>0]<<8|d[da+2>>0]<<16|d[da+3>>0]<<24|0))break;k=k+4|0;r=r+4|0}while(1){if(r>>>0>=F>>>0)break;if((a[i+(g+k)>>0]|0)!=(a[r>>0]|0))break;k=k+1|0;r=r+1|0}if(k>>>0>3?(ca=(k*540|0)+3840+(_((aa(w|0)|0)^31,-120)|0)|0,D>>>0<ca>>>0):0){r=k;g=ca;t=d[i+(J+k)>>0]|0;s=1;u=k;k=ca;v=w}else{r=B;g=D;k=y}}else{r=B;g=D;k=y}da=H;B=r;D=g;H=H+4|0;E=E+1|0;y=k;k=da}c[l+(L+(x>>>3&1)<<2)>>2]=x;if((s&1)==0|y>>>0<(A+700|0)>>>0){x=h;w=e;u=z;break}e=e+1|0;N=N+1|0;if(!((N|0)<4&(h+9|0)>>>0<Y>>>0)){w=e;f=v;break}else{h=x;z=u;f=v;A=y}}v=x+(u<<1)+U|0;c:do if(f>>>0<=(x>>>0<X>>>0?x:X)>>>0){h=f+3|0;da=c[m>>2]|0;e=h-da|0;k=c[T>>2]|0;h=h-k|0;if((f|0)==(da|0))e=0;else{d:do if((f|0)!=(k|0)){do if(e>>>0<7)e=158663784>>>(e<<2)&15;else{if(h>>>0<7){e=266017486>>>(h<<2)&15;break}if((f|0)==(c[R>>2]|0)){e=2;break d}if((f|0)==(c[S>>2]|0)){e=3;break d}e=f+15|0}while(0);if(!e)break c}else e=1;while(0);c[S>>2]=c[R>>2];c[R>>2]=c[T>>2];c[T>>2]=c[m>>2];c[m>>2]=f}}else e=f+15|0;while(0);t=O+16|0;c[O>>2]=w;c[O+4>>2]=u;k=O+14|0;if(e>>>0<16){e=e&65535;b[k>>1]=e;k=0}else{M=e+-12|0;da=((aa(M|0)|0)^31)+-1|0;N=M>>>da&1;e=((da<<1)+65534|N)+16&65535;b[k>>1]=e;k=da<<24|M-((N|2)<<da)}c[O+8>>2]=k;g=e<<16>>16==0;s=O+12|0;do if(w>>>0>=6){if(w>>>0<130){da=w+-2|0;k=((aa(da|0)|0)^31)+-1|0;k=(k<<1)+(da>>>k)+2&65535;break}if(w>>>0<2114){k=((aa(w+-66|0)|0)^31)+10&65535;break}if(w>>>0<6210)k=21;else k=w>>>0<22594?22:23}else k=w&65535;while(0);do if(u>>>0>=10){if(u>>>0<134){da=u+-6|0;e=((aa(da|0)|0)^31)+-1|0;e=(e<<1)+(da>>>e)+4&65535;break}if(u>>>0<2118)e=((aa(u+-70|0)|0)^31)+12&65535;else e=23}else e=u+65534&65535;while(0);h=e&65535;f=k&65535;r=h&7|f<<3&56;if(g&(k&65535)<8&(e&65535)<16)e=((e&65535)<8?r:r|64)&65535;else e=b[88156+((h>>>3)+((f>>>3)*3|0)<<1)>>1]|r&65535;b[s>>1]=e;c[q>>2]=(c[q>>2]|0)+w;h=x+u|0;k=h>>>0<W>>>0?h:W;e=x+2|0;while(1){if(e>>>0>=k>>>0){O=t;e=0;continue a}da=i+(e&j)|0;O=da;da=da+4|0;da=lc(d[O>>0]|d[O+1>>0]<<8|d[O+2>>0]<<16|d[O+3>>0]<<24|0,d[da>>0]|d[da+1>>0]<<8|d[da+2>>0]<<16|d[da+3>>0]<<24|0,-1124073472,1979815)|0;da=bc(da|0,C|0,48)|0;c[l+(da+(e>>>3&1)<<2)>>2]=e;e=e+1|0}}c[n>>2]=e+M;c[p>>2]=(c[p>>2]|0)+(J-o>>4);return}function La(e,f,g,h,j,k,l,m,n,o,p,q,r){e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;var s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0;la=i;i=i+32|0;ja=la+16|0;ka=la;ha=(1<<c[l+8>>2])+-16|0;u=c[o>>2]|0;ia=g+f|0;ga=f>>>0>7?ia+-7|0:g;fa=l+4|0;ea=(c[fa>>2]|0)<9?64:512;t=ea+g|0;if((((g|0)==0^1|h^1)^1)&f>>>0<4097){e=0;while(1){if((e|0)==(f|0))break;da=j+e|0;ca=da;da=da+4|0;da=lc(d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0,d[da>>0]|d[da+1>>0]<<8|d[da+2>>0]<<16|d[da+3>>0]<<24|0,-1124073472,1979815)|0;da=bc(da|0,C|0,47)|0;da=m+(da<<2)|0;c[da>>2]=0;c[da+4>>2]=0;c[da+8>>2]=0;c[da+12>>2]=0;e=e+1|0}if(f){a[m+524304>>0]=0;s=8}}else if(!(a[m+524304>>0]&1))s=8;else{ac(m|0,0,524305)|0;s=8}if((s|0)==8?f>>>0>6&g>>>0>2:0){da=g+-3|0;ca=j+(da&k)|0;ba=ca;ca=ca+4|0;ca=lc(d[ba>>0]|d[ba+1>>0]<<8|d[ba+2>>0]<<16|d[ba+3>>0]<<24|0,d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0,-1124073472,1979815)|0;ca=bc(ca|0,C|0,47)|0;c[m+(ca+(da>>>3&3)<<2)>>2]=da;da=g+-2|0;ca=j+(da&k)|0;ba=ca;ca=ca+4|0;ca=lc(d[ba>>0]|d[ba+1>>0]<<8|d[ba+2>>0]<<16|d[ba+3>>0]<<24|0,d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0,-1124073472,1979815)|0;ca=bc(ca|0,C|0,47)|0;c[m+(ca+(da>>>3&3)<<2)>>2]=da;da=g+-1|0;ca=j+(da&k)|0;ba=ca;ca=ca+4|0;ca=lc(d[ba>>0]|d[ba+1>>0]<<8|d[ba+2>>0]<<16|d[ba+3>>0]<<24|0,d[ca>>0]|d[ca+1>>0]<<8|d[ca+2>>0]<<16|d[ca+3>>0]<<24|0,-1124073472,1979815)|0;ca=bc(ca|0,C|0,47)|0;c[m+(ca+(da>>>3&3)<<2)>>2]=da}R=ja+4|0;S=ja+8|0;T=ja+12|0;U=ja+12|0;V=ja+4|0;W=ja+8|0;X=m+524312|0;Y=m+524308|0;Z=ea<<2;$=ia+-7|0;ba=ka+12|0;ca=ka+4|0;da=ka+8|0;L=ka+12|0;M=ka+4|0;N=ka+8|0;O=n+8|0;P=n+12|0;Q=n+4|0;l=g;K=p;e=u;a:while(1){I=K;J=t+Z|0;b:while(1){H=ia-l|0;if((l+8|0)>>>0>=ia>>>0)break a;E=l>>>0<ha>>>0?l:ha;c[ja>>2]=0;c[R>>2]=0;c[S>>2]=0;c[T>>2]=4240;F=l&k;D=j+F|0;G=D;h=G;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;G=G+4|0;G=lc(h|0,d[G>>0]|d[G+1>>0]<<8|d[G+2>>0]<<16|d[G+3>>0]<<24|0,-1124073472,1979815)|0;G=bc(G|0,C|0,47)|0;w=h&255;v=c[U>>2]|0;u=c[n>>2]|0;f=l-u|0;c[V>>2]=0;if(f>>>0<l>>>0?(ma=f&k,(a[j+ma>>0]|0)==(h&255)<<24>>24):0){f=F+H|0;g=j+f|0;f=j+(f+-4)|0;s=0;h=D;while(1){if(h>>>0>f>>>0)break;B=j+(ma+s)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24|0))break;s=s+4|0;h=h+4|0}while(1){if(h>>>0>=g>>>0)break;if((a[j+(ma+s)>>0]|0)!=(a[h>>0]|0))break;s=s+1|0;h=h+1|0}if(s>>>0>3){g=(s*540|0)+3900|0;c[ja>>2]=s;c[W>>2]=u;c[U>>2]=g;f=s;s=d[j+(F+s)>>0]|0;h=1}else{f=0;g=v;s=w;h=0}}else{f=0;g=v;s=w;h=0}B=F+H|0;A=j+B|0;B=j+(B+-4)|0;z=m+(G+1<<2)|0;y=0;u=m+(G<<2)|0;while(1){u=c[u>>2]|0;if((y|0)==4)break;w=l-u|0;x=u&k;if(!(((l|0)==(u|0)?1:(s|0)!=(d[j+(x+f)>>0]|0))|w>>>0>E>>>0)){u=0;v=D;while(1){if(v>>>0>B>>>0)break;Aa=j+(x+u)|0;if((d[v>>0]|d[v+1>>0]<<8|d[v+2>>0]<<16|d[v+3>>0]<<24|0)!=(d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0))break;u=u+4|0;v=v+4|0}while(1){if(v>>>0>=A>>>0)break;if((a[j+(x+u)>>0]|0)!=(a[v>>0]|0))break;u=u+1|0;v=v+1|0}if(u>>>0>3?(na=(u*540|0)+3840+(_((aa(w|0)|0)^31,-120)|0)|0,g>>>0<na>>>0):0){c[ja>>2]=u;c[W>>2]=w;c[U>>2]=na;f=u;g=na;s=d[j+(F+u)>>0]|0;h=1}}u=z;z=z+4|0;y=y+1|0}if(!(h&1)){if((c[X>>2]|0)>>>0<(c[Y>>2]|0)>>>7>>>0)h=0;else{w=0;h=0;v=(_(d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((w|0)==1)break;Aa=b[21084+(v<<1)>>1]|0;f=Aa&65535;c[Y>>2]=(c[Y>>2]|0)+1;if(Aa<<16>>16!=0?(oa=f&31,pa=f>>>5,qa=(c[11272+(oa<<2)>>2]|0)+(_(oa,pa)|0)|0,oa>>>0<=H>>>0):0){s=qa+oa|0;u=280811+s|0;s=280811+(s+-4)|0;g=0;f=280811+qa|0;while(1){if(f>>>0>s>>>0)break;Aa=j+(F+g)|0;if((d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24|0)!=(d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0))break;g=g+4|0;f=f+4|0}while(1){if(f>>>0>=u>>>0)break;if((a[j+(F+g)>>0]|0)!=(a[f>>0]|0))break;g=g+1|0;f=f+1|0}if(!((g+10|0)>>>0<=oa>>>0|(g|0)==0)?(ra=E+pa+1+(d[407930+(oa-g)>>0]<<d[280786+oa>>0])|0,sa=(g*540|0)+3840+(_((aa(ra|0)|0)^31,-120)|0)|0,sa>>>0>=(c[U>>2]|0)>>>0):0){c[ja>>2]=g;c[V>>2]=oa^g;c[W>>2]=ra;c[U>>2]=sa;c[X>>2]=(c[X>>2]|0)+1;h=1}}w=w+1|0;v=v+1|0}h=(h&1)!=0}h=h&1}c[m+(G+(l>>>3&3)<<2)>>2]=l;if(h&1){I=0;break}e=e+1|0;f=l+1|0;if(f>>>0<=t>>>0){l=f;continue}if(f>>>0>J>>>0){h=l+17|0;h=h>>>0<$>>>0?h:$;l=f;while(1){if(l>>>0>=h>>>0)continue b;Aa=j+(l&k)|0;H=Aa;Aa=Aa+4|0;Aa=lc(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24|0,d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0,-1124073472,1979815)|0;Aa=bc(Aa|0,C|0,47)|0;c[m+(Aa+(l>>>3&3)<<2)>>2]=l;l=l+4|0;e=e+4|0}}else{h=l+9|0;h=h>>>0<$>>>0?h:$;l=f;while(1){if(l>>>0>=h>>>0)continue b;Aa=j+(l&k)|0;H=Aa;Aa=Aa+4|0;Aa=lc(d[H>>0]|d[H+1>>0]<<8|d[H+2>>0]<<16|d[H+3>>0]<<24|0,d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0,-1124073472,1979815)|0;Aa=bc(Aa|0,C|0,47)|0;c[m+(Aa+(l>>>3&3)<<2)>>2]=l;l=l+2|0;e=e+2|0}}}while(1){H=H+-1|0;if((c[fa>>2]|0)<5){u=(c[ja>>2]|0)+-1|0;u=u>>>0<H>>>0?u:H}else u=0;c[ka>>2]=u;c[ca>>2]=0;c[da>>2]=0;c[ba>>2]=4240;x=l+1|0;E=x>>>0<ha>>>0?x:ha;F=x&k;D=j+F|0;G=D;h=G;G=G+4|0;G=lc(d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0,d[G>>0]|d[G+1>>0]<<8|d[G+2>>0]<<16|d[G+3>>0]<<24|0,-1124073472,1979815)|0;G=bc(G|0,C|0,47)|0;h=a[j+(F+u)>>0]|0;w=h&255;t=c[L>>2]|0;v=c[n>>2]|0;f=x-v|0;c[M>>2]=0;if(f>>>0<x>>>0?(ta=f&k,h<<24>>24==(a[j+(ta+u)>>0]|0)):0){f=F+H|0;g=j+f|0;f=j+(f+-4)|0;s=0;h=D;while(1){if(h>>>0>f>>>0)break;Aa=j+(ta+s)|0;if((d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24|0)!=(d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0))break;s=s+4|0;h=h+4|0}while(1){if(h>>>0>=g>>>0)break;if((a[j+(ta+s)>>0]|0)!=(a[h>>0]|0))break;s=s+1|0;h=h+1|0}if(s>>>0>3){t=(s*540|0)+3900|0;c[ka>>2]=s;c[N>>2]=v;c[L>>2]=t;f=s;s=d[j+(F+s)>>0]|0;h=1}else{f=u;s=w;h=0}}else{f=u;s=w;h=0}B=F+H|0;A=j+B|0;B=j+(B+-4)|0;z=m+(G+1<<2)|0;y=0;g=m+(G<<2)|0;while(1){g=c[g>>2]|0;if((y|0)==4)break;v=x-g|0;w=g&k;if(!(((x|0)==(g|0)?1:(s|0)!=(d[j+(w+f)>>0]|0))|v>>>0>E>>>0)){g=0;u=D;while(1){if(u>>>0>B>>>0)break;Aa=j+(w+g)|0;if((d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24|0)!=(d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0))break;g=g+4|0;u=u+4|0}while(1){if(u>>>0>=A>>>0)break;if((a[j+(w+g)>>0]|0)!=(a[u>>0]|0))break;g=g+1|0;u=u+1|0}if(g>>>0>3?(ua=(g*540|0)+3840+(_((aa(v|0)|0)^31,-120)|0)|0,t>>>0<ua>>>0):0){c[ka>>2]=g;c[N>>2]=v;c[L>>2]=ua;f=g;t=ua;s=d[j+(F+g)>>0]|0;h=1}}g=z;z=z+4|0;y=y+1|0}if(!(h&1)){if((c[X>>2]|0)>>>0<(c[Y>>2]|0)>>>7>>>0)h=0;else{v=0;h=0;u=(_(d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==1)break;Aa=b[21084+(u<<1)>>1]|0;f=Aa&65535;c[Y>>2]=(c[Y>>2]|0)+1;if(Aa<<16>>16!=0?(va=f&31,wa=f>>>5,xa=(c[11272+(va<<2)>>2]|0)+(_(va,wa)|0)|0,va>>>0<=H>>>0):0){s=xa+va|0;g=280811+s|0;s=280811+(s+-4)|0;t=0;f=280811+xa|0;while(1){if(f>>>0>s>>>0)break;Aa=j+(F+t)|0;if((d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24|0)!=(d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0))break;t=t+4|0;f=f+4|0}while(1){if(f>>>0>=g>>>0)break;if((a[j+(F+t)>>0]|0)!=(a[f>>0]|0))break;t=t+1|0;f=f+1|0}if(!((t+10|0)>>>0<=va>>>0|(t|0)==0)?(ya=E+wa+1+(d[407930+(va-t)>>0]<<d[280786+va>>0])|0,za=(t*540|0)+3840+(_((aa(ya|0)|0)^31,-120)|0)|0,za>>>0>=(c[L>>2]|0)>>>0):0){c[ka>>2]=t;c[M>>2]=va^t;c[N>>2]=ya;c[L>>2]=za;c[X>>2]=(c[X>>2]|0)+1;h=1}}v=v+1|0;u=u+1|0}h=(h&1)!=0}h=h&1}c[m+(G+(x>>>3&3)<<2)>>2]=x;if(!(h&1)){x=l;w=e;break}if((c[ba>>2]|0)>>>0<((c[T>>2]|0)+700|0)>>>0){x=l;w=e;break}e=e+1|0;c[ja>>2]=c[ka>>2];c[ja+4>>2]=c[ka+4>>2];c[ja+8>>2]=c[ka+8>>2];c[ja+12>>2]=c[ka+12>>2];I=I+1|0;if(!((I|0)<4&(l+9|0)>>>0<ia>>>0)){w=e;break}else l=x}l=c[ja>>2]|0;t=x+(l<<1)+ea|0;e=c[S>>2]|0;c:do if(e>>>0<=(x>>>0<ha>>>0?x:ha)>>>0){s=e+3|0;Aa=c[n>>2]|0;h=s-Aa|0;f=c[Q>>2]|0;s=s-f|0;if((e|0)==(Aa|0))e=0;else{d:do if((e|0)!=(f|0)){do if(h>>>0<7)e=158663784>>>(h<<2)&15;else{if(s>>>0<7){e=266017486>>>(s<<2)&15;break}if((e|0)==(c[O>>2]|0)){e=2;break d}if((e|0)==(c[P>>2]|0)){e=3;break d}e=e+15|0}while(0);if(!e)break c}else e=1;while(0);c[P>>2]=c[O>>2];c[O>>2]=c[Q>>2];c[Q>>2]=c[n>>2];c[n>>2]=c[S>>2];l=c[ja>>2]|0}}else e=e+15|0;while(0);v=K+16|0;Aa=c[R>>2]|0;h=l^Aa;c[K>>2]=w;c[K+4>>2]=l|Aa<<24;l=K+14|0;if(e>>>0<16){e=e&65535;b[l>>1]=e;l=0}else{I=e+-12|0;Aa=((aa(I|0)|0)^31)+-1|0;J=I>>>Aa&1;e=((Aa<<1)+65534|J)+16&65535;b[l>>1]=e;l=Aa<<24|I-((J|2)<<Aa)}c[K+8>>2]=l;g=e<<16>>16==0;u=K+12|0;do if(w>>>0>=6){if(w>>>0<130){Aa=w+-2|0;s=((aa(Aa|0)|0)^31)+-1|0;s=(s<<1)+(Aa>>>s)+2&65535;break}if(w>>>0<2114){s=((aa(w+-66|0)|0)^31)+10&65535;break}if(w>>>0<6210)s=21;else s=w>>>0<22594?22:23}else s=w&65535;while(0);do if(h>>>0>=10){if(h>>>0<134){Aa=h+-6|0;e=((aa(Aa|0)|0)^31)+-1|0;e=(e<<1)+(Aa>>>e)+4&65535;break}if(h>>>0<2118)e=((aa(h+-70|0)|0)^31)+12&65535;else e=23}else e=h+65534&65535;while(0);l=e&65535;h=s&65535;f=l&7|h<<3&56;if(g&(s&65535)<8&(e&65535)<16)e=((e&65535)<8?f:f|64)&65535;else e=b[88156+((l>>>3)+((h>>>3)*3|0)<<1)>>1]|f&65535;b[u>>1]=e;c[r>>2]=(c[r>>2]|0)+w;e=x+(c[ja>>2]|0)|0;e=e>>>0<ga>>>0?e:ga;l=x+2|0;while(1){if(l>>>0>=e>>>0)break;Aa=j+(l&k)|0;K=Aa;Aa=Aa+4|0;Aa=lc(d[K>>0]|d[K+1>>0]<<8|d[K+2>>0]<<16|d[K+3>>0]<<24|0,d[Aa>>0]|d[Aa+1>>0]<<8|d[Aa+2>>0]<<16|d[Aa+3>>0]<<24|0,-1124073472,1979815)|0;Aa=bc(Aa|0,C|0,47)|0;c[m+(Aa+(l>>>3&3)<<2)>>2]=l;l=l+1|0}l=x+(c[ja>>2]|0)|0;K=v;e=0}c[o>>2]=e+H;c[q>>2]=(c[q>>2]|0)+(I-p>>4);i=la;return}function Ma(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0;qa=i;i=i+32|0;oa=qa+16|0;pa=qa;ma=(1<<c[m+8>>2])+-16|0;v=c[p>>2]|0;na=h+g|0;la=g>>>0>3?na+-3|0:h;ka=m+4|0;ja=(c[ka>>2]|0)<9?64:512;u=ja+h|0;if((((h|0)==0^1|j^1)^1)&g>>>0<257){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;b[n+((_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>18<<1)>>1]=0;f=f+1|0}if(g){a[n+1081344>>0]=0;t=8}}else{f=n+1081344|0;if(!(a[f>>0]&1))t=8;else{ac(n|0,0,32768)|0;a[f>>0]=0;t=8}}if((t|0)==8?g>>>0>2&h>>>0>2:0){ga=h+-3|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>18;ia=n+(ha<<1)|0;c[n+32768+((e[ia>>1]&15|ha<<4)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16;ia=h+-2|0;ha=k+(ia&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>18;ga=n+(ha<<1)|0;c[n+32768+((e[ga>>1]&15|ha<<4)<<2)>>2]=ia;b[ga>>1]=(b[ga>>1]|0)+1<<16>>16;ga=h+-1|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>18;ia=n+(ha<<1)|0;c[n+32768+((e[ia>>1]&15|ha<<4)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16}P=oa+4|0;Q=oa+8|0;R=oa+12|0;S=oa+4|0;T=oa+8|0;U=oa+12|0;V=n+1081352|0;W=n+1081348|0;X=ja<<2;Y=na+-4|0;Z=na+-3|0;$=pa+12|0;ba=pa+4|0;ca=pa+8|0;da=pa+4|0;ea=pa+8|0;fa=pa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;O=q;f=v;a:while(1){M=O;N=u+X|0;b:while(1){L=na-m|0;if((m+4|0)>>>0>=na>>>0)break a;J=m>>>0<ma>>>0?m:ma;c[oa>>2]=0;c[P>>2]=0;c[Q>>2]=0;c[R>>2]=4240;K=m&l;g=c[oa>>2]|0;c[oa>>2]=0;c[S>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;y=4240;j=0;x=0;while(1){if((x|0)==4)break;w=(c[o+(c[11372+(x<<2)>>2]<<2)>>2]|0)+(c[11436+(x<<2)>>2]|0)|0;t=m-w|0;do if(((!(t>>>0>=m>>>0|w>>>0>J>>>0)?(ra=t&l,sa=K+g|0,sa>>>0<=l>>>0):0)?(ta=ra+g|0,ta>>>0<=l>>>0):0)?(a[k+sa>>0]|0)==(a[k+ta>>0]|0):0){v=0;t=F;while(1){h=t;if(h>>>0>H>>>0)break;D=t;E=k+(ra+v)|0;if((d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;v=v+4|0;t=h+4|0}while(1){if(t>>>0>=G>>>0)break;if((a[k+(ra+v)>>0]|0)!=(a[t>>0]|0))break;v=v+1|0;t=t+1|0}if(v>>>0<=2?!((v|0)==2&x>>>0<2):0){t=y;break}t=(v*540|0)+(c[11500+(x<<2)>>2]|0)|0;if(y>>>0<t>>>0){c[oa>>2]=v;c[T>>2]=w;c[U>>2]=t;g=v;j=1}else t=y}else t=y;while(0);y=t;x=x+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18;C=D<<4;D=n+(D<<1)|0;E=b[D>>1]|0;t=E&65535;E=(E&65535)>16?t+-16|0:0;c:while(1){B=K+g|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(t>>>0<=E>>>0)break c;t=t+-1|0;h=c[n+32768+((C|t&15)<<2)>>2]|0;z=m-h|0;if(z>>>0>J>>>0)break c;x=h&l;if(A)continue;h=x+g|0;if(h>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+h>>0]|0)){w=0;h=F}else continue;while(1){v=h;if(v>>>0>H>>>0)break;Ia=h;Ha=k+(x+w)|0;if((d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0)!=(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0))break;w=w+4|0;h=v+4|0}while(1){if(h>>>0>=G>>>0)break;if((a[k+(x+w)>>0]|0)!=(a[h>>0]|0))break;w=w+1|0;h=h+1|0}if(w>>>0<=3)continue;h=(w*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(y>>>0<h>>>0)break}c[oa>>2]=w;c[T>>2]=z;c[U>>2]=h;g=w;y=h;j=1}Ia=b[D>>1]|0;c[n+32768+((C|Ia&15)<<2)>>2]=m;b[D>>1]=Ia+1<<16>>16;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ia=b[21084+(w<<1)>>1]|0;t=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(ua=t&31,va=t>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=L>>>0):0){g=wa+ua|0;v=280811+g|0;g=280811+(g+-4)|0;h=0;t=280811+wa|0;while(1){if(t>>>0>g>>>0)break;Ia=k+(K+h)|0;if((d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;t=t+4|0}while(1){if(t>>>0>=v>>>0)break;if((a[k+(K+h)>>0]|0)!=(a[t>>0]|0))break;h=h+1|0;t=t+1|0}if(!((h+10|0)>>>0<=ua>>>0|(h|0)==0)?(xa=J+va+1+(d[407930+(ua-h)>>0]<<d[280786+ua>>0])|0,ya=(h*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[U>>2]|0)>>>0):0){c[oa>>2]=h;c[S>>2]=ua^h;c[T>>2]=xa;c[U>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){M=0;x=f;break}f=f+1|0;t=m+1|0;if(t>>>0<=u>>>0){m=t;continue}if(t>>>0>N>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>18;Ia=n+(Ha<<1)|0;c[n+32768+((e[Ia>>1]&15|Ha<<4)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>18;Ia=n+(Ha<<1)|0;c[n+32768+((e[Ia>>1]&15|Ha<<4)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+2|0;f=f+2|0}}}while(1){L=L+-1|0;if((c[ka>>2]|0)<5){f=(c[oa>>2]|0)+-1|0;f=f>>>0<L>>>0?f:L}else f=0;c[pa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;J=y>>>0<ma>>>0?y:ma;K=y&l;t=c[pa>>2]|0;c[pa>>2]=0;c[da>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;w=4240;f=0;v=0;while(1){if((v|0)==4)break;h=(c[o+(c[11372+(v<<2)>>2]<<2)>>2]|0)+(c[11436+(v<<2)>>2]|0)|0;j=y-h|0;do if(((!(j>>>0>=y>>>0|h>>>0>J>>>0)?(za=j&l,Aa=K+t|0,Aa>>>0<=l>>>0):0)?(Ba=za+t|0,Ba>>>0<=l>>>0):0)?(a[k+Aa>>0]|0)==(a[k+Ba>>0]|0):0){u=0;j=F;while(1){g=j;if(g>>>0>H>>>0)break;Ha=j;Ia=k+(za+u)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;u=u+4|0;j=g+4|0}while(1){if(j>>>0>=G>>>0)break;if((a[k+(za+u)>>0]|0)!=(a[j>>0]|0))break;u=u+1|0;j=j+1|0}if(u>>>0<=2?!((u|0)==2&v>>>0<2):0){j=w;break}j=(u*540|0)+(c[11500+(v<<2)>>2]|0)|0;if(w>>>0<j>>>0){c[pa>>2]=u;c[ea>>2]=h;c[fa>>2]=j;t=u;f=1}else j=w}else j=w;while(0);w=j;v=v+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18;C=D<<4;D=n+(D<<1)|0;E=b[D>>1]|0;j=E&65535;E=(E&65535)>16?j+-16|0:0;d:while(1){B=K+t|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(j>>>0<=E>>>0)break d;j=j+-1|0;g=c[n+32768+((C|j&15)<<2)>>2]|0;z=y-g|0;if(z>>>0>J>>>0)break d;v=g&l;if(A)continue;g=v+t|0;if(g>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+g>>0]|0)){h=0;g=F}else continue;while(1){u=g;if(u>>>0>H>>>0)break;Ha=g;Ia=k+(v+h)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;g=u+4|0}while(1){if(g>>>0>=G>>>0)break;if((a[k+(v+h)>>0]|0)!=(a[g>>0]|0))break;h=h+1|0;g=g+1|0}if(h>>>0<=3)continue;g=(h*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(w>>>0<g>>>0)break}c[pa>>2]=h;c[ea>>2]=z;c[fa>>2]=g;t=h;w=g;f=1}Ia=b[D>>1]|0;c[n+32768+((C|Ia&15)<<2)>>2]=y;b[D>>1]=Ia+1<<16>>16;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;h=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ia=b[21084+(h<<1)>>1]|0;j=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(Ca=j&31,Da=j>>>5,Ea=(c[11272+(Ca<<2)>>2]|0)+(_(Ca,Da)|0)|0,Ca>>>0<=L>>>0):0){t=Ea+Ca|0;u=280811+t|0;t=280811+(t+-4)|0;g=0;j=280811+Ea|0;while(1){if(j>>>0>t>>>0)break;Ia=k+(K+g)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;g=g+4|0;j=j+4|0}while(1){if(j>>>0>=u>>>0)break;if((a[k+(K+g)>>0]|0)!=(a[j>>0]|0))break;g=g+1|0;j=j+1|0}if(!((g+10|0)>>>0<=Ca>>>0|(g|0)==0)?(Fa=J+Da+1+(d[407930+(Ca-g)>>0]<<d[280786+Ca>>0])|0,Ga=(g*540|0)+3840+(_((aa(Fa|0)|0)^31,-120)|0)|0,Ga>>>0>=(c[fa>>2]|0)>>>0):0){c[pa>>2]=g;c[da>>2]=Ca^g;c[ea>>2]=Fa;c[fa>>2]=Ga;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;h=h+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[R>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[oa>>2]=c[pa>>2];c[oa+4>>2]=c[pa+4>>2];c[oa+8>>2]=c[pa+8>>2];c[oa+12>>2]=c[pa+12>>2];M=M+1|0;if(!((M|0)<4&(m+5|0)>>>0<na>>>0)){x=f;break}else{m=y;x=f}}m=c[oa>>2]|0;u=y+(m<<1)+ja|0;f=c[Q>>2]|0;e:do if(f>>>0<=(y>>>0<ma>>>0?y:ma)>>>0){g=f+3|0;Ia=c[o>>2]|0;j=g-Ia|0;t=c[ia>>2]|0;g=g-t|0;if((f|0)==(Ia|0))f=0;else{f:do if((f|0)!=(t|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(g>>>0<7){f=266017486>>>(g<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[Q>>2];m=c[oa>>2]|0}}else f=f+15|0;while(0);w=O+16|0;Ia=c[P>>2]|0;j=m^Ia;c[O>>2]=x;c[O+4>>2]=m|Ia<<24;m=O+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{N=f+-12|0;Ia=((aa(N|0)|0)^31)+-1|0;Ha=N>>>Ia&1;f=((Ia<<1)+65534|Ha)+16&65535;b[m>>1]=f;m=Ia<<24|N-((Ha|2)<<Ia)}c[O+8>>2]=m;h=f<<16>>16==0;v=O+12|0;do if(x>>>0>=6){if(x>>>0<130){Ia=x+-2|0;g=((aa(Ia|0)|0)^31)+-1|0;g=(g<<1)+(Ia>>>g)+2&65535;break}if(x>>>0<2114){g=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)g=21;else g=x>>>0<22594?22:23}else g=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ia=j+-6|0;f=((aa(Ia|0)|0)^31)+-1|0;f=(f<<1)+(Ia>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=g&65535;t=m&7|j<<3&56;if(h&(g&65535)<8&(f&65535)<16)f=((f&65535)<8?t:t|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|t&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[oa>>2]|0)|0;f=f>>>0<la>>>0?f:la;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>18;Ia=n+(Ha<<1)|0;c[n+32768+((e[Ia>>1]&15|Ha<<4)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+1|0}m=y+(c[oa>>2]|0)|0;O=w;f=0}c[p>>2]=f+L;c[r>>2]=(c[r>>2]|0)+(M-q>>4);i=qa;return}function Na(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0;qa=i;i=i+32|0;oa=qa+16|0;pa=qa;ma=(1<<c[m+8>>2])+-16|0;v=c[p>>2]|0;na=h+g|0;la=g>>>0>3?na+-3|0:h;ka=m+4|0;ja=(c[ka>>2]|0)<9?64:512;u=ja+h|0;if((((h|0)==0^1|j^1)^1)&g>>>0<257){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;b[n+((_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>18<<1)>>1]=0;f=f+1|0}if(g){a[n+2129920>>0]=0;t=8}}else{f=n+2129920|0;if(!(a[f>>0]&1))t=8;else{ac(n|0,0,32768)|0;a[f>>0]=0;t=8}}if((t|0)==8?g>>>0>2&h>>>0>2:0){ga=h+-3|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>18;ia=n+(ha<<1)|0;c[n+32768+((e[ia>>1]&31|ha<<5)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16;ia=h+-2|0;ha=k+(ia&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>18;ga=n+(ha<<1)|0;c[n+32768+((e[ga>>1]&31|ha<<5)<<2)>>2]=ia;b[ga>>1]=(b[ga>>1]|0)+1<<16>>16;ga=h+-1|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>18;ia=n+(ha<<1)|0;c[n+32768+((e[ia>>1]&31|ha<<5)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16}P=oa+4|0;Q=oa+8|0;R=oa+12|0;S=oa+4|0;T=oa+8|0;U=oa+12|0;V=n+2129928|0;W=n+2129924|0;X=ja<<2;Y=na+-4|0;Z=na+-3|0;$=pa+12|0;ba=pa+4|0;ca=pa+8|0;da=pa+4|0;ea=pa+8|0;fa=pa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;O=q;f=v;a:while(1){M=O;N=u+X|0;b:while(1){L=na-m|0;if((m+4|0)>>>0>=na>>>0)break a;J=m>>>0<ma>>>0?m:ma;c[oa>>2]=0;c[P>>2]=0;c[Q>>2]=0;c[R>>2]=4240;K=m&l;g=c[oa>>2]|0;c[oa>>2]=0;c[S>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;y=4240;j=0;x=0;while(1){if((x|0)==4)break;w=(c[o+(c[11372+(x<<2)>>2]<<2)>>2]|0)+(c[11436+(x<<2)>>2]|0)|0;t=m-w|0;do if(((!(t>>>0>=m>>>0|w>>>0>J>>>0)?(ra=t&l,sa=K+g|0,sa>>>0<=l>>>0):0)?(ta=ra+g|0,ta>>>0<=l>>>0):0)?(a[k+sa>>0]|0)==(a[k+ta>>0]|0):0){v=0;t=F;while(1){h=t;if(h>>>0>H>>>0)break;D=t;E=k+(ra+v)|0;if((d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;v=v+4|0;t=h+4|0}while(1){if(t>>>0>=G>>>0)break;if((a[k+(ra+v)>>0]|0)!=(a[t>>0]|0))break;v=v+1|0;t=t+1|0}if(v>>>0<=2?!((v|0)==2&x>>>0<2):0){t=y;break}t=(v*540|0)+(c[11500+(x<<2)>>2]|0)|0;if(y>>>0<t>>>0){c[oa>>2]=v;c[T>>2]=w;c[U>>2]=t;g=v;j=1}else t=y}else t=y;while(0);y=t;x=x+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18;C=D<<5;D=n+(D<<1)|0;E=b[D>>1]|0;t=E&65535;E=(E&65535)>32?t+-32|0:0;c:while(1){B=K+g|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(t>>>0<=E>>>0)break c;t=t+-1|0;h=c[n+32768+((C|t&31)<<2)>>2]|0;z=m-h|0;if(z>>>0>J>>>0)break c;x=h&l;if(A)continue;h=x+g|0;if(h>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+h>>0]|0)){w=0;h=F}else continue;while(1){v=h;if(v>>>0>H>>>0)break;Ia=h;Ha=k+(x+w)|0;if((d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0)!=(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0))break;w=w+4|0;h=v+4|0}while(1){if(h>>>0>=G>>>0)break;if((a[k+(x+w)>>0]|0)!=(a[h>>0]|0))break;w=w+1|0;h=h+1|0}if(w>>>0<=3)continue;h=(w*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(y>>>0<h>>>0)break}c[oa>>2]=w;c[T>>2]=z;c[U>>2]=h;g=w;y=h;j=1}Ia=b[D>>1]|0;c[n+32768+((C|Ia&31)<<2)>>2]=m;b[D>>1]=Ia+1<<16>>16;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ia=b[21084+(w<<1)>>1]|0;t=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(ua=t&31,va=t>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=L>>>0):0){g=wa+ua|0;v=280811+g|0;g=280811+(g+-4)|0;h=0;t=280811+wa|0;while(1){if(t>>>0>g>>>0)break;Ia=k+(K+h)|0;if((d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;t=t+4|0}while(1){if(t>>>0>=v>>>0)break;if((a[k+(K+h)>>0]|0)!=(a[t>>0]|0))break;h=h+1|0;t=t+1|0}if(!((h+10|0)>>>0<=ua>>>0|(h|0)==0)?(xa=J+va+1+(d[407930+(ua-h)>>0]<<d[280786+ua>>0])|0,ya=(h*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[U>>2]|0)>>>0):0){c[oa>>2]=h;c[S>>2]=ua^h;c[T>>2]=xa;c[U>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){M=0;x=f;break}f=f+1|0;t=m+1|0;if(t>>>0<=u>>>0){m=t;continue}if(t>>>0>N>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>18;Ia=n+(Ha<<1)|0;c[n+32768+((e[Ia>>1]&31|Ha<<5)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>18;Ia=n+(Ha<<1)|0;c[n+32768+((e[Ia>>1]&31|Ha<<5)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+2|0;f=f+2|0}}}while(1){L=L+-1|0;if((c[ka>>2]|0)<5){f=(c[oa>>2]|0)+-1|0;f=f>>>0<L>>>0?f:L}else f=0;c[pa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;J=y>>>0<ma>>>0?y:ma;K=y&l;t=c[pa>>2]|0;c[pa>>2]=0;c[da>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;w=4240;f=0;v=0;while(1){if((v|0)==4)break;h=(c[o+(c[11372+(v<<2)>>2]<<2)>>2]|0)+(c[11436+(v<<2)>>2]|0)|0;j=y-h|0;do if(((!(j>>>0>=y>>>0|h>>>0>J>>>0)?(za=j&l,Aa=K+t|0,Aa>>>0<=l>>>0):0)?(Ba=za+t|0,Ba>>>0<=l>>>0):0)?(a[k+Aa>>0]|0)==(a[k+Ba>>0]|0):0){u=0;j=F;while(1){g=j;if(g>>>0>H>>>0)break;Ha=j;Ia=k+(za+u)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;u=u+4|0;j=g+4|0}while(1){if(j>>>0>=G>>>0)break;if((a[k+(za+u)>>0]|0)!=(a[j>>0]|0))break;u=u+1|0;j=j+1|0}if(u>>>0<=2?!((u|0)==2&v>>>0<2):0){j=w;break}j=(u*540|0)+(c[11500+(v<<2)>>2]|0)|0;if(w>>>0<j>>>0){c[pa>>2]=u;c[ea>>2]=h;c[fa>>2]=j;t=u;f=1}else j=w}else j=w;while(0);w=j;v=v+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18;C=D<<5;D=n+(D<<1)|0;E=b[D>>1]|0;j=E&65535;E=(E&65535)>32?j+-32|0:0;d:while(1){B=K+t|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(j>>>0<=E>>>0)break d;j=j+-1|0;g=c[n+32768+((C|j&31)<<2)>>2]|0;z=y-g|0;if(z>>>0>J>>>0)break d;v=g&l;if(A)continue;g=v+t|0;if(g>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+g>>0]|0)){h=0;g=F}else continue;while(1){u=g;if(u>>>0>H>>>0)break;Ha=g;Ia=k+(v+h)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;g=u+4|0}while(1){if(g>>>0>=G>>>0)break;if((a[k+(v+h)>>0]|0)!=(a[g>>0]|0))break;h=h+1|0;g=g+1|0}if(h>>>0<=3)continue;g=(h*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(w>>>0<g>>>0)break}c[pa>>2]=h;c[ea>>2]=z;c[fa>>2]=g;t=h;w=g;f=1}Ia=b[D>>1]|0;c[n+32768+((C|Ia&31)<<2)>>2]=y;b[D>>1]=Ia+1<<16>>16;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;h=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ia=b[21084+(h<<1)>>1]|0;j=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(Ca=j&31,Da=j>>>5,Ea=(c[11272+(Ca<<2)>>2]|0)+(_(Ca,Da)|0)|0,Ca>>>0<=L>>>0):0){t=Ea+Ca|0;u=280811+t|0;t=280811+(t+-4)|0;g=0;j=280811+Ea|0;while(1){if(j>>>0>t>>>0)break;Ia=k+(K+g)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;g=g+4|0;j=j+4|0}while(1){if(j>>>0>=u>>>0)break;if((a[k+(K+g)>>0]|0)!=(a[j>>0]|0))break;g=g+1|0;j=j+1|0}if(!((g+10|0)>>>0<=Ca>>>0|(g|0)==0)?(Fa=J+Da+1+(d[407930+(Ca-g)>>0]<<d[280786+Ca>>0])|0,Ga=(g*540|0)+3840+(_((aa(Fa|0)|0)^31,-120)|0)|0,Ga>>>0>=(c[fa>>2]|0)>>>0):0){c[pa>>2]=g;c[da>>2]=Ca^g;c[ea>>2]=Fa;c[fa>>2]=Ga;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;h=h+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[R>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[oa>>2]=c[pa>>2];c[oa+4>>2]=c[pa+4>>2];c[oa+8>>2]=c[pa+8>>2];c[oa+12>>2]=c[pa+12>>2];M=M+1|0;if(!((M|0)<4&(m+5|0)>>>0<na>>>0)){x=f;break}else{m=y;x=f}}m=c[oa>>2]|0;u=y+(m<<1)+ja|0;f=c[Q>>2]|0;e:do if(f>>>0<=(y>>>0<ma>>>0?y:ma)>>>0){g=f+3|0;Ia=c[o>>2]|0;j=g-Ia|0;t=c[ia>>2]|0;g=g-t|0;if((f|0)==(Ia|0))f=0;else{f:do if((f|0)!=(t|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(g>>>0<7){f=266017486>>>(g<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[Q>>2];m=c[oa>>2]|0}}else f=f+15|0;while(0);w=O+16|0;Ia=c[P>>2]|0;j=m^Ia;c[O>>2]=x;c[O+4>>2]=m|Ia<<24;m=O+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{N=f+-12|0;Ia=((aa(N|0)|0)^31)+-1|0;Ha=N>>>Ia&1;f=((Ia<<1)+65534|Ha)+16&65535;b[m>>1]=f;m=Ia<<24|N-((Ha|2)<<Ia)}c[O+8>>2]=m;h=f<<16>>16==0;v=O+12|0;do if(x>>>0>=6){if(x>>>0<130){Ia=x+-2|0;g=((aa(Ia|0)|0)^31)+-1|0;g=(g<<1)+(Ia>>>g)+2&65535;break}if(x>>>0<2114){g=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)g=21;else g=x>>>0<22594?22:23}else g=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ia=j+-6|0;f=((aa(Ia|0)|0)^31)+-1|0;f=(f<<1)+(Ia>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=g&65535;t=m&7|j<<3&56;if(h&(g&65535)<8&(f&65535)<16)f=((f&65535)<8?t:t|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|t&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[oa>>2]|0)|0;f=f>>>0<la>>>0?f:la;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>18;Ia=n+(Ha<<1)|0;c[n+32768+((e[Ia>>1]&31|Ha<<5)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+1|0}m=y+(c[oa>>2]|0)|0;O=w;f=0}c[p>>2]=f+L;c[r>>2]=(c[r>>2]|0)+(M-q>>4);i=qa;return}function Oa(f,g,h,j,k,l,m,n,o,p,q,r,s){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,$=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0;qa=i;i=i+32|0;oa=qa+16|0;pa=qa;ma=(1<<c[m+8>>2])+-16|0;v=c[p>>2]|0;na=h+g|0;la=g>>>0>3?na+-3|0:h;ka=m+4|0;ja=(c[ka>>2]|0)<9?64:512;u=ja+h|0;if((((h|0)==0^1|j^1)^1)&g>>>0<513){f=0;while(1){if((f|0)==(g|0))break;ia=k+f|0;b[n+((_(d[ia>>0]|d[ia+1>>0]<<8|d[ia+2>>0]<<16|d[ia+3>>0]<<24,506832829)|0)>>>17<<1)>>1]=0;f=f+1|0}if(g){a[n+8454144>>0]=0;t=8}}else{f=n+8454144|0;if(!(a[f>>0]&1))t=8;else{ac(n|0,0,65536)|0;a[f>>0]=0;t=8}}if((t|0)==8?g>>>0>2&h>>>0>2:0){ga=h+-3|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ia=n+(ha<<1)|0;c[n+65536+((e[ia>>1]&63|ha<<6)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16;ia=h+-2|0;ha=k+(ia&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ga=n+(ha<<1)|0;c[n+65536+((e[ga>>1]&63|ha<<6)<<2)>>2]=ia;b[ga>>1]=(b[ga>>1]|0)+1<<16>>16;ga=h+-1|0;ha=k+(ga&l)|0;ha=(_(d[ha>>0]|d[ha+1>>0]<<8|d[ha+2>>0]<<16|d[ha+3>>0]<<24,506832829)|0)>>>17;ia=n+(ha<<1)|0;c[n+65536+((e[ia>>1]&63|ha<<6)<<2)>>2]=ga;b[ia>>1]=(b[ia>>1]|0)+1<<16>>16}P=oa+4|0;Q=oa+8|0;R=oa+12|0;S=oa+4|0;T=oa+8|0;U=oa+12|0;V=n+8454152|0;W=n+8454148|0;X=ja<<2;Y=na+-4|0;Z=na+-3|0;$=pa+12|0;ba=pa+4|0;ca=pa+8|0;da=pa+4|0;ea=pa+8|0;fa=pa+12|0;ga=o+8|0;ha=o+12|0;ia=o+4|0;m=h;O=q;f=v;a:while(1){M=O;N=u+X|0;b:while(1){L=na-m|0;if((m+4|0)>>>0>=na>>>0)break a;J=m>>>0<ma>>>0?m:ma;c[oa>>2]=0;c[P>>2]=0;c[Q>>2]=0;c[R>>2]=4240;K=m&l;g=c[oa>>2]|0;c[oa>>2]=0;c[S>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;y=4240;j=0;x=0;while(1){if((x|0)==10)break;w=(c[o+(c[11372+(x<<2)>>2]<<2)>>2]|0)+(c[11436+(x<<2)>>2]|0)|0;t=m-w|0;do if(((!(t>>>0>=m>>>0|w>>>0>J>>>0)?(ra=t&l,sa=K+g|0,sa>>>0<=l>>>0):0)?(ta=ra+g|0,ta>>>0<=l>>>0):0)?(a[k+sa>>0]|0)==(a[k+ta>>0]|0):0){v=0;t=F;while(1){h=t;if(h>>>0>H>>>0)break;D=t;E=k+(ra+v)|0;if((d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;v=v+4|0;t=h+4|0}while(1){if(t>>>0>=G>>>0)break;if((a[k+(ra+v)>>0]|0)!=(a[t>>0]|0))break;v=v+1|0;t=t+1|0}if(v>>>0<=2?!((v|0)==2&x>>>0<2):0){t=y;break}t=(v*540|0)+(c[11500+(x<<2)>>2]|0)|0;if(y>>>0<t>>>0){c[oa>>2]=v;c[T>>2]=w;c[U>>2]=t;g=v;j=1}else t=y}else t=y;while(0);y=t;x=x+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;C=D<<6;D=n+(D<<1)|0;E=b[D>>1]|0;t=E&65535;E=(E&65535)>64?t+-64|0:0;c:while(1){B=K+g|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(t>>>0<=E>>>0)break c;t=t+-1|0;h=c[n+65536+((C|t&63)<<2)>>2]|0;z=m-h|0;if(z>>>0>J>>>0)break c;x=h&l;if(A)continue;h=x+g|0;if(h>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+h>>0]|0)){w=0;h=F}else continue;while(1){v=h;if(v>>>0>H>>>0)break;Ia=h;Ha=k+(x+w)|0;if((d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0)!=(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0))break;w=w+4|0;h=v+4|0}while(1){if(h>>>0>=G>>>0)break;if((a[k+(x+w)>>0]|0)!=(a[h>>0]|0))break;w=w+1|0;h=h+1|0}if(w>>>0<=3)continue;h=(w*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(y>>>0<h>>>0)break}c[oa>>2]=w;c[T>>2]=z;c[U>>2]=h;g=w;y=h;j=1}Ia=b[D>>1]|0;c[n+65536+((C|Ia&63)<<2)>>2]=m;b[D>>1]=Ia+1<<16>>16;if(!(j&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)j=0;else{x=0;j=0;w=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((x|0)==2)break;Ia=b[21084+(w<<1)>>1]|0;t=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(ua=t&31,va=t>>>5,wa=(c[11272+(ua<<2)>>2]|0)+(_(ua,va)|0)|0,ua>>>0<=L>>>0):0){g=wa+ua|0;v=280811+g|0;g=280811+(g+-4)|0;h=0;t=280811+wa|0;while(1){if(t>>>0>g>>>0)break;Ia=k+(K+h)|0;if((d[t>>0]|d[t+1>>0]<<8|d[t+2>>0]<<16|d[t+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;t=t+4|0}while(1){if(t>>>0>=v>>>0)break;if((a[k+(K+h)>>0]|0)!=(a[t>>0]|0))break;h=h+1|0;t=t+1|0}if(!((h+10|0)>>>0<=ua>>>0|(h|0)==0)?(xa=J+va+1+(d[407930+(ua-h)>>0]<<d[280786+ua>>0])|0,ya=(h*540|0)+3840+(_((aa(xa|0)|0)^31,-120)|0)|0,ya>>>0>=(c[U>>2]|0)>>>0):0){c[oa>>2]=h;c[S>>2]=ua^h;c[T>>2]=xa;c[U>>2]=ya;c[V>>2]=(c[V>>2]|0)+1;j=1}}x=x+1|0;w=w+1|0}j=(j&1)!=0}j=j&1}if(j&1){M=0;x=f;break}f=f+1|0;t=m+1|0;if(t>>>0<=u>>>0){m=t;continue}if(t>>>0>N>>>0){j=m+17|0;j=j>>>0<Y>>>0?j:Y;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&63|Ha<<6)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+4|0;f=f+4|0}}else{j=m+9|0;j=j>>>0<Z>>>0?j:Z;m=t;while(1){if(m>>>0>=j>>>0)continue b;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&63|Ha<<6)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+2|0;f=f+2|0}}}while(1){L=L+-1|0;if((c[ka>>2]|0)<5){f=(c[oa>>2]|0)+-1|0;f=f>>>0<L>>>0?f:L}else f=0;c[pa>>2]=f;c[ba>>2]=0;c[ca>>2]=0;c[$>>2]=4240;y=m+1|0;J=y>>>0<ma>>>0?y:ma;K=y&l;t=c[pa>>2]|0;c[pa>>2]=0;c[da>>2]=0;I=k+K|0;H=K+L|0;G=k+H|0;F=I;H=k+(H+-4)|0;w=4240;f=0;v=0;while(1){if((v|0)==10)break;h=(c[o+(c[11372+(v<<2)>>2]<<2)>>2]|0)+(c[11436+(v<<2)>>2]|0)|0;j=y-h|0;do if(((!(j>>>0>=y>>>0|h>>>0>J>>>0)?(za=j&l,Aa=K+t|0,Aa>>>0<=l>>>0):0)?(Ba=za+t|0,Ba>>>0<=l>>>0):0)?(a[k+Aa>>0]|0)==(a[k+Ba>>0]|0):0){u=0;j=F;while(1){g=j;if(g>>>0>H>>>0)break;Ha=j;Ia=k+(za+u)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;u=u+4|0;j=g+4|0}while(1){if(j>>>0>=G>>>0)break;if((a[k+(za+u)>>0]|0)!=(a[j>>0]|0))break;u=u+1|0;j=j+1|0}if(u>>>0<=2?!((u|0)==2&v>>>0<2):0){j=w;break}j=(u*540|0)+(c[11500+(v<<2)>>2]|0)|0;if(w>>>0<j>>>0){c[pa>>2]=u;c[ea>>2]=h;c[fa>>2]=j;t=u;f=1}else j=w}else j=w;while(0);w=j;v=v+1|0}D=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>17;C=D<<6;D=n+(D<<1)|0;E=b[D>>1]|0;j=E&65535;E=(E&65535)>64?j+-64|0:0;d:while(1){B=K+t|0;A=B>>>0>l>>>0;B=k+B|0;while(1){if(j>>>0<=E>>>0)break d;j=j+-1|0;g=c[n+65536+((C|j&63)<<2)>>2]|0;z=y-g|0;if(z>>>0>J>>>0)break d;v=g&l;if(A)continue;g=v+t|0;if(g>>>0>l>>>0)continue;if((a[B>>0]|0)==(a[k+g>>0]|0)){h=0;g=F}else continue;while(1){u=g;if(u>>>0>H>>>0)break;Ha=g;Ia=k+(v+h)|0;if((d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;h=h+4|0;g=u+4|0}while(1){if(g>>>0>=G>>>0)break;if((a[k+(v+h)>>0]|0)!=(a[g>>0]|0))break;h=h+1|0;g=g+1|0}if(h>>>0<=3)continue;g=(h*540|0)+3840+(_((aa(z|0)|0)^31,-120)|0)|0;if(w>>>0<g>>>0)break}c[pa>>2]=h;c[ea>>2]=z;c[fa>>2]=g;t=h;w=g;f=1}Ia=b[D>>1]|0;c[n+65536+((C|Ia&63)<<2)>>2]=y;b[D>>1]=Ia+1<<16>>16;if(!(f&1)){if((c[V>>2]|0)>>>0<(c[W>>2]|0)>>>7>>>0)f=0;else{v=0;f=0;h=(_(d[I>>0]|d[I+1>>0]<<8|d[I+2>>0]<<16|d[I+3>>0]<<24,506832829)|0)>>>18<<1;while(1){if((v|0)==2)break;Ia=b[21084+(h<<1)>>1]|0;j=Ia&65535;c[W>>2]=(c[W>>2]|0)+1;if(Ia<<16>>16!=0?(Ca=j&31,Da=j>>>5,Ea=(c[11272+(Ca<<2)>>2]|0)+(_(Ca,Da)|0)|0,Ca>>>0<=L>>>0):0){t=Ea+Ca|0;u=280811+t|0;t=280811+(t+-4)|0;g=0;j=280811+Ea|0;while(1){if(j>>>0>t>>>0)break;Ia=k+(K+g)|0;if((d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)!=(d[Ia>>0]|d[Ia+1>>0]<<8|d[Ia+2>>0]<<16|d[Ia+3>>0]<<24|0))break;g=g+4|0;j=j+4|0}while(1){if(j>>>0>=u>>>0)break;if((a[k+(K+g)>>0]|0)!=(a[j>>0]|0))break;g=g+1|0;j=j+1|0}if(!((g+10|0)>>>0<=Ca>>>0|(g|0)==0)?(Fa=J+Da+1+(d[407930+(Ca-g)>>0]<<d[280786+Ca>>0])|0,Ga=(g*540|0)+3840+(_((aa(Fa|0)|0)^31,-120)|0)|0,Ga>>>0>=(c[fa>>2]|0)>>>0):0){c[pa>>2]=g;c[da>>2]=Ca^g;c[ea>>2]=Fa;c[fa>>2]=Ga;c[V>>2]=(c[V>>2]|0)+1;f=1}}v=v+1|0;h=h+1|0}f=(f&1)!=0}f=f&1}if(!(f&1)){y=m;break}if((c[$>>2]|0)>>>0<((c[R>>2]|0)+700|0)>>>0){y=m;break}f=x+1|0;c[oa>>2]=c[pa>>2];c[oa+4>>2]=c[pa+4>>2];c[oa+8>>2]=c[pa+8>>2];c[oa+12>>2]=c[pa+12>>2];M=M+1|0;if(!((M|0)<4&(m+5|0)>>>0<na>>>0)){x=f;break}else{m=y;x=f}}m=c[oa>>2]|0;u=y+(m<<1)+ja|0;f=c[Q>>2]|0;e:do if(f>>>0<=(y>>>0<ma>>>0?y:ma)>>>0){g=f+3|0;Ia=c[o>>2]|0;j=g-Ia|0;t=c[ia>>2]|0;g=g-t|0;if((f|0)==(Ia|0))f=0;else{f:do if((f|0)!=(t|0)){do if(j>>>0<7)f=158663784>>>(j<<2)&15;else{if(g>>>0<7){f=266017486>>>(g<<2)&15;break}if((f|0)==(c[ga>>2]|0)){f=2;break f}if((f|0)==(c[ha>>2]|0)){f=3;break f}f=f+15|0}while(0);if(!f)break e}else f=1;while(0);c[ha>>2]=c[ga>>2];c[ga>>2]=c[ia>>2];c[ia>>2]=c[o>>2];c[o>>2]=c[Q>>2];m=c[oa>>2]|0}}else f=f+15|0;while(0);w=O+16|0;Ia=c[P>>2]|0;j=m^Ia;c[O>>2]=x;c[O+4>>2]=m|Ia<<24;m=O+14|0;if(f>>>0<16){f=f&65535;b[m>>1]=f;m=0}else{N=f+-12|0;Ia=((aa(N|0)|0)^31)+-1|0;Ha=N>>>Ia&1;f=((Ia<<1)+65534|Ha)+16&65535;b[m>>1]=f;m=Ia<<24|N-((Ha|2)<<Ia)}c[O+8>>2]=m;h=f<<16>>16==0;v=O+12|0;do if(x>>>0>=6){if(x>>>0<130){Ia=x+-2|0;g=((aa(Ia|0)|0)^31)+-1|0;g=(g<<1)+(Ia>>>g)+2&65535;break}if(x>>>0<2114){g=((aa(x+-66|0)|0)^31)+10&65535;break}if(x>>>0<6210)g=21;else g=x>>>0<22594?22:23}else g=x&65535;while(0);do if(j>>>0>=10){if(j>>>0<134){Ia=j+-6|0;f=((aa(Ia|0)|0)^31)+-1|0;f=(f<<1)+(Ia>>>f)+4&65535;break}if(j>>>0<2118)f=((aa(j+-70|0)|0)^31)+12&65535;else f=23}else f=j+65534&65535;while(0);m=f&65535;j=g&65535;t=m&7|j<<3&56;if(h&(g&65535)<8&(f&65535)<16)f=((f&65535)<8?t:t|64)&65535;else f=b[88156+((m>>>3)+((j>>>3)*3|0)<<1)>>1]|t&65535;b[v>>1]=f;c[s>>2]=(c[s>>2]|0)+x;f=y+(c[oa>>2]|0)|0;f=f>>>0<la>>>0?f:la;m=y+2|0;while(1){if(m>>>0>=f>>>0)break;Ha=k+(m&l)|0;Ha=(_(d[Ha>>0]|d[Ha+1>>0]<<8|d[Ha+2>>0]<<16|d[Ha+3>>0]<<24,506832829)|0)>>>17;Ia=n+(Ha<<1)|0;c[n+65536+((e[Ia>>1]&63|Ha<<6)<<2)>>2]=m;b[Ia>>1]=(b[Ia>>1]|0)+1<<16>>16;m=m+1|0}m=y+(c[oa>>2]|0)|0;O=w;f=0}c[p>>2]=f+L;c[r>>2]=(c[r>>2]|0)+(M-q>>4);i=qa;return}
function Db(f,j,k,l,m,n,o,p,q,r,t,u,v,w,x,y,z){f=f|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;t=t|0;u=u|0;v=v|0;w=w|0;x=x|0;y=y|0;z=z|0;var A=0,B=0,D=0,E=0,F=0.0,G=0.0,H=0,I=0,J=0,K=0,L=0,M=0.0,N=0,O=0,P=0,Q=0,R=0.0,S=0.0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0.0,$=0,ba=0.0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,qa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0;Ga=i;i=i+12768|0;Ca=Ga+7760|0;ya=Ga+4928|0;za=Ga+2096|0;O=Ga+12504|0;N=Ga+10968|0;B=Ga;E=Ga+12248|0;D=Ga+10456|0;J=Ga+11544|0;I=Ga+9048|0;L=Ga+11480|0;K=Ga+8920|0;Ba=Ga+8808|0;Q=Ga+8804|0;P=Ga+8800|0;if(m>>>0>0|(m|0)==0&l>>>0>3221225471){Fa=bc(l|0,m|0,30)|0;Fa=l&1073741823|((Fa&1^1)<<30)+1073741824}else Fa=l;if(!n){k=c[y>>2]|0;z=z+(k>>>3)|0;u=d[z>>0]|0;k=cc(3,0,k&7|0)|0;w=C;k=u|k;u=z;a[u>>0]=k;a[u+1>>0]=k>>8;a[u+2>>0]=k>>16;a[u+3>>0]=k>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;c[y>>2]=(c[y>>2]|0)+9&-8;i=Ga;return}if(!(Fb(j,k,l,m,n,t,u)|0)){c[x>>2]=c[w>>2];c[x+4>>2]=c[w+4>>2];c[x+8>>2]=c[w+8>>2];c[x+12>>2]=c[w+12>>2];bb(o,j,Fa,k,n,y,z);i=Ga;return}Da=a[z>>0]|0;Ea=c[y>>2]|0;Aa=p+4|0;l=c[Aa>>2]|0;if((l|0)>9)if((c[p>>2]|0)==2){A=0;while(1){if((A|0)==(u|0))break;do if((c[v+(A<<4)+4>>2]&16777215|0)!=0?(e[v+(A<<4)+12>>1]|0)>127:0){t=v+(A<<4)+14|0;l=b[t>>1]|0;if((l&65535)>=16){m=v+(A<<4)+8|0;xa=c[m>>2]|0;wa=xa>>>24;l=((l&65535)+-12-(wa<<1)<<wa)+(xa&16777215)+12|0;if(l>>>0>=28){va=l+-20|0;ua=(aa(va|0)|0)^31;xa=ua+-1|0;wa=va>>>xa&1;ua=ua+-2|0;b[t>>1]=(((ua<<1)+32766|wa)<<1)+28|va&1;c[m>>2]=ua<<24|(va-((wa|2)<<xa)|0)>>>1;break}}else{m=v+(A<<4)+8|0;l=l&65535}b[t>>1]=l;c[m>>2]=0}while(0);A=A+1|0}l=c[Aa>>2]|0;m=1;t=12;H=19}else{xa=0;wa=0;H=38}else{m=0;t=0;H=19}do if((H|0)==19){if((l|0)>=3){if((l|0)>=4){xa=m;wa=t;H=38;break}db(o,n,y,z);ac(Ca|0,0,1028)|0;h[Ca+1032>>3]=s;ac(ya|0,0,2820)|0;h[ya+2824>>3]=s;ac(za|0,0,2084)|0;h[za+2088>>3]=s;jb(j,Fa,k,v,u,Ca,ya,za);l=z+((c[y>>2]|0)>>>3)|0;r=d[l>>0]|0;m=l;a[m>>0]=r;a[m+1>>0]=r>>8;a[m+2>>0]=r>>16;a[m+3>>0]=r>>24;l=l+4|0;a[l>>0]=0;a[l+1>>0]=0;a[l+2>>0]=0;a[l+3>>0]=0;c[y>>2]=(c[y>>2]|0)+13;l=f+8|0;m=sa[c[f>>2]&1](c[l>>2]|0,11272)|0;if(!m)oa(1);kb(Ca,256,m,O,N,y,z);kb(ya,704,m,J,I,y,z);kb(za,64,m,L,K,y,z);ra[c[f+4>>2]&1](c[l>>2]|0,m);lb(j,Fa,k,v,u,O,N,J,I,L,K,y,z);if(o){u=(c[y>>2]|0)+7|0;c[y>>2]=u&-8;a[z+(u>>>3)>>0]=0}break}db(o,n,y,z);r=z+((c[y>>2]|0)>>>3)|0;Ba=d[r>>0]|0;q=r;a[q>>0]=Ba;a[q+1>>0]=Ba>>8;a[q+2>>0]=Ba>>16;a[q+3>>0]=Ba>>24;r=r+4|0;a[r>>0]=0;a[r+1>>0]=0;a[r+2>>0]=0;a[r+3>>0]=0;c[y>>2]=(c[y>>2]|0)+13;if(u>>>0<129){ac(Ca|0,0,1024)|0;t=0;A=Fa;D=0;while(1){if((D|0)==(u|0))break;l=c[v+(D<<4)>>2]|0;m=c[v+(D<<4)+4>>2]|0;B=A;E=l;while(1){if(!E)break;r=Ca+((d[j+(B&k)>>0]|0)<<2)|0;c[r>>2]=(c[r>>2]|0)+1;B=B+1|0;E=E+-1|0}t=t+l|0;A=A+l+(m&16777215)|0;D=D+1|0}$a(f,Ca,t,8,O,N,y,z);Ca=c[y>>2]|0;q=z+(Ca>>>3)|0;f=d[q>>0]|0;Ca=cc(372273155,9593412,Ca&7|0)|0;r=C;Ca=f|Ca;f=q;a[f>>0]=Ca;a[f+1>>0]=Ca>>8;a[f+2>>0]=Ca>>16;a[f+3>>0]=Ca>>24;q=q+4|0;a[q>>0]=r;a[q+1>>0]=r>>8;a[q+2>>0]=r>>16;a[q+3>>0]=r>>24;q=(c[y>>2]|0)+56|0;c[y>>2]=q;q=z+(q>>>3)|0;r=d[q>>0]|0;f=q;a[f>>0]=r;a[f+1>>0]=r>>8;a[f+2>>0]=r>>16;a[f+3>>0]=r>>24;q=q+4|0;a[q>>0]=0;a[q+1>>0]=0;a[q+2>>0]=0;a[q+3>>0]=0;q=(c[y>>2]|0)+3|0;c[y>>2]=q;f=z+(q>>>3)|0;r=d[f>>0]|0;q=cc(57269251,0,q&7|0)|0;Ca=C;q=r|q;r=f;a[r>>0]=q;a[r+1>>0]=q>>8;a[r+2>>0]=q>>16;a[r+3>>0]=q>>24;f=f+4|0;a[f>>0]=Ca;a[f+1>>0]=Ca>>8;a[f+2>>0]=Ca>>16;a[f+3>>0]=Ca>>24;c[y>>2]=(c[y>>2]|0)+28;lb(j,Fa,k,v,u,O,N,404412,86620,405116,88028,y,z)}else{ac(ya|0,0,1028)|0;h[ya+1032>>3]=s;ac(za|0,0,2820)|0;h[za+2824>>3]=s;ac(B|0,0,2084)|0;h[B+2088>>3]=s;jb(j,Fa,k,v,u,ya,za,B);$a(f,ya,c[ya+1024>>2]|0,8,E,D,y,z);$a(f,za,c[za+2816>>2]|0,10,J,I,y,z);$a(f,B,c[B+2080>>2]|0,6,L,K,y,z);lb(j,Fa,k,v,u,E,D,J,I,L,K,y,z)}if(o){u=(c[y>>2]|0)+7|0;c[y>>2]=u&-8;a[z+(u>>>3)>>0]=0}}while(0);if((H|0)==38){ha=Ba+8|0;ia=Ba+12|0;K=Ba+24|0;ga=Ba+28|0;c[Ba>>2]=0;c[Ba+4>>2]=0;c[Ba+8>>2]=0;c[Ba+12>>2]=0;c[Ba+16>>2]=0;c[Ba+20>>2]=0;c[Ba+24>>2]=0;ja=Ba+32|0;ka=Ba+36|0;L=Ba+48|0;c[ga>>2]=0;c[ga+4>>2]=0;c[ga+8>>2]=0;c[ga+12>>2]=0;c[ga+16>>2]=0;c[L>>2]=0;ga=Ba+52|0;la=Ba+56|0;ma=Ba+60|0;na=Ba+72|0;c[ga>>2]=0;c[ga+4>>2]=0;c[ga+8>>2]=0;c[ga+12>>2]=0;c[ga+16>>2]=0;ga=Ba+76|0;qa=Ba+80|0;ta=Ba+88|0;J=Ba+92|0;ua=Ba+96|0;N=Ba+100|0;va=Ba+104|0;O=Ba+108|0;m=(l|0)<10;t=na;A=t+40|0;do{c[t>>2]=0;t=t+4|0}while((t|0)<(A|0));do if(m){c[Q>>2]=1;c[P>>2]=0;if(!((l|0)<5|n>>>0<64)){D=Fa+n|0;t=za;A=t+36|0;do{c[t>>2]=0;t=t+4|0}while((t|0)<(A|0));B=Fa;while(1){A=B+64|0;if(A>>>0>D>>>0)break;t=d[j+(B&k)>>0]|0;m=B;while(1){m=m+1|0;if(m>>>0>=A>>>0)break;fa=d[j+(m&k)>>0]|0;ea=za+(((c[18900+(t>>>6<<2)>>2]|0)*3|0)+(c[18900+(fa>>>6<<2)>>2]|0)<<2)|0;c[ea>>2]=(c[ea>>2]|0)+1;t=fa}B=B+4096|0}c[Ca>>2]=0;c[Ca+4>>2]=0;c[Ca+8>>2]=0;c[ya>>2]=0;c[ya+4>>2]=0;c[ya+8>>2]=0;c[ya+12>>2]=0;c[ya+16>>2]=0;c[ya+20>>2]=0;m=0;I=0;while(1){if((m|0)==9)break;fa=c[za+(m<<2)>>2]|0;ea=Ca+(((m>>>0)%3|0)<<2)|0;c[ea>>2]=(c[ea>>2]|0)+fa;ea=ya+((m>>>0>5?m+-6|0:m)<<2)|0;c[ea>>2]=(c[ea>>2]|0)+fa;m=m+1|0;I=I+fa|0}D=Ca+12|0;B=Ca;M=0.0;m=0;while(1){A=B+4|0;t=c[B>>2]|0;E=m+t|0;G=+(t>>>0);if(t>>>0<256)F=+g[19516+(t<<2)>>2];else F=+Xb(G);M=M-G*F;if(A>>>0>=D>>>0)break;m=c[A>>2]|0;F=+(m>>>0);if(m>>>0<256)G=+g[19516+(m<<2)>>2];else G=+Xb(F);B=B+8|0;M=M-F*G;m=E+m|0}if(!E)ba=M;else{G=+(E>>>0);if(E>>>0<256)F=+g[19516+(E<<2)>>2];else F=+Xb(G);ba=M+G*F}H=ya+12|0;B=ya;M=0.0;m=0;while(1){A=B+4|0;t=c[B>>2]|0;D=m+t|0;G=+(t>>>0);if(t>>>0<256)F=+g[19516+(t<<2)>>2];else F=+Xb(G);M=M-G*F;if(A>>>0>=H>>>0)break;m=c[A>>2]|0;F=+(m>>>0);if(m>>>0<256)G=+g[19516+(m<<2)>>2];else G=+Xb(F);B=B+8|0;M=M-F*G;m=D+m|0}if(!D)Z=M;else{G=+(D>>>0);if(D>>>0<256)F=+g[19516+(D<<2)>>2];else F=+Xb(G);Z=M+G*F}E=ya+24|0;B=H;M=0.0;m=0;while(1){A=B+4|0;t=c[B>>2]|0;D=m+t|0;G=+(t>>>0);if(t>>>0<256)F=+g[19516+(t<<2)>>2];else F=+Xb(G);M=M-G*F;if(A>>>0>=E>>>0)break;m=c[A>>2]|0;F=+(m>>>0);if(m>>>0<256)G=+g[19516+(m<<2)>>2];else G=+Xb(F);B=B+8|0;M=M-F*G;m=D+m|0}if(!D)R=M;else{G=+(D>>>0);if(D>>>0<256)F=+g[19516+(D<<2)>>2];else F=+Xb(G);R=M+G*F}S=0.0;E=0;while(1){if((E|0)==3)break;t=E*3|0;A=za+(t+3<<2)|0;t=za+(t<<2)|0;M=0.0;m=0;while(1){B=t+4|0;t=c[t>>2]|0;D=m+t|0;G=+(t>>>0);if(t>>>0<256)F=+g[19516+(t<<2)>>2];else F=+Xb(G);F=M-G*F;if(B>>>0>=A>>>0)break;m=c[B>>2]|0;G=+(m>>>0);if(m>>>0<256)M=+g[19516+(m<<2)>>2];else M=+Xb(G);t=B+4|0;M=F-G*M;m=D+m|0}if(D){M=+(D>>>0);if(D>>>0<256)G=+g[19516+(D<<2)>>2];else G=+Xb(M);F=F+M*G}S=S+F;E=E+1|0}if(!I)pa(406445,406053,339,406456);F=1.0/+(I>>>0);ba=ba*F;G=(Z+R)*F;F=(l|0)<7?ba*10.0:S*F;do if(!(ba-G<.2&ba-F<.2))if(G-F<.02){c[Q>>2]=2;c[P>>2]=18916;break}else{c[Q>>2]=3;c[P>>2]=19172;break}else c[Q>>2]=1;while(0);fa=c[P>>2]|0;W=fa;if(fa){fa=c[Q>>2]|0;l=0;m=0;while(1){if((m|0)==(u|0))break;l=l+(c[v+(m<<4)>>2]|0)|0;m=m+1|0}E=(l>>>9)+1|0;c[Ca>>2]=256;c[Ca+4>>2]=fa;H=256/(fa>>>0)|0;c[Ca+8>>2]=H;c[Ca+12>>2]=512;h[Ca+16>>3]=400.0;c[Ca+24>>2]=0;c[Ca+28>>2]=Ba;c[Ca+36>>2]=J;ca=Ca+40|0;c[ca>>2]=512;da=Ca+44|0;c[da>>2]=0;ea=Ca+48|0;c[ea>>2]=0;c[Ca+64>>2]=0;H=H+1|0;H=E>>>0<H>>>0?E:H;D=Ba+16|0;l=c[D>>2]|0;if(l>>>0<E>>>0){B=(l|0)==0?E:l;while(1){if(B>>>0>=E>>>0)break;B=B<<1}m=f+8|0;t=sa[c[f>>2]&1](c[m>>2]|0,B)|0;if(!t)oa(1);A=c[D>>2]|0;l=Ba+8|0;if(A)dc(t|0,c[l>>2]|0,A|0)|0;ra[c[f+4>>2]&1](c[m>>2]|0,c[l>>2]|0);c[l>>2]=t;c[D>>2]=B}B=Ba+20|0;l=c[B>>2]|0;if(l>>>0<E>>>0){A=(l|0)==0?E:l;while(1){if(A>>>0>=E>>>0)break;A=A<<1}D=f+8|0;m=sa[c[f>>2]&1](c[D>>2]|0,A<<2)|0;if(!m)oa(1);t=c[B>>2]|0;l=Ba+12|0;if(t)dc(m|0,c[l>>2]|0,t<<2|0)|0;ra[c[f+4>>2]&1](c[D>>2]|0,c[l>>2]|0);c[l>>2]=m;c[B>>2]=A;$=f}else{$=f;D=f+8|0}c[Ba+4>>2]=E;l=sa[c[$>>2]&1](c[D>>2]|0,fa<<4)|0;if(!l)oa(1);Y=Ca+60|0;c[Y>>2]=l;if(c[ta>>2]|0)pa(407776,406802,255,407793);m=_(H,fa)|0;c[J>>2]=m;m=sa[c[$>>2]&1](c[D>>2]|0,m*1040|0)|0;l=m;if(!m)oa(1);c[ta>>2]=l;X=Ca+32|0;c[X>>2]=l;l=0;while(1){if((l|0)==(fa|0))break;ac(m+(l*1040|0)|0,0,1024)|0;c[m+(l*1040|0)+1024>>2]=0;h[m+(l*1040|0)+1032>>3]=s;l=l+1|0}c[Ca+56>>2]=0;c[Ca+52>>2]=0;Pb(f,ya,u,K,ua,N);Qb(f,za,u,L,va,O);Q=ya+40|0;T=ya+24|0;U=ya+36|0;V=ya+32|0;L=za+40|0;N=za+24|0;O=za+36|0;p=za+32|0;t=Fa;m=q;l=r;P=0;while(1){if((P|0)==(u|0))break;H=c[v+(P<<4)>>2]|0;I=c[v+(P<<4)+4>>2]|0;A=c[v+(P<<4)+12>>2]|0;J=A&65535;K=A>>>16;E=c[Q>>2]|0;B=c[T>>2]|0;A=B+(E*2832|0)+((A&65535)<<2)|0;c[A>>2]=(c[A>>2]|0)+1;E=B+(E*2832|0)+2816|0;c[E>>2]=(c[E>>2]|0)+1;E=(c[U>>2]|0)+1|0;c[U>>2]=E;if((E|0)==(c[V>>2]|0)){Sb(ya,0);E=t;B=H}else{E=t;B=H}while(1){if(!B)break;A=a[j+(E&k)>>0]|0;l=(c[ea>>2]|0)+(c[W+(((a[406969+(m&255)>>0]|a[406969+(l&255|256)>>0])&255)<<2)>>2]|0)|0;Ha=c[X>>2]|0;Ia=Ha+(l*1040|0)+((A&255)<<2)|0;c[Ia>>2]=(c[Ia>>2]|0)+1;l=Ha+(l*1040|0)+1024|0;c[l>>2]=(c[l>>2]|0)+1;l=(c[da>>2]|0)+1|0;c[da>>2]=l;if((l|0)==(c[ca>>2]|0))Ub(f,Ca,0);l=m;E=E+1|0;m=A;B=B+-1|0}Ia=I&16777215;t=t+H+Ia|0;do if(Ia){l=a[j+(t+-2&k)>>0]|0;m=a[j+(t+-1&k)>>0]|0;if((J&65535)<=127)break;Ia=c[L>>2]|0;Ha=c[N>>2]|0;K=Ha+(Ia*2096|0)+(K<<2)|0;c[K>>2]=(c[K>>2]|0)+1;Ia=Ha+(Ia*2096|0)+2080|0;c[Ia>>2]=(c[Ia>>2]|0)+1;Ia=(c[O>>2]|0)+1|0;c[O>>2]=Ia;if((Ia|0)!=(c[p>>2]|0))break;Tb(za,0)}while(0);P=P+1|0}Ub(f,Ca,1);ra[c[f+4>>2]&1](c[D>>2]|0,c[Y>>2]|0);c[Y>>2]=0;Sb(ya,1);Tb(za,1);if(c[na>>2]|0)pa(406853,406802,473,407737);l=c[Ba>>2]|0;c[ga>>2]=l<<6;l=sa[c[$>>2]&1](c[D>>2]|0,l<<8)|0;if(!l)oa(1);c[na>>2]=l;t=0;while(1){if(t>>>0>=(c[Ba>>2]|0)>>>0)break;l=_(t,fa)|0;m=t<<6;A=0;while(1){if((A|0)==64)break;c[(c[na>>2]|0)+(m+A<<2)>>2]=l+(c[W+(A<<2)>>2]|0);A=A+1|0}t=t+1|0}l=2;break}}l=0;m=0;while(1){if((m|0)==(u|0))break;l=l+(c[v+(m<<4)>>2]|0)|0;m=m+1|0}H=(l>>>9)+1|0;E=H>>>0<257?H:257;c[Ca>>2]=256;c[Ca+4>>2]=512;h[Ca+8>>3]=400.0;c[Ca+16>>2]=0;I=Ca+20|0;c[I>>2]=Ba;c[Ca+28>>2]=J;T=Ca+32|0;c[T>>2]=512;U=Ca+36|0;c[U>>2]=0;V=Ca+40|0;c[V>>2]=0;c[Ca+72>>2]=0;D=Ba+16|0;l=c[D>>2]|0;if(l>>>0<H>>>0){B=(l|0)==0?H:l;while(1){if(B>>>0>=H>>>0)break;B=B<<1}m=f+8|0;t=sa[c[f>>2]&1](c[m>>2]|0,B)|0;if(!t)oa(1);A=c[D>>2]|0;l=Ba+8|0;if(A)dc(t|0,c[l>>2]|0,A|0)|0;ra[c[f+4>>2]&1](c[m>>2]|0,c[l>>2]|0);c[l>>2]=t;c[D>>2]=B}D=Ba+20|0;l=c[D>>2]|0;if(l>>>0<H>>>0){B=(l|0)==0?H:l;while(1){if(B>>>0>=H>>>0)break;B=B<<1}m=f+8|0;t=sa[c[f>>2]&1](c[m>>2]|0,B<<2)|0;if(!t)oa(1);A=c[D>>2]|0;l=Ba+12|0;if(A)dc(t|0,c[l>>2]|0,A<<2|0)|0;ra[c[f+4>>2]&1](c[m>>2]|0,c[l>>2]|0);c[l>>2]=t;c[D>>2]=B}c[(c[I>>2]|0)+4>>2]=H;if(c[ta>>2]|0)pa(407776,407818,70,407905);c[J>>2]=E;l=sa[c[f>>2]&1](c[f+8>>2]|0,E*1040|0)|0;m=l;if(!l)oa(1);c[ta>>2]=m;Q=Ca+24|0;c[Q>>2]=m;ac(l|0,0,1024)|0;c[l+1024>>2]=0;h[l+1032>>3]=s;c[Ca+48>>2]=0;c[Ca+44>>2]=0;Pb(f,ya,u,K,ua,N);Qb(f,za,u,L,va,O);D=ya+40|0;E=ya+24|0;H=ya+36|0;I=ya+32|0;J=za+40|0;K=za+24|0;L=za+36|0;N=za+32|0;l=Fa;P=0;while(1){if((P|0)==(u|0))break;t=c[v+(P<<4)>>2]|0;A=c[v+(P<<4)+4>>2]|0;ga=c[v+(P<<4)+12>>2]|0;O=ga&65535;p=ga>>>16;Ia=c[D>>2]|0;Ha=c[E>>2]|0;ga=Ha+(Ia*2832|0)+((ga&65535)<<2)|0;c[ga>>2]=(c[ga>>2]|0)+1;Ia=Ha+(Ia*2832|0)+2816|0;c[Ia>>2]=(c[Ia>>2]|0)+1;Ia=(c[H>>2]|0)+1|0;c[H>>2]=Ia;if((Ia|0)==(c[I>>2]|0)){Sb(ya,0);m=l;B=t}else{m=l;B=t}while(1){if(!B)break;Ia=c[V>>2]|0;Ha=c[Q>>2]|0;ga=Ha+(Ia*1040|0)+((d[j+(m&k)>>0]|0)<<2)|0;c[ga>>2]=(c[ga>>2]|0)+1;Ia=Ha+(Ia*1040|0)+1024|0;c[Ia>>2]=(c[Ia>>2]|0)+1;Ia=(c[U>>2]|0)+1|0;c[U>>2]=Ia;if((Ia|0)==(c[T>>2]|0))Rb(Ca,0);m=m+1|0;B=B+-1|0}Ia=A&16777215;l=l+t+Ia|0;if((Ia|0)!=0&(O&65535)>127?(Ia=c[J>>2]|0,Ha=c[K>>2]|0,ga=Ha+(Ia*2096|0)+(p<<2)|0,c[ga>>2]=(c[ga>>2]|0)+1,Ia=Ha+(Ia*2096|0)+2080|0,c[Ia>>2]=(c[Ia>>2]|0)+1,Ia=(c[L>>2]|0)+1|0,c[L>>2]=Ia,(Ia|0)==(c[N>>2]|0)):0)Tb(za,0);P=P+1|0}Rb(Ca,1);Sb(ya,1);Tb(za,1);l=2}else{l=Wb(j,Fa,k,n)|0;l=l?2:3;Nb(f,j,Fa,k,p,q,r,v,u,l,Ba)}while(0);if((c[Aa>>2]|0)>3)Ob(wa,xa,Ba);ab(f,j,Fa,n,k,q,r,o,wa,xa,l,v,u,Ba,y,z);Ha=f+4|0;Ia=f+8|0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[Ba+8>>2]|0);c[ha>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[ia>>2]|0);c[ia>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[Ba+32>>2]|0);c[ja>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[ka>>2]|0);c[ka>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[Ba+56>>2]|0);c[la>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[ma>>2]|0);c[ma>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[na>>2]|0);c[na>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[qa>>2]|0);c[qa>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[ta>>2]|0);c[ta>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[ua>>2]|0);c[ua>>2]=0;ra[c[Ha>>2]&1](c[Ia>>2]|0,c[va>>2]|0);c[va>>2]=0}if((n+4|0)>>>0>=(c[y>>2]|0)>>>3>>>0){i=Ga;return};c[x>>2]=c[w>>2];c[x+4>>2]=c[w+4>>2];c[x+8>>2]=c[w+8>>2];c[x+12>>2]=c[w+12>>2];a[z>>0]=Da;c[y>>2]=Ea&255;bb(o,j,Fa,k,n,y,z);i=Ga;return}function Eb(b){b=b|0;var e=0,f=0,g=0,h=0,i=0;f=b+192|0;g=d[f>>0]|0;i=b+193|0;h=d[i>>0]|0;a[f>>0]=0;a[i>>0]=0;i=6<<h;f=b+5216|0;e=c[f>>2]|0;if(!e){e=b+5228|0;c[f>>2]=e}else e=e+(c[b+5220>>2]|0)|0;a[e>>0]=g|i;if((h+6|0)>>>0>8)a[e+1>>0]=i>>>8;b=b+5220|0;c[b>>2]=(c[b>>2]|0)+((h+13|0)>>>3);return}function Fb(a,b,e,f,h,j,k){a=a|0;b=b|0;e=e|0;f=f|0;h=h|0;j=j|0;k=k|0;var l=0.0,m=0.0,n=0.0,o=0.0,p=0.0,q=0,r=0.0,s=0;s=i;i=i+1024|0;q=s;if(((h>>>8)+2|0)>>>0>k>>>0?(l=+(h>>>0),+(j>>>0)>l*.99):0){ac(q|0,0,1024)|0;l=l*7.92;j=((h+12|0)>>>0)/13|0;f=e;k=0;while(1){if(k>>>0>=j>>>0)break;e=q+((d[a+(f&b)>>0]|0)<<2)|0;c[e>>2]=(c[e>>2]|0)+1;f=f+13|0;k=k+1|0}r=l/13.0;h=q+1024|0;f=q;l=0.0;e=0;while(1){if(f>>>0>=h>>>0)break;k=c[f>>2]|0;m=+(k>>>0);if(k>>>0<256)p=+g[19516+(k<<2)>>2];else p=+Xb(m);j=c[f+4>>2]|0;n=+(j>>>0);if(j>>>0<256)o=+g[19516+(j<<2)>>2];else o=+Xb(n);f=f+8|0;l=l-m*p-n*o;e=e+k+j|0}n=+(e>>>0);if(!e)o=n;else{if(e>>>0<256)m=+g[19516+(e<<2)>>2];else m=+Xb(n);o=n;l=l+n*m}if((l<o?o:l)>r){q=0;i=s;return q|0}}q=1;i=s;return q|0}function Gb(d,e,f,g){d=d|0;e=e|0;f=f|0;g=g|0;var h=0,j=0,k=0,l=0,m=0;m=i;i=i+64|0;l=m;if((g|0)>=16)pa(406658,406674,26,406709);c[l>>2]=-1;h=0;a:while(1){j=b[e+(d<<3)+4>>1]|0;if(j<<16>>16>-1){k=h+1|0;if((h|0)>=(g|0)){h=0;d=12;break}c[l+(k<<2)>>2]=b[e+(d<<3)+6>>1];h=k;d=j<<16>>16;continue}a[f+(b[e+(d<<3)+6>>1]|0)>>0]=h;while(1){if((h|0)<=-1){h=1;d=12;break a}if((c[l+(h<<2)>>2]|0)!=-1)break;h=h+-1|0}k=l+(h<<2)|0;d=c[k>>2]|0;c[k>>2]=-1}if((d|0)==12){i=m;return h|0}return 0}function Hb(d,e,f,g,h){d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;x=i;i=i+16|0;u=x+8|0;v=x;t=1;while(1){j=e;s=0;a:while(1){do{if(!j)break a;j=j+-1|0;k=c[d+(j<<2)>>2]|0}while((k|0)==0);c[g+(s<<3)>>2]=k>>>0>t>>>0?k:t;b[g+(s<<3)+4>>1]=-1;b[g+(s<<3)+6>>1]=j;s=s+1|0}if((s|0)==1)break;b:do if(s>>>0<13){o=1;while(1){if(o>>>0>=s>>>0)break b;n=g+(o<<3)|0;m=c[n>>2]|0;n=c[n+4>>2]|0;l=u;c[l>>2]=m;c[l+4>>2]=n;l=o;while(1){j=l;l=l+-1|0;k=g+(l<<3)|0;if(!(Kb(u,k)|0))break;p=k;q=c[p+4>>2]|0;r=g+(j<<3)|0;c[r>>2]=c[p>>2];c[r+4>>2]=q;if(!l){j=0;break}}r=g+(j<<3)|0;c[r>>2]=m;c[r+4>>2]=n;o=o+1|0}}else{p=s>>>0<57?2:0;while(1){if((p|0)==6)break b;j=c[19428+(p<<2)>>2]|0;q=j;while(1){if(q>>>0>=s>>>0)break;l=g+(q<<3)|0;k=c[l>>2]|0;l=c[l+4>>2]|0;o=v;c[o>>2]=k;c[o+4>>2]=l;o=q;while(1){if(o>>>0<j>>>0)break;m=o-j|0;n=g+(m<<3)|0;if(!(Kb(v,n)|0))break;y=n;n=c[y+4>>2]|0;r=g+(o<<3)|0;c[r>>2]=c[y>>2];c[r+4>>2]=n;o=m}y=g+(o<<3)|0;c[y>>2]=k;c[y+4>>2]=l;q=q+1|0}p=p+1|0}}while(0);n=g+(s<<3)|0;c[n>>2]=-1;c[n+4>>2]=-1;n=s+1|0;r=g+(n<<3)|0;c[r>>2]=-1;c[r+4>>2]=-1;r=s<<1;m=0;j=s;while(1){j=j+-1|0;if(!j)break;l=c[g+(m<<3)>>2]|0;k=c[g+(n<<3)>>2]|0;if(l>>>0>k>>>0){q=n+1|0;k=c[g+(q<<3)>>2]|0;p=m;o=n}else{p=m+1|0;l=c[g+(p<<3)>>2]|0;q=n;o=m}if(l>>>0>k>>>0){m=p;n=q+1|0;l=q}else{k=l;m=p+1|0;n=q;l=p}y=r-j|0;c[g+(y<<3)>>2]=(c[g+(o<<3)>>2]|0)+k;b[g+(y<<3)+4>>1]=o;b[g+(y<<3)+6>>1]=l;y=g+(y+1<<3)|0;c[y>>2]=-1;c[y+4>>2]=-1}if(Gb(r+-1|0,g,h,f)|0){w=36;break}t=t<<1}if((w|0)==36){i=x;return}a[h+(b[g+6>>1]|0)>>0]=1;i=x;return}function Ib(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0;f=0;g=0;while(1){if((g|0)==(b|0))break;f=(c[d+(g<<2)>>2]|0)==0?f:f+1|0;g=g+1|0}if(f>>>0<16)return;else k=b;while(1){if(!k){l=55;break}b=k+-1|0;if(!(c[d+(b<<2)>>2]|0))k=b;else break}if((l|0)==55)return;g=0;b=1073741824;h=0;while(1){if((h|0)==(k|0))break;f=c[d+(h<<2)>>2]|0;if(f){g=g+1|0;b=b>>>0>f>>>0?f:b}h=h+1|0}if(g>>>0<5)return;a:do if(b>>>0<4&(k-g|0)>>>0<6){b=k+-1|0;f=1;while(1){if(f>>>0>=b>>>0)break a;if(((c[d+(f+-1<<2)>>2]|0)!=0?(i=d+(f<<2)|0,(c[i>>2]|0)==0):0)?(c[d+(f+1<<2)>>2]|0)!=0:0)c[i>>2]=1;f=f+1|0}}while(0);if(g>>>0<28)return;ac(e|0,0,k|0)|0;b=0;i=0;f=c[d>>2]|0;while(1){if(i>>>0>k>>>0)break;g=(i|0)==(k|0);if(!g?(c[d+(i<<2)>>2]|0)==(f|0):0)b=b+1|0;else{if(!f){if(b>>>0>4){h=0;l=29}}else if(b>>>0>6){h=0;l=29}b:do if((l|0)==29)while(1){l=0;if((h|0)==(b|0))break b;a[e+(i-h+-1)>>0]=1;h=h+1|0;l=29}while(0);if(g)b=1;else{b=1;f=c[d+(i<<2)>>2]|0}}i=i+1|0}i=k+-2|0;f=(((c[d>>2]|0)+(c[d+4>>2]|0)+(c[d+8>>2]|0)<<8>>>0)/3|0)+420|0;j=0;g=0;b=0;while(1){if(j>>>0>k>>>0)break;h=(j|0)==(k|0);do if(!h?(a[e+j>>0]|0)==0:0){if((j|0)!=0?(a[e+(j+-1)>>0]|0)!=0:0){l=42;break}if(((c[d+(j<<2)>>2]<<8)-f+1240|0)>>>0>2479)l=42}else l=42;while(0);do if((l|0)==42){l=0;if(!(g>>>0<=3?!(g>>>0>2&(b|0)==0):0))l=44;c:do if((l|0)==44){l=0;f=((b+(g>>>1)|0)>>>0)/(g>>>0)|0;b=(b|0)==0?0:(f|0)==0?1:f;f=0;while(1){if((f|0)==(g|0))break c;c[d+(j-f+-1<<2)>>2]=b;f=f+1|0}}while(0);if(j>>>0<i>>>0){f=(((c[d+(j<<2)>>2]|0)+(c[d+(j+1<<2)>>2]|0)+(c[d+(j+2<<2)>>2]|0)<<8>>>0)/3|0)+420|0;g=0;b=0;break}if(j>>>0<k>>>0){f=c[d+(j<<2)>>2]<<8;g=0;b=0}else{f=0;g=0;b=0}}while(0);g=g+1|0;if(!h){b=b+(c[d+(j<<2)>>2]|0)|0;if(g>>>0>3){f=(((b<<8)+(g>>>1)|0)>>>0)/(g>>>0)|0;f=(g|0)==4?f+120|0:f}}j=j+1|0}return}function Jb(f,g,h){f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;p=i;i=i+64|0;l=p+32|0;o=p;j=l;k=j+32|0;do{b[j>>1]=0;j=j+2|0}while((j|0)<(k|0));j=0;while(1){if((j|0)==(g|0))break;n=l+((d[f+j>>0]|0)<<1)|0;b[n>>1]=(b[n>>1]|0)+1<<16>>16;j=j+1|0}b[l>>1]=0;b[o>>1]=0;j=0;k=1;while(1){if((k|0)==16){n=0;break}n=j+(e[l+(k+-1<<1)>>1]|0)<<1;b[o+(k<<1)>>1]=n;j=n;k=k+1|0}while(1){if((n|0)==(g|0))break;j=a[f+n>>0]|0;if(j<<24>>24){k=j&255;j=o+(k<<1)|0;l=b[j>>1]|0;b[j>>1]=l+1<<16>>16;j=l;l=c[19452+((l&15)<<2)>>2]|0;m=4;while(1){if(m>>>0>=k>>>0)break;q=(j&65535)>>>4;j=q;l=l<<4|c[19452+((q&15)<<2)>>2];m=m+4|0}b[h+(n<<1)>>1]=l>>>(0-k&3)}n=n+1|0}i=p;return}function Kb(a,d){a=a|0;d=d|0;var e=0,f=0;e=c[a>>2]|0;f=c[d>>2]|0;if((e|0)==(f|0)){d=(b[a+6>>1]|0)>(b[d+6>>1]|0);return d|0}else{d=e>>>0<f>>>0;return d|0}return 0}function Lb(a,b){a=a|0;b=b|0;return Yb(b)|0}function Mb(a,b){a=a|0;b=b|0;Zb(b);return}function Nb(f,g,j,k,l,m,n,o,p,q,r){f=f|0;g=g|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;var t=0,u=0,v=0,w=0.0,x=0.0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0;Z=i;i=i+2096|0;W=Z;Za(f,o,p,g,j,k,l,r,r+24|0,r+48|0);Y=f+8|0;R=sa[c[f>>2]&1](c[Y>>2]|0,c[r>>2]<<2)|0;if(!R)oa(1);l=0;while(1){t=c[r>>2]|0;if(l>>>0>=t>>>0)break;c[R+(l<<2)>>2]=q;l=l+1|0}U=r+48|0;u=c[U>>2]|0;T=sa[c[f>>2]&1](c[Y>>2]|0,t*66560|0)|0;if(!T)oa(1);l=t<<6;q=0;while(1){if((q|0)==(l|0))break;ac(T+(q*1040|0)|0,0,1024)|0;c[T+(q*1040|0)+1024>>2]=0;h[T+(q*1040|0)+1032>>3]=s;q=q+1|0}v=r+96|0;if(c[v>>2]|0)pa(406774,406802,70,406832);t=c[r+24>>2]|0;l=r+100|0;c[l>>2]=t;t=sa[c[f>>2]&1](c[Y>>2]|0,t*2832|0)|0;if(!t)oa(1);c[v>>2]=t;l=c[l>>2]|0;q=0;while(1){if((q|0)==(l|0))break;ac(t+(q*2832|0)|0,0,2816)|0;c[t+(q*2832|0)+2816>>2]=0;h[t+(q*2832|0)+2824>>3]=s;q=q+1|0}X=sa[c[f>>2]&1](c[Y>>2]|0,u*8384|0)|0;if(!X)oa(1);l=u<<2;q=0;while(1){if((q|0)==(l|0))break;ac(X+(q*2096|0)|0,0,2080)|0;c[X+(q*2096|0)+2080>>2]=0;h[X+(q*2096|0)+2088>>3]=s;q=q+1|0}l=c[r+12>>2]|0;if(!l)u=0;else u=c[l>>2]|0;l=c[r+36>>2]|0;if(!l)q=0;else q=c[l>>2]|0;l=c[r+60>>2]|0;if(!l)l=0;else l=c[l>>2]|0;P=r+32|0;Q=r+36|0;K=c[v>>2]|0;L=r+8|0;M=r+12|0;N=r+56|0;O=r+60|0;v=n;z=0;t=0;A=0;y=0;C=0;B=0;J=0;while(1){if((J|0)==(p|0))break;if(!q){y=y+1|0;I=d[(c[P>>2]|0)+y>>0]|0;q=c[(c[Q>>2]|0)+(y<<2)>>2]|0}else I=A;G=o+(J<<4)+12|0;F=K+(I*2832|0)+((e[G>>1]|0)<<2)|0;c[F>>2]=(c[F>>2]|0)+1;F=K+(I*2832|0)+2816|0;c[F>>2]=(c[F>>2]|0)+1;F=c[o+(J<<4)>>2]|0;E=v;H=C;n=u;C=j;D=F;while(1){if(!D)break;if(!n){B=B+1|0;A=d[(c[L>>2]|0)+B>>0]|0;u=c[(c[M>>2]|0)+(B<<2)>>2]|0}else{A=H;u=n}n=u+-1|0;v=A<<6;switch(c[R+(A<<2)>>2]|0){case 0:{u=m&63;break}case 1:{u=(m&255)>>>2;break}case 2:{u=a[406969+(m&255)>>0]|a[406969+(E&255|256)>>0];break}case 3:{u=((d[407481+(m&255)>>0]|0)<<3)+(d[407481+(E&255)>>0]|0)&255;break}default:u=0}E=v+(u&255)|0;H=g+(C&k)|0;v=T+(E*1040|0)+((d[H>>0]|0)<<2)|0;c[v>>2]=(c[v>>2]|0)+1;E=T+(E*1040|0)+1024|0;c[E>>2]=(c[E>>2]|0)+1;E=m;m=a[H>>0]|0;H=A;C=C+1|0;D=D+-1|0}q=q+-1|0;D=c[o+(J<<4)+4>>2]&16777215;j=j+F+D|0;if(D){C=a[g+(j+-2&k)>>0]|0;m=a[g+(j+-1&k)>>0]|0;v=b[G>>1]|0;if((v&65535)>127){if(!l){A=t+1|0;z=d[(c[N>>2]|0)+A>>0]|0;l=c[(c[O>>2]|0)+(A<<2)>>2]|0}else A=t;l=l+-1|0;u=z<<2;G=v&65535;t=G&7;switch(G>>>6|0){case 7:case 4:case 2:case 0:{t=t>>>0<3?t:3;break}default:t=3}v=u+t|0;u=X+(v*2096|0)+((e[o+(J<<4)+14>>1]|0)<<2)|0;c[u>>2]=(c[u>>2]|0)+1;v=X+(v*2096|0)+2080|0;c[v>>2]=(c[v>>2]|0)+1;v=C;u=z;t=A}else{v=C;u=z}}else{v=E;u=z}z=u;A=I;C=H;u=n;J=J+1|0}H=f+4|0;ra[c[H>>2]&1](c[Y>>2]|0,R);t=r+72|0;if(c[t>>2]|0)pa(406853,406802,86,406832);l=c[r>>2]|0;q=r+76|0;c[q>>2]=l<<6;l=sa[c[f>>2]&1](c[Y>>2]|0,l<<8)|0;if(!l)oa(1);c[t>>2]=l;l=r+88|0;if(c[l>>2]|0)pa(406882,406802,92,406832);j=c[q>>2]|0;G=r+92|0;c[G>>2]=j;j=sa[c[f>>2]&1](c[Y>>2]|0,j*1040|0)|0;if(!j)oa(1);c[l>>2]=j;F=c[q>>2]|0;l=c[t>>2]|0;C=F<<2;n=sa[c[f>>2]&1](c[Y>>2]|0,C)|0;if(!n)oa(1);D=sa[c[f>>2]&1](c[Y>>2]|0,C)|0;if(!D)oa(1);v=sa[c[f>>2]&1](c[Y>>2]|0,49176)|0;if(!v)oa(1);q=0;while(1){if((q|0)==(F|0))break;c[n+(q<<2)>>2]=1;q=q+1|0}E=l;l=0;while(1){if((l|0)==(F|0))break;g=T+(l*1040|0)|0;dc(j+(l*1040|0)|0,g|0,1040)|0;h[j+(l*1040|0)+1032>>3]=+Wa(g);c[E+(l<<2)>>2]=l;l=l+1|0}u=0;q=0;while(1){if(F>>>0<=q>>>0)break;l=F-q|0;l=l>>>0<64?l:64;t=0;while(1){if((t|0)==(l|0))break;c[D+(u+t<<2)>>2]=q+t;t=t+1|0}u=u+(pb(j,n,E+(q<<2)|0,D+(u<<2)|0,v,l,l,256,2048)|0)|0;q=q+64|0}l=u<<6;t=_(u>>>1,u)|0;t=l>>>0<t>>>0?l:t;l=t+1|0;do if(l>>>0>2048){q=2048;while(1){if(q>>>0>=l>>>0)break;q=q<<1}l=sa[c[f>>2]&1](c[Y>>2]|0,q*24|0)|0;if(!l)oa(1);else{dc(l|0,v|0,49152)|0;ra[c[H>>2]&1](c[Y>>2]|0,v);S=l;break}}else S=v;while(0);B=pb(j,n,E,D,S,u,F,256,t)|0;ra[c[H>>2]&1](c[Y>>2]|0,S);ra[c[H>>2]&1](c[Y>>2]|0,n);m=W+1024|0;y=W+1024|0;A=0;while(1){if((A|0)==(F|0)){l=0;break}t=c[((A|0)==0?E:E+(A+-1<<2)|0)>>2]|0;l=T+(A*1040|0)|0;z=T+(A*1040|0)+1024|0;if(!(c[z>>2]|0))w=0.0;else{dc(W|0,l|0,1040)|0;c[y>>2]=(c[y>>2]|0)+(c[j+(t*1040|0)+1024>>2]|0);q=0;while(1){if((q|0)==256)break;S=W+(q<<2)|0;c[S>>2]=(c[S>>2]|0)+(c[j+(t*1040|0)+(q<<2)>>2]|0);q=q+1|0}w=+Wa(W);w=w-+h[j+(t*1040|0)+1032>>3]}q=t;n=0;while(1){if((n|0)==(B|0))break;v=D+(n<<2)|0;t=c[v>>2]|0;if(!(c[z>>2]|0))x=0.0;else{dc(W|0,l|0,1040)|0;c[m>>2]=(c[m>>2]|0)+(c[j+(t*1040|0)+1024>>2]|0);u=0;while(1){if((u|0)==256)break;S=W+(u<<2)|0;c[S>>2]=(c[S>>2]|0)+(c[j+(t*1040|0)+(u<<2)>>2]|0);u=u+1|0}x=+Wa(W);x=x-+h[j+(t*1040|0)+1032>>3]}if(x<w){w=x;q=c[v>>2]|0}n=n+1|0}c[E+(A<<2)>>2]=q;A=A+1|0}while(1){if((l|0)==(B|0)){q=0;break}S=c[D+(l<<2)>>2]|0;ac(j+(S*1040|0)|0,0,1024)|0;c[j+(S*1040|0)+1024>>2]=0;h[j+(S*1040|0)+1032>>3]=s;l=l+1|0}while(1){if((q|0)==(F|0))break;l=c[E+(q<<2)>>2]|0;t=j+(l*1040|0)+1024|0;c[t>>2]=(c[t>>2]|0)+(c[T+(q*1040|0)+1024>>2]|0);t=0;while(1){if((t|0)==256)break;S=j+(l*1040|0)+(t<<2)|0;c[S>>2]=(c[S>>2]|0)+(c[T+(q*1040|0)+(t<<2)>>2]|0);t=t+1|0}q=q+1|0}ra[c[H>>2]&1](c[Y>>2]|0,D);m=sa[c[f>>2]&1](c[Y>>2]|0,C)|0;if(!m)oa(1);l=0;while(1){if((l|0)==(F|0)){l=0;t=0;break}c[m+(l<<2)>>2]=-1;l=l+1|0}while(1){if((t|0)==(F|0))break;q=m+(c[E+(t<<2)>>2]<<2)|0;if((c[q>>2]|0)==-1){c[q>>2]=l;l=l+1|0}t=t+1|0}n=sa[c[f>>2]&1](c[Y>>2]|0,l*1040|0)|0;if(!n)oa(1);u=0;v=0;while(1){if((v|0)==(F|0))break;t=E+(v<<2)|0;l=c[t>>2]|0;q=c[m+(l<<2)>>2]|0;if((q|0)==(u|0)){dc(n+(u*1040|0)|0,j+(l*1040|0)|0,1040)|0;q=c[m+(c[t>>2]<<2)>>2]|0;l=u+1|0}else l=u;c[t>>2]=q;u=l;v=v+1|0}ra[c[H>>2]&1](c[Y>>2]|0,m);l=0;while(1){if((l|0)==(u|0))break;dc(j+(l*1040|0)|0,n+(l*1040|0)|0,1040)|0;l=l+1|0}ra[c[H>>2]&1](c[Y>>2]|0,n);c[G>>2]=u;ra[c[H>>2]&1](c[Y>>2]|0,T);t=r+80|0;if(c[t>>2]|0)pa(406910,406802,106,406832);l=c[U>>2]|0;q=r+84|0;c[q>>2]=l<<2;l=sa[c[f>>2]&1](c[Y>>2]|0,l<<4)|0;if(!l)oa(1);c[t>>2]=l;l=r+104|0;if(c[l>>2]|0)pa(406940,406802,112,406832);j=c[q>>2]|0;G=r+108|0;c[G>>2]=j;j=sa[c[f>>2]&1](c[Y>>2]|0,j*2096|0)|0;if(!j)oa(1);c[l>>2]=j;F=c[q>>2]|0;l=c[t>>2]|0;C=F<<2;n=sa[c[f>>2]&1](c[Y>>2]|0,C)|0;if(!n)oa(1);D=sa[c[f>>2]&1](c[Y>>2]|0,C)|0;if(!D)oa(1);v=sa[c[f>>2]&1](c[Y>>2]|0,49176)|0;if(!v)oa(1);q=0;while(1){if((q|0)==(F|0))break;c[n+(q<<2)>>2]=1;q=q+1|0}E=l;l=0;while(1){if((l|0)==(F|0))break;r=X+(l*2096|0)|0;dc(j+(l*2096|0)|0,r|0,2096)|0;h[j+(l*2096|0)+2088>>3]=+Ya(r);c[E+(l<<2)>>2]=l;l=l+1|0}u=0;q=0;while(1){if(F>>>0<=q>>>0)break;l=F-q|0;l=l>>>0<64?l:64;t=0;while(1){if((t|0)==(l|0))break;c[D+(u+t<<2)>>2]=q+t;t=t+1|0}u=u+(tb(j,n,E+(q<<2)|0,D+(u<<2)|0,v,l,l,256,2048)|0)|0;q=q+64|0}l=u<<6;t=_(u>>>1,u)|0;t=l>>>0<t>>>0?l:t;l=t+1|0;do if(l>>>0>2048){q=2048;while(1){if(q>>>0>=l>>>0)break;q=q<<1}l=sa[c[f>>2]&1](c[Y>>2]|0,q*24|0)|0;if(!l)oa(1);else{dc(l|0,v|0,49152)|0;ra[c[H>>2]&1](c[Y>>2]|0,v);V=l;break}}else V=v;while(0);B=tb(j,n,E,D,V,u,F,256,t)|0;ra[c[H>>2]&1](c[Y>>2]|0,V);ra[c[H>>2]&1](c[Y>>2]|0,n);m=W+2080|0;y=W+2080|0;A=0;while(1){if((A|0)==(F|0)){l=0;break}t=c[((A|0)==0?E:E+(A+-1<<2)|0)>>2]|0;l=X+(A*2096|0)|0;z=X+(A*2096|0)+2080|0;if(!(c[z>>2]|0))w=0.0;else{dc(W|0,l|0,2096)|0;c[y>>2]=(c[y>>2]|0)+(c[j+(t*2096|0)+2080>>2]|0);q=0;while(1){if((q|0)==520)break;V=W+(q<<2)|0;c[V>>2]=(c[V>>2]|0)+(c[j+(t*2096|0)+(q<<2)>>2]|0);q=q+1|0}w=+Ya(W);w=w-+h[j+(t*2096|0)+2088>>3]}q=t;n=0;while(1){if((n|0)==(B|0))break;v=D+(n<<2)|0;t=c[v>>2]|0;if(!(c[z>>2]|0))x=0.0;else{dc(W|0,l|0,2096)|0;c[m>>2]=(c[m>>2]|0)+(c[j+(t*2096|0)+2080>>2]|0);u=0;while(1){if((u|0)==520)break;V=W+(u<<2)|0;c[V>>2]=(c[V>>2]|0)+(c[j+(t*2096|0)+(u<<2)>>2]|0);u=u+1|0}x=+Ya(W);x=x-+h[j+(t*2096|0)+2088>>3]}if(x<w){w=x;q=c[v>>2]|0}n=n+1|0}c[E+(A<<2)>>2]=q;A=A+1|0}while(1){if((l|0)==(B|0)){q=0;break}W=c[D+(l<<2)>>2]|0;ac(j+(W*2096|0)|0,0,2080)|0;c[j+(W*2096|0)+2080>>2]=0;h[j+(W*2096|0)+2088>>3]=s;l=l+1|0}while(1){if((q|0)==(F|0))break;l=c[E+(q<<2)>>2]|0;t=j+(l*2096|0)+2080|0;c[t>>2]=(c[t>>2]|0)+(c[X+(q*2096|0)+2080>>2]|0);t=0;while(1){if((t|0)==520)break;W=j+(l*2096|0)+(t<<2)|0;c[W>>2]=(c[W>>2]|0)+(c[X+(q*2096|0)+(t<<2)>>2]|0);t=t+1|0}q=q+1|0}ra[c[H>>2]&1](c[Y>>2]|0,D);m=sa[c[f>>2]&1](c[Y>>2]|0,C)|0;if(!m)oa(1);l=0;while(1){if((l|0)==(F|0)){l=0;t=0;break}c[m+(l<<2)>>2]=-1;l=l+1|0}while(1){if((t|0)==(F|0))break;q=m+(c[E+(t<<2)>>2]<<2)|0;if((c[q>>2]|0)==-1){c[q>>2]=l;l=l+1|0}t=t+1|0}n=sa[c[f>>2]&1](c[Y>>2]|0,l*2096|0)|0;if(!n)oa(1);u=0;v=0;while(1){if((v|0)==(F|0))break;t=E+(v<<2)|0;l=c[t>>2]|0;q=c[m+(l<<2)>>2]|0;if((q|0)==(u|0)){dc(n+(u*2096|0)|0,j+(l*2096|0)|0,2096)|0;q=c[m+(c[t>>2]<<2)>>2]|0;l=u+1|0}else l=u;c[t>>2]=q;u=l;v=v+1|0}ra[c[H>>2]&1](c[Y>>2]|0,m);l=0;while(1){if((l|0)==(u|0))break;dc(j+(l*2096|0)|0,n+(l*2096|0)|0,2096)|0;l=l+1|0}ra[c[H>>2]&1](c[Y>>2]|0,n);c[G>>2]=u;ra[c[H>>2]&1](c[Y>>2]|0,X);i=Z;return}function Ob(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0;j=i;i=i+704|0;h=j;e=d+92|0;f=d+88|0;g=0;while(1){if(g>>>0>=(c[e>>2]|0)>>>0)break;Ib(256,(c[f>>2]|0)+(g*1040|0)|0,h);g=g+1|0}e=d+100|0;f=d+96|0;g=0;while(1){if(g>>>0>=(c[e>>2]|0)>>>0)break;Ib(704,(c[f>>2]|0)+(g*2832|0)|0,h);g=g+1|0}g=a+16+(48<<b)|0;b=d+108|0;e=d+104|0;f=0;while(1){if(f>>>0>=(c[b>>2]|0)>>>0)break;Ib(g,(c[e>>2]|0)+(f*2096|0)|0,h);f=f+1|0}i=j;return}function Pb(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;o=(d>>>10)+1|0;n=o>>>0<257?o:257;c[b>>2]=704;c[b+4>>2]=1024;h[b+8>>3]=500.0;c[b+16>>2]=0;p=b+20|0;c[p>>2]=e;c[b+28>>2]=g;c[b+32>>2]=1024;c[b+36>>2]=0;c[b+40>>2]=0;c[b+72>>2]=0;m=e+16|0;d=c[m>>2]|0;if(d>>>0<o>>>0){l=(d|0)==0?o:d;while(1){if(l>>>0>=o>>>0)break;l=l<<1}i=a+8|0;j=sa[c[a>>2]&1](c[i>>2]|0,l)|0;if(!j)oa(1);k=c[m>>2]|0;d=e+8|0;if(k)dc(j|0,c[d>>2]|0,k|0)|0;ra[c[a+4>>2]&1](c[i>>2]|0,c[d>>2]|0);c[d>>2]=j;c[m>>2]=l}m=e+20|0;d=c[m>>2]|0;if(d>>>0<o>>>0){l=(d|0)==0?o:d;while(1){if(l>>>0>=o>>>0)break;l=l<<1}i=a+8|0;j=sa[c[a>>2]&1](c[i>>2]|0,l<<2)|0;if(!j)oa(1);k=c[m>>2]|0;d=e+12|0;if(k)dc(j|0,c[d>>2]|0,k<<2|0)|0;ra[c[a+4>>2]&1](c[i>>2]|0,c[d>>2]|0);c[d>>2]=j;c[m>>2]=l}c[(c[p>>2]|0)+4>>2]=o;if(c[f>>2]|0)pa(407776,407818,70,407880);c[g>>2]=n;d=sa[c[a>>2]&1](c[a+8>>2]|0,n*2832|0)|0;i=d;if(!d)oa(1);else{c[f>>2]=i;c[b+24>>2]=i;ac(d|0,0,2816)|0;c[d+2816>>2]=0;h[d+2824>>3]=s;c[b+48>>2]=0;c[b+44>>2]=0;return}}function Qb(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;o=(d>>>9)+1|0;n=o>>>0<257?o:257;c[b>>2]=64;c[b+4>>2]=512;h[b+8>>3]=100.0;c[b+16>>2]=0;p=b+20|0;c[p>>2]=e;c[b+28>>2]=g;c[b+32>>2]=512;c[b+36>>2]=0;c[b+40>>2]=0;c[b+72>>2]=0;m=e+16|0;d=c[m>>2]|0;if(d>>>0<o>>>0){l=(d|0)==0?o:d;while(1){if(l>>>0>=o>>>0)break;l=l<<1}i=a+8|0;j=sa[c[a>>2]&1](c[i>>2]|0,l)|0;if(!j)oa(1);k=c[m>>2]|0;d=e+8|0;if(k)dc(j|0,c[d>>2]|0,k|0)|0;ra[c[a+4>>2]&1](c[i>>2]|0,c[d>>2]|0);c[d>>2]=j;c[m>>2]=l}m=e+20|0;d=c[m>>2]|0;if(d>>>0<o>>>0){l=(d|0)==0?o:d;while(1){if(l>>>0>=o>>>0)break;l=l<<1}i=a+8|0;j=sa[c[a>>2]&1](c[i>>2]|0,l<<2)|0;if(!j)oa(1);k=c[m>>2]|0;d=e+12|0;if(k)dc(j|0,c[d>>2]|0,k<<2|0)|0;ra[c[a+4>>2]&1](c[i>>2]|0,c[d>>2]|0);c[d>>2]=j;c[m>>2]=l}c[(c[p>>2]|0)+4>>2]=o;if(c[f>>2]|0)pa(407776,407818,70,407854);c[g>>2]=n;d=sa[c[a>>2]&1](c[a+8>>2]|0,n*2096|0)|0;i=d;if(!d)oa(1);else{c[f>>2]=i;c[b+24>>2]=i;ac(d|0,0,2080)|0;c[d+2080>>2]=0;h[d+2088>>3]=s;c[b+48>>2]=0;c[b+44>>2]=0;return}}function Rb(b,d){b=b|0;d=d|0;var e=0,f=0.0,j=0.0,k=0.0,l=0.0,m=0,n=0,o=0,p=0,q=0,r=0.0,t=0,u=0,v=0.0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0;J=i;i=i+2112|0;y=J+32|0;x=J+16|0;z=J;G=c[b+20>>2]|0;F=b+56|0;e=c[b+24>>2]|0;I=b+36|0;H=c[I>>2]|0;D=b+4|0;B=c[D>>2]|0;B=H>>>0>B>>>0?H:B;c[I>>2]=B;H=b+16|0;w=c[H>>2]|0;do if(w){if(B){E=b+40|0;u=c[E>>2]|0;C=e;q=c[b>>2]|0;e=C+(u*1040|0)|0;o=C+(u*1040|0)+(q<<2)|0;t=(q&1|0)==0;if(t){f=0.0;n=0}else{k=0.0;n=0;A=27}while(1){if((A|0)==27){A=0;m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=27}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}r=f<k?k:f;p=0;while(1){if((p|0)==2)break;e=c[b+44+(p<<2)>>2]|0;dc(y+(p*1040|0)|0,C+(u*1040|0)|0,1040)|0;m=y+(p*1040|0)+1024|0;c[m>>2]=(c[m>>2]|0)+(c[C+(e*1040|0)+1024>>2]|0);m=0;while(1){if((m|0)==256)break;o=y+(p*1040|0)+(m<<2)|0;c[o>>2]=(c[o>>2]|0)+(c[C+(e*1040|0)+(m<<2)>>2]|0);m=m+1|0}e=y+(p*1040|0)|0;o=y+(p*1040|0)+(q<<2)|0;if(t){f=0.0;n=0}else{k=0.0;n=0;A=47}while(1){if((A|0)==47){A=0;m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=47}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}l=f<k?k:f;h[x+(p<<3)>>3]=l;h[z+(p<<3)>>3]=l-r-+h[b+56+(p<<3)>>3];p=p+1|0}e=G;f=+h[z>>3];if(((c[e>>2]|0)>>>0<256?(v=+h[b+8>>3],f>v):0)?+h[z+8>>3]>v:0){c[(c[e+12>>2]|0)+(w<<2)>>2]=B;a[(c[e+8>>2]|0)+(c[H>>2]|0)>>0]=c[e>>2];B=b+44|0;c[b+48>>2]=c[B>>2];c[B>>2]=c[e>>2]&255;h[b+64>>3]=+h[F>>3];h[F>>3]=r;c[H>>2]=(c[H>>2]|0)+1;c[e>>2]=(c[e>>2]|0)+1;e=(c[E>>2]|0)+1|0;c[E>>2]=e;if(e>>>0<(c[c[b+28>>2]>>2]|0)>>>0){ac(C+(e*1040|0)|0,0,1024)|0;c[C+(e*1040|0)+1024>>2]=0;h[C+(e*1040|0)+1032>>3]=s}c[I>>2]=0;c[b+72>>2]=0;c[b+32>>2]=c[D>>2];break}if(+h[z+8>>3]<f+-20.0){c[(c[e+12>>2]|0)+(w<<2)>>2]=B;w=c[H>>2]|0;z=c[e+8>>2]|0;a[z+w>>0]=a[z+(w+-2)>>0]|0;w=b+44|0;z=c[w>>2]|0;A=b+48|0;B=c[A>>2]|0;c[w>>2]=B;c[A>>2]=z;dc(C+(B*1040|0)|0,y+1040|0,1040)|0;h[b+64>>3]=+h[F>>3];h[F>>3]=+h[x+8>>3];c[H>>2]=(c[H>>2]|0)+1;c[I>>2]=0;I=c[E>>2]|0;ac(C+(I*1040|0)|0,0,1024)|0;c[C+(I*1040|0)+1024>>2]=0;h[C+(I*1040|0)+1032>>3]=s;c[b+72>>2]=0;c[b+32>>2]=c[D>>2];break}A=(c[e+12>>2]|0)+(w+-1<<2)|0;c[A>>2]=(c[A>>2]|0)+B;dc(C+((c[b+44>>2]|0)*1040|0)|0,y|0,1040)|0;f=+h[x>>3];h[F>>3]=f;if((c[e>>2]|0)==1)h[b+64>>3]=f;c[I>>2]=0;F=c[E>>2]|0;ac(C+(F*1040|0)|0,0,1024)|0;c[C+(F*1040|0)+1024>>2]=0;h[C+(F*1040|0)+1032>>3]=s;F=b+72|0;I=(c[F>>2]|0)+1|0;c[F>>2]=I;if(I>>>0>1){I=b+32|0;c[I>>2]=(c[I>>2]|0)+(c[D>>2]|0)}}}else{p=G;c[c[p+12>>2]>>2]=B;a[c[p+8>>2]>>0]=0;q=e;E=c[b>>2]|0;o=q+(E<<2)|0;if(!(E&1)){f=0.0;n=0}else{k=0.0;n=0;A=8}while(1){if((A|0)==8){m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=8}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}v=f<k?k:f;h[F>>3]=v;h[b+64>>3]=v;c[H>>2]=(c[H>>2]|0)+1;c[p>>2]=(c[p>>2]|0)+1;F=b+40|0;e=(c[F>>2]|0)+1|0;c[F>>2]=e;if(e>>>0<(c[c[b+28>>2]>>2]|0)>>>0){ac(q+(e*1040|0)|0,0,1024)|0;c[q+(e*1040|0)+1024>>2]=0;h[q+(e*1040|0)+1032>>3]=s}c[I>>2]=0}while(0);if(!d){i=J;return}d=G;c[c[b+28>>2]>>2]=c[d>>2];c[d+4>>2]=c[H>>2];i=J;return}function Sb(b,d){b=b|0;d=d|0;var e=0,f=0.0,j=0.0,k=0.0,l=0.0,m=0,n=0,o=0,p=0,q=0,r=0.0,t=0,u=0,v=0.0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0;J=i;i=i+5696|0;y=J+32|0;x=J+16|0;z=J;G=c[b+20>>2]|0;F=b+56|0;e=c[b+24>>2]|0;I=b+36|0;H=c[I>>2]|0;D=b+4|0;B=c[D>>2]|0;B=H>>>0>B>>>0?H:B;c[I>>2]=B;H=b+16|0;w=c[H>>2]|0;do if(w){if(B){E=b+40|0;u=c[E>>2]|0;C=e;q=c[b>>2]|0;e=C+(u*2832|0)|0;o=C+(u*2832|0)+(q<<2)|0;t=(q&1|0)==0;if(t){f=0.0;n=0}else{k=0.0;n=0;A=27}while(1){if((A|0)==27){A=0;m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=27}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}r=f<k?k:f;p=0;while(1){if((p|0)==2)break;e=c[b+44+(p<<2)>>2]|0;dc(y+(p*2832|0)|0,C+(u*2832|0)|0,2832)|0;m=y+(p*2832|0)+2816|0;c[m>>2]=(c[m>>2]|0)+(c[C+(e*2832|0)+2816>>2]|0);m=0;while(1){if((m|0)==704)break;o=y+(p*2832|0)+(m<<2)|0;c[o>>2]=(c[o>>2]|0)+(c[C+(e*2832|0)+(m<<2)>>2]|0);m=m+1|0}e=y+(p*2832|0)|0;o=y+(p*2832|0)+(q<<2)|0;if(t){f=0.0;n=0}else{k=0.0;n=0;A=47}while(1){if((A|0)==47){A=0;m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=47}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}l=f<k?k:f;h[x+(p<<3)>>3]=l;h[z+(p<<3)>>3]=l-r-+h[b+56+(p<<3)>>3];p=p+1|0}e=G;f=+h[z>>3];if(((c[e>>2]|0)>>>0<256?(v=+h[b+8>>3],f>v):0)?+h[z+8>>3]>v:0){c[(c[e+12>>2]|0)+(w<<2)>>2]=B;a[(c[e+8>>2]|0)+(c[H>>2]|0)>>0]=c[e>>2];B=b+44|0;c[b+48>>2]=c[B>>2];c[B>>2]=c[e>>2]&255;h[b+64>>3]=+h[F>>3];h[F>>3]=r;c[H>>2]=(c[H>>2]|0)+1;c[e>>2]=(c[e>>2]|0)+1;e=(c[E>>2]|0)+1|0;c[E>>2]=e;if(e>>>0<(c[c[b+28>>2]>>2]|0)>>>0){ac(C+(e*2832|0)|0,0,2816)|0;c[C+(e*2832|0)+2816>>2]=0;h[C+(e*2832|0)+2824>>3]=s}c[I>>2]=0;c[b+72>>2]=0;c[b+32>>2]=c[D>>2];break}if(+h[z+8>>3]<f+-20.0){c[(c[e+12>>2]|0)+(w<<2)>>2]=B;w=c[H>>2]|0;z=c[e+8>>2]|0;a[z+w>>0]=a[z+(w+-2)>>0]|0;w=b+44|0;z=c[w>>2]|0;A=b+48|0;B=c[A>>2]|0;c[w>>2]=B;c[A>>2]=z;dc(C+(B*2832|0)|0,y+2832|0,2832)|0;h[b+64>>3]=+h[F>>3];h[F>>3]=+h[x+8>>3];c[H>>2]=(c[H>>2]|0)+1;c[I>>2]=0;I=c[E>>2]|0;ac(C+(I*2832|0)|0,0,2816)|0;c[C+(I*2832|0)+2816>>2]=0;h[C+(I*2832|0)+2824>>3]=s;c[b+72>>2]=0;c[b+32>>2]=c[D>>2];break}A=(c[e+12>>2]|0)+(w+-1<<2)|0;c[A>>2]=(c[A>>2]|0)+B;dc(C+((c[b+44>>2]|0)*2832|0)|0,y|0,2832)|0;f=+h[x>>3];h[F>>3]=f;if((c[e>>2]|0)==1)h[b+64>>3]=f;c[I>>2]=0;F=c[E>>2]|0;ac(C+(F*2832|0)|0,0,2816)|0;c[C+(F*2832|0)+2816>>2]=0;h[C+(F*2832|0)+2824>>3]=s;F=b+72|0;I=(c[F>>2]|0)+1|0;c[F>>2]=I;if(I>>>0>1){I=b+32|0;c[I>>2]=(c[I>>2]|0)+(c[D>>2]|0)}}}else{p=G;c[c[p+12>>2]>>2]=B;a[c[p+8>>2]>>0]=0;q=e;E=c[b>>2]|0;o=q+(E<<2)|0;if(!(E&1)){f=0.0;n=0}else{k=0.0;n=0;A=8}while(1){if((A|0)==8){m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=8}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}v=f<k?k:f;h[F>>3]=v;h[b+64>>3]=v;c[H>>2]=(c[H>>2]|0)+1;c[p>>2]=(c[p>>2]|0)+1;F=b+40|0;e=(c[F>>2]|0)+1|0;c[F>>2]=e;if(e>>>0<(c[c[b+28>>2]>>2]|0)>>>0){ac(q+(e*2832|0)|0,0,2816)|0;c[q+(e*2832|0)+2816>>2]=0;h[q+(e*2832|0)+2824>>3]=s}c[I>>2]=0}while(0);if(!d){i=J;return}d=G;c[c[b+28>>2]>>2]=c[d>>2];c[d+4>>2]=c[H>>2];i=J;return}function Tb(b,d){b=b|0;d=d|0;var e=0,f=0.0,j=0.0,k=0.0,l=0.0,m=0,n=0,o=0,p=0,q=0,r=0.0,t=0,u=0,v=0.0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0;J=i;i=i+4224|0;y=J+32|0;x=J+16|0;z=J;G=c[b+20>>2]|0;F=b+56|0;e=c[b+24>>2]|0;I=b+36|0;H=c[I>>2]|0;D=b+4|0;B=c[D>>2]|0;B=H>>>0>B>>>0?H:B;c[I>>2]=B;H=b+16|0;w=c[H>>2]|0;do if(w){if(B){E=b+40|0;u=c[E>>2]|0;C=e;q=c[b>>2]|0;e=C+(u*2096|0)|0;o=C+(u*2096|0)+(q<<2)|0;t=(q&1|0)==0;if(t){f=0.0;n=0}else{k=0.0;n=0;A=27}while(1){if((A|0)==27){A=0;m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=27}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}r=f<k?k:f;p=0;while(1){if((p|0)==2)break;e=c[b+44+(p<<2)>>2]|0;dc(y+(p*2096|0)|0,C+(u*2096|0)|0,2096)|0;m=y+(p*2096|0)+2080|0;c[m>>2]=(c[m>>2]|0)+(c[C+(e*2096|0)+2080>>2]|0);m=0;while(1){if((m|0)==520)break;o=y+(p*2096|0)+(m<<2)|0;c[o>>2]=(c[o>>2]|0)+(c[C+(e*2096|0)+(m<<2)>>2]|0);m=m+1|0}e=y+(p*2096|0)|0;o=y+(p*2096|0)+(q<<2)|0;if(t){f=0.0;n=0}else{k=0.0;n=0;A=47}while(1){if((A|0)==47){A=0;m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=47}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}l=f<k?k:f;h[x+(p<<3)>>3]=l;h[z+(p<<3)>>3]=l-r-+h[b+56+(p<<3)>>3];p=p+1|0}e=G;f=+h[z>>3];if(((c[e>>2]|0)>>>0<256?(v=+h[b+8>>3],f>v):0)?+h[z+8>>3]>v:0){c[(c[e+12>>2]|0)+(w<<2)>>2]=B;a[(c[e+8>>2]|0)+(c[H>>2]|0)>>0]=c[e>>2];B=b+44|0;c[b+48>>2]=c[B>>2];c[B>>2]=c[e>>2]&255;h[b+64>>3]=+h[F>>3];h[F>>3]=r;c[H>>2]=(c[H>>2]|0)+1;c[e>>2]=(c[e>>2]|0)+1;e=(c[E>>2]|0)+1|0;c[E>>2]=e;if(e>>>0<(c[c[b+28>>2]>>2]|0)>>>0){ac(C+(e*2096|0)|0,0,2080)|0;c[C+(e*2096|0)+2080>>2]=0;h[C+(e*2096|0)+2088>>3]=s}c[I>>2]=0;c[b+72>>2]=0;c[b+32>>2]=c[D>>2];break}if(+h[z+8>>3]<f+-20.0){c[(c[e+12>>2]|0)+(w<<2)>>2]=B;w=c[H>>2]|0;z=c[e+8>>2]|0;a[z+w>>0]=a[z+(w+-2)>>0]|0;w=b+44|0;z=c[w>>2]|0;A=b+48|0;B=c[A>>2]|0;c[w>>2]=B;c[A>>2]=z;dc(C+(B*2096|0)|0,y+2096|0,2096)|0;h[b+64>>3]=+h[F>>3];h[F>>3]=+h[x+8>>3];c[H>>2]=(c[H>>2]|0)+1;c[I>>2]=0;I=c[E>>2]|0;ac(C+(I*2096|0)|0,0,2080)|0;c[C+(I*2096|0)+2080>>2]=0;h[C+(I*2096|0)+2088>>3]=s;c[b+72>>2]=0;c[b+32>>2]=c[D>>2];break}A=(c[e+12>>2]|0)+(w+-1<<2)|0;c[A>>2]=(c[A>>2]|0)+B;dc(C+((c[b+44>>2]|0)*2096|0)|0,y|0,2096)|0;f=+h[x>>3];h[F>>3]=f;if((c[e>>2]|0)==1)h[b+64>>3]=f;c[I>>2]=0;F=c[E>>2]|0;ac(C+(F*2096|0)|0,0,2080)|0;c[C+(F*2096|0)+2080>>2]=0;h[C+(F*2096|0)+2088>>3]=s;F=b+72|0;I=(c[F>>2]|0)+1|0;c[F>>2]=I;if(I>>>0>1){I=b+32|0;c[I>>2]=(c[I>>2]|0)+(c[D>>2]|0)}}}else{p=G;c[c[p+12>>2]>>2]=B;a[c[p+8>>2]>>0]=0;q=e;E=c[b>>2]|0;o=q+(E<<2)|0;if(!(E&1)){f=0.0;n=0}else{k=0.0;n=0;A=8}while(1){if((A|0)==8){m=c[e>>2]|0;f=+(m>>>0);if(m>>>0<256)j=+g[19516+(m<<2)>>2];else j=+Xb(f);e=e+4|0;f=k-f*j;n=n+m|0}if(e>>>0>=o>>>0)break;m=c[e>>2]|0;j=+(m>>>0);if(m>>>0<256)k=+g[19516+(m<<2)>>2];else k=+Xb(j);e=e+4|0;k=f-j*k;n=n+m|0;A=8}l=+(n>>>0);if(!n)k=l;else{if(n>>>0<256)j=+g[19516+(n<<2)>>2];else j=+Xb(l);k=l;f=f+l*j}v=f<k?k:f;h[F>>3]=v;h[b+64>>3]=v;c[H>>2]=(c[H>>2]|0)+1;c[p>>2]=(c[p>>2]|0)+1;F=b+40|0;e=(c[F>>2]|0)+1|0;c[F>>2]=e;if(e>>>0<(c[c[b+28>>2]>>2]|0)>>>0){ac(q+(e*2096|0)|0,0,2080)|0;c[q+(e*2096|0)+2080>>2]=0;h[q+(e*2096|0)+2088>>3]=s}c[I>>2]=0}while(0);if(!d){i=J;return}d=G;c[c[b+28>>2]>>2]=c[d>>2];c[d+4>>2]=c[H>>2];i=J;return}function Ub(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,j=0,k=0,l=0,m=0.0,n=0.0,o=0.0,p=0.0,q=0,r=0,t=0,u=0,v=0,w=0,x=0,y=0.0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0;N=i;i=i+16|0;z=N;K=c[d+28>>2]|0;I=d+4|0;L=c[I>>2]|0;l=c[d+60>>2]|0;H=d+32|0;k=c[H>>2]|0;M=d+44|0;j=c[M>>2]|0;F=d+12|0;f=c[F>>2]|0;if(j>>>0<f>>>0)c[M>>2]=f;else f=j;J=d+24|0;if(c[J>>2]|0){if(f){C=b+8|0;D=sa[c[b>>2]&1](c[C>>2]|0,L<<3)|0;if(!D)oa(1);E=sa[c[b>>2]&1](c[C>>2]|0,L*2080|0)|0;if(!E)oa(1);B=sa[c[b>>2]&1](c[C>>2]|0,L<<4)|0;if(!B)oa(1);c[z>>2]=0;c[z+4>>2]=0;c[z+8>>2]=0;c[z+12>>2]=0;A=d+48|0;x=0;while(1){if((x|0)==(L|0))break;t=(c[A>>2]|0)+x|0;w=c[d>>2]|0;f=k+(t*1040|0)|0;r=k+(t*1040|0)+(w<<2)|0;if(!(w&1)){m=0.0;q=0}else{o=0.0;q=0;G=42}while(1){if((G|0)==42){G=0;j=c[f>>2]|0;m=+(j>>>0);if(j>>>0<256)n=+g[19516+(j<<2)>>2];else n=+Xb(m);f=f+4|0;m=o-m*n;q=q+j|0}if(f>>>0>=r>>>0)break;j=c[f>>2]|0;n=+(j>>>0);if(j>>>0<256)o=+g[19516+(j<<2)>>2];else o=+Xb(n);f=f+4|0;o=m-n*o;q=q+j|0;G=42}o=+(q>>>0);if(!q)p=o;else{if(q>>>0<256)n=+g[19516+(q<<2)>>2];else n=+Xb(o);p=o;m=m+o*n}w=D+(x<<3)|0;h[w>>3]=m<p?p:m;t=k+(t*1040|0)|0;v=0;while(1){if((v|0)==2)break;u=(_(v,L)|0)+x|0;f=(c[d+52+(v<<2)>>2]|0)+x|0;dc(E+(u*1040|0)|0,t|0,1040)|0;j=E+(u*1040|0)+1024|0;c[j>>2]=(c[j>>2]|0)+(c[k+(f*1040|0)+1024>>2]|0);j=0;while(1){if((j|0)==256)break;r=E+(u*1040|0)+(j<<2)|0;c[r>>2]=(c[r>>2]|0)+(c[k+(f*1040|0)+(j<<2)>>2]|0);j=j+1|0}q=c[d>>2]|0;f=E+(u*1040|0)|0;r=E+(u*1040|0)+(q<<2)|0;if(!(q&1)){m=0.0;q=0}else{o=0.0;q=0;G=62}while(1){if((G|0)==62){G=0;j=c[f>>2]|0;m=+(j>>>0);if(j>>>0<256)n=+g[19516+(j<<2)>>2];else n=+Xb(m);f=f+4|0;m=o-m*n;q=q+j|0}if(f>>>0>=r>>>0)break;j=c[f>>2]|0;n=+(j>>>0);if(j>>>0<256)o=+g[19516+(j<<2)>>2];else o=+Xb(n);f=f+4|0;o=m-n*o;q=q+j|0;G=62}p=+(q>>>0);if(!q)o=p;else{if(q>>>0<256)n=+g[19516+(q<<2)>>2];else n=+Xb(p);o=p;m=m+p*n}p=m<o?o:m;h[B+(u<<3)>>3]=p;r=z+(v<<3)|0;h[r>>3]=+h[r>>3]+(p-+h[w>>3]-+h[l+(u<<3)>>3]);v=v+1|0}x=x+1|0}q=K;m=+h[z>>3];if(((c[q>>2]|0)>>>0<(c[d+8>>2]|0)>>>0?(y=+h[d+16>>3],m>y):0)?+h[z+8>>3]>y:0){c[(c[q+12>>2]|0)+(c[J>>2]<<2)>>2]=c[M>>2];a[(c[q+8>>2]|0)+(c[J>>2]|0)>>0]=c[q>>2];f=d+52|0;c[d+56>>2]=c[f>>2];c[f>>2]=_(c[q>>2]|0,L)|0;f=0;while(1){if((f|0)==(L|0))break;z=l+(f<<3)|0;h[l+(L+f<<3)>>3]=+h[z>>3];h[z>>3]=+h[D+(f<<3)>>3];f=f+1|0}c[J>>2]=(c[J>>2]|0)+1;c[q>>2]=(c[q>>2]|0)+1;l=(c[A>>2]|0)+L|0;c[A>>2]=l;a:do if(l>>>0<(c[c[d+36>>2]>>2]|0)>>>0){k=c[H>>2]|0;f=c[I>>2]|0;j=0;while(1){if((j|0)==(f|0))break a;I=l+j|0;ac(k+(I*1040|0)|0,0,1024)|0;c[k+(I*1040|0)+1024>>2]=0;h[k+(I*1040|0)+1032>>3]=s;j=j+1|0}}while(0);c[M>>2]=0;c[d+64>>2]=0;c[d+40>>2]=c[F>>2]}else G=84;do if((G|0)==84){f=c[M>>2]|0;j=c[J>>2]|0;if(+h[z+8>>3]<m+-20.0){c[(c[q+12>>2]|0)+(j<<2)>>2]=f;f=c[J>>2]|0;I=c[q+8>>2]|0;a[I+f>>0]=a[I+(f+-2)>>0]|0;f=d+52|0;I=c[f>>2]|0;j=d+56|0;c[f>>2]=c[j>>2];c[j>>2]=I;j=0;while(1){if((j|0)==(L|0))break;H=L+j|0;dc(k+(((c[f>>2]|0)+j|0)*1040|0)|0,E+(H*1040|0)|0,1040)|0;I=l+(j<<3)|0;h[l+(H<<3)>>3]=+h[I>>3];h[I>>3]=+h[B+(H<<3)>>3];I=(c[A>>2]|0)+j|0;ac(k+(I*1040|0)|0,0,1024)|0;c[k+(I*1040|0)+1024>>2]=0;h[k+(I*1040|0)+1032>>3]=s;j=j+1|0}c[J>>2]=(c[J>>2]|0)+1;c[M>>2]=0;c[d+64>>2]=0;c[d+40>>2]=c[F>>2];break}j=(c[q+12>>2]|0)+(j+-1<<2)|0;c[j>>2]=(c[j>>2]|0)+f;f=d+52|0;j=0;while(1){if((j|0)==(L|0))break;dc(k+(((c[f>>2]|0)+j|0)*1040|0)|0,E+(j*1040|0)|0,1040)|0;m=+h[B+(j<<3)>>3];h[l+(j<<3)>>3]=m;if((c[q>>2]|0)==1)h[l+(L+j<<3)>>3]=m;I=(c[A>>2]|0)+j|0;ac(k+(I*1040|0)|0,0,1024)|0;c[k+(I*1040|0)+1024>>2]=0;h[k+(I*1040|0)+1032>>3]=s;j=j+1|0}c[M>>2]=0;I=d+64|0;M=(c[I>>2]|0)+1|0;c[I>>2]=M;if(M>>>0>1){M=d+40|0;c[M>>2]=(c[M>>2]|0)+(c[F>>2]|0)}}while(0);M=b+4|0;ra[c[M>>2]&1](c[C>>2]|0,B);ra[c[M>>2]&1](c[C>>2]|0,E);ra[c[M>>2]&1](c[C>>2]|0,D)}}else{u=K;c[c[u+12>>2]>>2]=f;a[c[u+8>>2]>>0]=0;t=k;r=0;while(1){if((r|0)==(L|0))break;b=c[d>>2]|0;f=t+(r*1040|0)|0;q=t+(r*1040|0)+(b<<2)|0;if(!(b&1)){m=0.0;k=0}else{o=0.0;k=0;G=12}while(1){if((G|0)==12){G=0;j=c[f>>2]|0;m=+(j>>>0);if(j>>>0<256)n=+g[19516+(j<<2)>>2];else n=+Xb(m);f=f+4|0;m=o-m*n;k=k+j|0}if(f>>>0>=q>>>0)break;j=c[f>>2]|0;n=+(j>>>0);if(j>>>0<256)o=+g[19516+(j<<2)>>2];else o=+Xb(n);f=f+4|0;o=m-n*o;k=k+j|0;G=12}p=+(k>>>0);if(!k)o=p;else{if(k>>>0<256)n=+g[19516+(k<<2)>>2];else n=+Xb(p);o=p;m=m+p*n}y=m<o?o:m;h[l+(r<<3)>>3]=y;h[l+(L+r<<3)>>3]=y;r=r+1|0}c[J>>2]=(c[J>>2]|0)+1;c[u>>2]=(c[u>>2]|0)+1;G=d+48|0;l=(c[G>>2]|0)+L|0;c[G>>2]=l;b:do if(l>>>0<(c[c[d+36>>2]>>2]|0)>>>0){k=c[H>>2]|0;f=c[I>>2]|0;j=0;while(1){if((j|0)==(f|0))break b;I=l+j|0;ac(k+(I*1040|0)|0,0,1024)|0;c[k+(I*1040|0)+1024>>2]=0;h[k+(I*1040|0)+1032>>3]=s;j=j+1|0}}while(0);c[M>>2]=0}if(!e){i=N;return}e=K;M=_(c[e>>2]|0,L)|0;c[c[d+36>>2]>>2]=M;c[e+4>>2]=c[J>>2];i=N;return}function Vb(f,g,h,i){f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0;v=f;k=b[88430+((_(d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24,506832829)|0)>>>17<<1)>>1]|0;D=f+1|0;B=D;l=k<<16>>16==0&1;j=0;k=k&65535;a:while(1){if(l<<24>>24)break;w=k+1|0;m=153966+(k<<2)|0;m=e[m>>1]|e[m+2>>1]<<16;k=m>>>8&255;s=m>>>16;t=m&127;u=d[280786+t>>0]|0;r=1<<u;l=(m&255)>>>7;if(k<<24>>24){r=k<<24>>24!=10;if(t>>>0>h>>>0){A=j;k=w;j=A;continue}q=(c[11272+(t<<2)>>2]|0)+(_(t,m>>>16)|0)|0;k=280811+q|0;b:do switch((m>>>8&255)<<24>>24){case 0:{p=f+t|0;m=f+(t+-4)|0;o=0;k=v;while(1){n=k;if(n>>>0>m>>>0)break;z=k;A=280811+(q+o)|0;if((d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24|0)!=(d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0))break;o=o+4|0;k=n+4|0}while(1){if(k>>>0>=p>>>0)break;if((a[280811+(q+o)>>0]|0)!=(a[k>>0]|0))break;o=o+1|0;k=k+1|0}if((o|0)!=(t|0)){A=j;k=w;j=A;continue a}break}case 10:{k=a[k>>0]|0;if(!((k&255)>96&(k&255)<123)){A=j;k=w;j=A;continue a}if((k&255^32|0)!=(d[f>>0]|0)){A=j;k=w;j=A;continue a}p=q+1|0;q=f+t|0;m=f+(t+-4)|0;o=0;k=B;while(1){n=k;if(n>>>0>m>>>0)break;z=k;A=280811+(p+o)|0;if((d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24|0)!=(d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0))break;o=o+4|0;k=n+4|0}while(1){if(k>>>0>=q>>>0)break;if((a[280811+(p+o)>>0]|0)!=(a[k>>0]|0))break;o=o+1|0;k=k+1|0}if((o|0)!=(t+-1|0)){A=j;k=w;j=A;continue a}break}default:{m=0;while(1){if(m>>>0>=t>>>0)break b;k=a[280811+(q+m)>>0]|0;if((k&255)>96&(k&255)<123){if((k&255^32|0)!=(d[f+m>>0]|0)){A=j;k=w;j=A;continue a}}else if(k<<24>>24!=(a[f+m>>0]|0)){A=j;k=w;j=A;continue a}m=m+1|0}}}while(0);A=(s+((r?44:9)<<u)<<5)+t|0;j=i+(t<<2)|0;z=c[j>>2]|0;c[j>>2]=z>>>0<A>>>0?z:A;j=t+1|0;if(j>>>0>=h>>>0){j=1;k=w;continue}switch(a[f+t>>0]|0){case 32:{k=(s+(((r&1)<<6|4)<<u)<<5)+t|0;j=i+(j<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 34:{z=(s+((r?87:66)<<u)<<5)+t|0;A=i+(j<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=62){j=1;k=w;continue a}k=(s+((r?97:69)<<u)<<5)+t|0;j=i+(t+2<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 46:{z=(s+((r?101:79)<<u)<<5)+t|0;A=i+(j<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=32){j=1;k=w;continue a}k=(s+((r?114:88)<<u)<<5)+t|0;j=i+(t+2<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 44:{z=(s+((r?112:99)<<u)<<5)+t|0;A=i+(j<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=32){j=1;k=w;continue a}k=(s+((r?107:58)<<u)<<5)+t|0;j=i+(t+2<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 39:{k=(s+((r?94:74)<<u)<<5)+t|0;j=i+(j<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 40:{k=(s+((r?113:78)<<u)<<5)+t|0;j=i+(j<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 61:switch(a[f+(t+1)>>0]|0){case 34:{k=(s+((r&1|104)<<u)<<5)+t|0;j=i+(t+2<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}case 39:{k=(s+(((r&1)<<3)+108<<u)<<5)+t|0;j=i+(t+2<<2)|0;A=c[j>>2]|0;c[j>>2]=A>>>0<k>>>0?A:k;j=1;k=w;continue a}default:{j=1;k=w;continue a}}default:{j=1;k=w;continue a}}}p=(c[11272+(t<<2)>>2]|0)+(_(t,s)|0)|0;m=t>>>0<h>>>0?t:h;q=f+m|0;m=f+(m+-4)|0;o=0;k=v;while(1){n=k;if(n>>>0>m>>>0)break;z=k;A=280811+(p+o)|0;if((d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24|0)!=(d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24|0))break;o=o+4|0;k=n+4|0}while(1){if(k>>>0>=q>>>0)break;if((a[280811+(p+o)>>0]|0)!=(a[k>>0]|0))break;o=o+1|0;k=k+1|0}if((o|0)==(t|0)){A=(s<<5)+t|0;j=i+(t<<2)|0;z=c[j>>2]|0;c[j>>2]=z>>>0<A>>>0?z:A;j=1}k=t+-1|0;if(o>>>0>=k>>>0){z=(s+(12<<u)<<5)+t|0;A=i+(k<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;A=t+2|0;if((((A>>>0<h>>>0?(a[f+k>>0]|0)==105:0)?(a[f+t>>0]|0)==110:0)?(a[f+(t+1)>>0]|0)==103:0)?(a[f+A>>0]|0)==32:0){A=(s+(49<<u)<<5)+t|0;j=i+(t+3<<2)|0;z=c[j>>2]|0;c[j>>2]=z>>>0<A>>>0?z:A;j=1}else j=1}if(t>>>0>9){k=t+-9|0;k=k>>>0<g>>>0?g:k}else k=g;m=t+-2|0;m=o>>>0<m>>>0?o:m;while(1){if(k>>>0>m>>>0)break;A=(s+(d[407930+(t-k)>>0]<<u)<<5)+t|0;j=i+(k<<2)|0;z=c[j>>2]|0;c[j>>2]=z>>>0<A>>>0?z:A;j=1;k=k+1|0}if(o>>>0<t>>>0){k=w;continue}k=t+6|0;if(k>>>0>=h>>>0){k=w;continue}do switch(a[f+t>>0]|0){case 32:{z=(s+r<<5)+t|0;A=i+(t+1<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;switch(a[f+(t+1)>>0]|0){case 97:switch(a[f+(t+2)>>0]|0){case 32:{A=(s+(28<<u)<<5)+t|0;k=i+(t+3<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 115:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(46<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 116:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(60<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 110:{if((a[f+(t+3)>>0]|0)!=100){k=w;continue a}if((a[f+(t+4)>>0]|0)!=32){k=w;continue a}A=(s+(10<<u)<<5)+t|0;k=i+(t+5<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}case 98:{if((a[f+(t+2)>>0]|0)!=121){k=w;continue a}if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(38<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 105:switch(a[f+(t+2)>>0]|0){case 110:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(r<<4)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 115:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(47<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}case 102:{switch(a[f+(t+2)>>0]|0){case 111:{if((a[f+(t+3)>>0]|0)!=114){k=w;continue a}if((a[f+(t+4)>>0]|0)!=32){k=w;continue a}A=(s+(25<<u)<<5)+t|0;k=i+(t+5<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 114:break;default:{k=w;continue a}}if((a[f+(t+3)>>0]|0)!=111){k=w;continue a}if((a[f+(t+4)>>0]|0)!=109){k=w;continue a}if((a[f+(t+5)>>0]|0)!=32){k=w;continue a}A=(s+(37<<u)<<5)+t|0;k=i+(k<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 111:switch(a[f+(t+2)>>0]|0){case 102:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(r<<3)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 110:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(45<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}case 110:{if((a[f+(t+2)>>0]|0)!=111){k=w;continue a}if((a[f+(t+3)>>0]|0)!=116){k=w;continue a}if((a[f+(t+4)>>0]|0)!=32){k=w;continue a}A=(s+(80<<u)<<5)+t|0;k=i+(t+5<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 116:{switch(a[f+(t+2)>>0]|0){case 104:break;case 111:{if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(17<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}switch(a[f+(t+3)>>0]|0){case 101:{if((a[f+(t+4)>>0]|0)!=32){k=w;continue a}A=(s+(5<<u)<<5)+t|0;k=i+(t+5<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 97:break;default:{k=w;continue a}}if((a[f+(t+4)>>0]|0)!=116){k=w;continue a}if((a[f+(t+5)>>0]|0)!=32){k=w;continue a}A=(s+(29<<u)<<5)+t|0;k=i+(k<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 119:{if((a[f+(t+2)>>0]|0)!=105){k=w;continue a}if((a[f+(t+3)>>0]|0)!=116){k=w;continue a}if((a[f+(t+4)>>0]|0)!=104){k=w;continue a}if((a[f+(t+5)>>0]|0)!=32){k=w;continue a}A=(s+(35<<u)<<5)+t|0;k=i+(k<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}}case 34:{z=(s+(19<<u)<<5)+t|0;A=i+(t+1<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=62){k=w;continue a}A=(s+(21<<u)<<5)+t|0;k=i+(t+2<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 46:{z=(s+(20<<u)<<5)+t|0;A=i+(t+1<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=32){k=w;continue a}z=(s+(31<<u)<<5)+t|0;A=i+(t+2<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+2)>>0]|0)!=84){k=w;continue a}if((a[f+(t+3)>>0]|0)!=104){k=w;continue a}switch(a[f+(t+4)>>0]|0){case 101:{if((a[f+(t+5)>>0]|0)!=32){k=w;continue a}A=(s+(43<<u)<<5)+t|0;k=i+(k<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 105:break;default:{k=w;continue a}}if((a[f+(t+5)>>0]|0)!=115){k=w;continue a}if((a[f+(t+6)>>0]|0)!=32){k=w;continue a}A=(s+(75<<u)<<5)+t|0;k=i+(t+7<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 44:{z=(s+(76<<u)<<5)+t|0;A=i+(t+1<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=32){k=w;continue a}A=(s+(14<<u)<<5)+t|0;k=i+(t+2<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 10:{z=(s+(22<<u)<<5)+t|0;A=i+(t+1<<2)|0;y=c[A>>2]|0;c[A>>2]=y>>>0<z>>>0?y:z;if((a[f+(t+1)>>0]|0)!=9){k=w;continue a}A=(s+(50<<u)<<5)+t|0;k=i+(t+2<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 93:{A=(s+(24<<u)<<5)+t|0;k=i+(t+1<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 39:{A=(s+(36<<u)<<5)+t|0;k=i+(t+1<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 58:{A=(s+(51<<u)<<5)+t|0;k=i+(t+1<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 40:{A=(s+(57<<u)<<5)+t|0;k=i+(t+1<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 61:switch(a[f+(t+1)>>0]|0){case 34:{A=(s+(70<<u)<<5)+t|0;k=i+(t+2<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 39:{A=(s+(86<<u)<<5)+t|0;k=i+(t+2<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}case 97:{if((a[f+(t+1)>>0]|0)!=108){k=w;continue a}if((a[f+(t+2)>>0]|0)!=32){k=w;continue a}A=(s+(84<<u)<<5)+t|0;k=i+(t+3<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 101:switch(a[f+(t+1)>>0]|0){case 100:{if((a[f+(t+2)>>0]|0)!=32){k=w;continue a}A=(s+(53<<u)<<5)+t|0;k=i+(t+3<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 114:{if((a[f+(t+2)>>0]|0)!=32){k=w;continue a}A=(s+(82<<u)<<5)+t|0;k=i+(t+3<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 115:{if((a[f+(t+2)>>0]|0)!=116){k=w;continue a}if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(95<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}case 102:{if((a[f+(t+1)>>0]|0)!=117){k=w;continue a}if((a[f+(t+2)>>0]|0)!=108){k=w;continue a}if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(90<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 105:switch(a[f+(t+1)>>0]|0){case 118:{if((a[f+(t+2)>>0]|0)!=101){k=w;continue a}if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(92<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 122:{if((a[f+(t+2)>>0]|0)!=101){k=w;continue a}if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(100<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}case 108:{switch(a[f+(t+1)>>0]|0){case 101:break;case 121:{if((a[f+(t+2)>>0]|0)!=32){k=w;continue a}A=(s+(61<<u)<<5)+t|0;k=i+(t+3<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}if((a[f+(t+2)>>0]|0)!=115){k=w;continue a}if((a[f+(t+3)>>0]|0)!=115){k=w;continue a}if((a[f+(t+4)>>0]|0)!=32){k=w;continue a}A=(s+(93<<u)<<5)+t|0;k=i+(t+5<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}case 111:{if((a[f+(t+1)>>0]|0)!=117){k=w;continue a}if((a[f+(t+2)>>0]|0)!=115){k=w;continue a}if((a[f+(t+3)>>0]|0)!=32){k=w;continue a}A=(s+(106<<u)<<5)+t|0;k=i+(t+4<<2)|0;z=c[k>>2]|0;c[k>>2]=z>>>0<A>>>0?z:A;k=w;continue a}default:{k=w;continue a}}while(0)}if(h>>>0<=4){h=j;h=h&1;h=h<<24>>24!=0;return h|0}A=a[f>>0]|0;w=A<<24>>24==32;c:do switch(A<<24>>24){case 32:case 46:{k=b[88430+((_(d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24,506832829)|0)>>>17<<1)>>1]|0;g=h+-1|0;x=w?6:32;y=w?2:77;z=w?89:67;A=f+2|0;l=k<<16>>16==0&1;k=k&65535;d:while(1){if(l<<24>>24)break c;v=k+1|0;m=153966+(k<<2)|0;m=e[m>>1]|e[m+2>>1]<<16;k=m>>>8&255;s=m>>>16;t=m&127;u=d[280786+t>>0]|0;l=(m&255)>>>7;if(!(k<<24>>24)){if(t>>>0>g>>>0){u=j;k=v;j=u;continue}p=(c[11272+(t<<2)>>2]|0)+(_(t,m>>>16)|0)|0;q=f+(t+1)|0;m=f+(t+-3)|0;o=0;k=B;while(1){n=k;if(n>>>0>m>>>0)break;E=k;r=280811+(p+o)|0;if((d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0)!=(d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24|0))break;o=o+4|0;k=n+4|0}while(1){if(k>>>0>=q>>>0)break;if((a[280811+(p+o)>>0]|0)!=(a[k>>0]|0))break;o=o+1|0;k=k+1|0}if((o|0)!=(t|0)){E=j;k=v;j=E;continue}j=t+1|0;E=(s+(x<<u)<<5)+t|0;k=i+(j<<2)|0;r=c[k>>2]|0;c[k>>2]=r>>>0<E>>>0?r:E;k=t+2|0;if(k>>>0>=h>>>0){j=1;k=v;continue}j=a[f+j>>0]|0;switch(j<<24>>24){case 32:{E=(s+(y<<u)<<5)+t|0;j=i+(k<<2)|0;k=c[j>>2]|0;c[j>>2]=k>>>0<E>>>0?k:E;j=1;k=v;continue d}case 40:{E=(s+(z<<u)<<5)+t|0;j=i+(k<<2)|0;k=c[j>>2]|0;c[j>>2]=k>>>0<E>>>0?k:E;j=1;k=v;continue d}default:{if(!w){j=1;k=v;continue d}switch(j<<24>>24){case 44:{r=(s+(103<<u)<<5)+t|0;E=i+(k<<2)|0;q=c[E>>2]|0;c[E>>2]=q>>>0<r>>>0?q:r;if((a[f+(t+2)>>0]|0)!=32){j=1;k=v;continue d}k=(s+(33<<u)<<5)+t|0;j=i+(t+3<<2)|0;E=c[j>>2]|0;c[j>>2]=E>>>0<k>>>0?E:k;j=1;k=v;continue d}case 46:{r=(s+(71<<u)<<5)+t|0;E=i+(k<<2)|0;q=c[E>>2]|0;c[E>>2]=q>>>0<r>>>0?q:r;if((a[f+(t+2)>>0]|0)!=32){j=1;k=v;continue d}k=(s+(52<<u)<<5)+t|0;j=i+(t+3<<2)|0;E=c[j>>2]|0;c[j>>2]=E>>>0<k>>>0?E:k;j=1;k=v;continue d}case 61:switch(a[f+(t+2)>>0]|0){case 34:{k=(s+(81<<u)<<5)+t|0;j=i+(t+3<<2)|0;E=c[j>>2]|0;c[j>>2]=E>>>0<k>>>0?E:k;j=1;k=v;continue d}case 39:{k=(s+(98<<u)<<5)+t|0;j=i+(t+3<<2)|0;E=c[j>>2]|0;c[j>>2]=E>>>0<k>>>0?E:k;j=1;k=v;continue d}default:{j=1;k=v;continue d}}default:{j=1;k=v;continue d}}}}}if(!w){E=j;k=v;j=E;continue}r=k<<24>>24!=10;if(t>>>0>g>>>0){E=j;k=v;j=E;continue}q=(c[11272+(t<<2)>>2]|0)+(_(t,m>>>16)|0)|0;k=280811+q|0;e:do switch((m>>>8&255)<<24>>24){case 0:{p=f+(t+1)|0;m=f+(t+-3)|0;o=0;k=B;while(1){n=k;if(n>>>0>m>>>0)break;F=k;E=280811+(q+o)|0;if((d[F>>0]|d[F+1>>0]<<8|d[F+2>>0]<<16|d[F+3>>0]<<24|0)!=(d[E>>0]|d[E+1>>0]<<8|d[E+2>>0]<<16|d[E+3>>0]<<24|0))break;o=o+4|0;k=n+4|0}while(1){if(k>>>0>=p>>>0)break;if((a[280811+(q+o)>>0]|0)!=(a[k>>0]|0))break;o=o+1|0;k=k+1|0}if((o|0)!=(t|0)){F=j;k=v;j=F;continue d}break}case 10:{k=a[k>>0]|0;if(!((k&255)>96&(k&255)<123)){F=j;k=v;j=F;continue d}if((k&255^32|0)!=(d[D>>0]|0)){F=j;k=v;j=F;continue d}o=q+1|0;p=f+(t+1)|0;m=f+(t+-3)|0;n=0;k=A;while(1){if(k>>>0>m>>>0)break;F=280811+(o+n)|0;if((d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24|0)!=(d[F>>0]|d[F+1>>0]<<8|d[F+2>>0]<<16|d[F+3>>0]<<24|0))break;n=n+4|0;k=k+4|0}while(1){if(k>>>0>=p>>>0)break;if((a[280811+(o+n)>>0]|0)!=(a[k>>0]|0))break;n=n+1|0;k=k+1|0}if((n|0)!=(t+-1|0)){F=j;k=v;j=F;continue d}break}default:{m=0;while(1){if(m>>>0>=t>>>0)break e;k=a[280811+(q+m)>>0]|0;if((k&255)>96&(k&255)<123){if((k&255^32|0)!=(d[f+(m+1)>>0]|0)){F=j;k=v;j=F;continue d}}else if(k<<24>>24!=(a[f+(m+1)>>0]|0)){F=j;k=v;j=F;continue d}m=m+1|0}}}while(0);j=t+1|0;F=(s+((r?85:30)<<u)<<5)+t|0;k=i+(j<<2)|0;E=c[k>>2]|0;c[k>>2]=E>>>0<F>>>0?E:F;k=t+2|0;if(k>>>0>=h>>>0){j=1;k=v;continue}switch(a[f+j>>0]|0){case 32:{F=(s+((r?83:15)<<u)<<5)+t|0;j=i+(k<<2)|0;k=c[j>>2]|0;c[j>>2]=k>>>0<F>>>0?k:F;j=1;k=v;continue d}case 44:{if(!r){E=(s+(109<<u)<<5)+t|0;F=i+(k<<2)|0;q=c[F>>2]|0;c[F>>2]=q>>>0<E>>>0?q:E}if((a[f+(t+2)>>0]|0)!=32){j=1;k=v;continue d}k=(s+((r?111:65)<<u)<<5)+t|0;j=i+(t+3<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=v;continue d}case 46:{E=(s+((r?115:96)<<u)<<5)+t|0;F=i+(k<<2)|0;q=c[F>>2]|0;c[F>>2]=q>>>0<E>>>0?q:E;if((a[f+(t+2)>>0]|0)!=32){j=1;k=v;continue d}k=(s+((r?117:91)<<u)<<5)+t|0;j=i+(t+3<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=v;continue d}case 61:switch(a[f+(t+2)>>0]|0){case 34:{k=(s+(((r&1)<<3^8)+110<<u)<<5)+t|0;j=i+(t+3<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=v;continue d}case 39:{k=(s+(120-(r&1)<<u)<<5)+t|0;j=i+(t+3<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=v;continue d}default:{j=1;k=v;continue d}}default:{j=1;k=v;continue d}}}}default:{}}while(0);if(h>>>0<=5){F=j;F=F&1;F=F<<24>>24!=0;return F|0}k=a[D>>0]|0;l=a[f>>0]|0;f:do if(k<<24>>24==32){switch(l<<24>>24){case 44:case 115:case 101:{C=241;break f}case -62:break;default:break f}if(k<<24>>24==-96)C=241}else if(l<<24>>24==-62&k<<24>>24==-96)C=241;while(0);g:do if((C|0)==241){u=f+2|0;k=b[88430+((_(d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24,506832829)|0)>>>17<<1)>>1]|0;v=h+-2|0;l=k<<16>>16==0&1;k=k&65535;while(1){if(l<<24>>24)break g;t=k+1|0;k=153966+(k<<2)|0;k=e[k>>1]|e[k+2>>1]<<16;q=k>>>16;r=k&127;s=d[280786+r>>0]|0;l=(k&255)>>>7;if((k&65280|0)!=0|r>>>0>v>>>0){F=j;k=t;j=F;continue}o=(c[11272+(r<<2)>>2]|0)+(_(r,k>>>16)|0)|0;p=f+(r+2)|0;m=f+(r+-2)|0;n=0;k=u;while(1){if(k>>>0>m>>>0)break;F=280811+(o+n)|0;if((d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24|0)!=(d[F>>0]|d[F+1>>0]<<8|d[F+2>>0]<<16|d[F+3>>0]<<24|0))break;n=n+4|0;k=k+4|0}while(1){if(k>>>0>=p>>>0)break;if((a[280811+(o+n)>>0]|0)!=(a[k>>0]|0))break;n=n+1|0;k=k+1|0}if((n|0)!=(r|0)){F=j;k=t;j=F;continue}k=a[f>>0]|0;if(k<<24>>24==-62){k=(q+(102<<s)<<5)+r|0;j=i+(r+2<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=t;continue}m=r+2|0;if(m>>>0>=h>>>0){F=j;k=t;j=F;continue}if((a[f+m>>0]|0)!=32){F=j;k=t;j=F;continue}k=(q+((k<<24>>24==101?18:k<<24>>24==115?7:13)<<s)<<5)+r|0;j=i+(r+3<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=t}}while(0);if(h>>>0<=8){F=j;F=F&1;F=F<<24>>24!=0;return F|0}switch(a[f>>0]|0){case 32:{if((a[D>>0]|0)!=116){F=j;F=F&1;F=F<<24>>24!=0;return F|0}if((a[f+2>>0]|0)!=104){F=j;F=F&1;F=F<<24>>24!=0;return F|0}if((a[f+3>>0]|0)!=101){F=j;F=F&1;F=F<<24>>24!=0;return F|0}if((a[f+4>>0]|0)!=32){F=j;F=F&1;F=F<<24>>24!=0;return F|0}break}case 46:{if((a[D>>0]|0)!=99){F=j;F=F&1;F=F<<24>>24!=0;return F|0}if((a[f+2>>0]|0)!=111){F=j;F=F&1;F=F<<24>>24!=0;return F|0}if((a[f+3>>0]|0)!=109){F=j;F=F&1;F=F<<24>>24!=0;return F|0}if((a[f+4>>0]|0)!=47){F=j;F=F&1;F=F<<24>>24!=0;return F|0}break}default:{F=j;F=F&1;F=F<<24>>24!=0;return F|0}}u=f+5|0;k=b[88430+((_(d[u>>0]|d[u+1>>0]<<8|d[u+2>>0]<<16|d[u+3>>0]<<24,506832829)|0)>>>17<<1)>>1]|0;v=h+-5|0;l=k<<16>>16==0&1;k=k&65535;while(1){if(l<<24>>24)break;t=k+1|0;k=153966+(k<<2)|0;k=e[k>>1]|e[k+2>>1]<<16;q=k>>>16;r=k&127;s=d[280786+r>>0]|0;l=(k&255)>>>7;if((k&65280|0)!=0|r>>>0>v>>>0){F=j;k=t;j=F;continue}o=(c[11272+(r<<2)>>2]|0)+(_(r,k>>>16)|0)|0;p=f+(r+5)|0;m=f+(r+1)|0;n=0;k=u;while(1){if(k>>>0>m>>>0)break;F=280811+(o+n)|0;if((d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24|0)!=(d[F>>0]|d[F+1>>0]<<8|d[F+2>>0]<<16|d[F+3>>0]<<24|0))break;n=n+4|0;k=k+4|0}while(1){if(k>>>0>=p>>>0)break;if((a[280811+(o+n)>>0]|0)!=(a[k>>0]|0))break;n=n+1|0;k=k+1|0}if((n|0)!=(r|0)){F=j;k=t;j=F;continue}j=r+5|0;E=(q+(((a[f>>0]|0)==32?41:72)<<s)<<5)+r|0;F=i+(j<<2)|0;D=c[F>>2]|0;c[F>>2]=D>>>0<E>>>0?D:E;if(j>>>0>=h>>>0){j=1;k=t;continue}if(!((a[f>>0]|0)==32&(r+8|0)>>>0<h>>>0)){j=1;k=t;continue}if((a[f+j>>0]|0)!=32){j=1;k=t;continue}if((a[f+(r+6)>>0]|0)!=111){j=1;k=t;continue}if((a[f+(r+7)>>0]|0)!=102){j=1;k=t;continue}if((a[f+(r+8)>>0]|0)!=32){j=1;k=t;continue}E=(q+(62<<s)<<5)+r|0;F=i+(r+9<<2)|0;D=c[F>>2]|0;c[F>>2]=D>>>0<E>>>0?D:E;if((r+12|0)>>>0>=h>>>0){j=1;k=t;continue}if((a[f+(r+9)>>0]|0)!=116){j=1;k=t;continue}if((a[f+(r+10)>>0]|0)!=104){j=1;k=t;continue}if((a[f+(r+11)>>0]|0)!=101){j=1;k=t;continue}if((a[f+(r+12)>>0]|0)!=32){j=1;k=t;continue}k=(q+(73<<s)<<5)+r|0;j=i+(r+13<<2)|0;F=c[j>>2]|0;c[j>>2]=F>>>0<k>>>0?F:k;j=1;k=t}F=j&1;F=F<<24>>24!=0;return F|0}function Wb(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;f=0;r=0;a:while(1){b:do{if(f>>>0>=e>>>0)break a;g=f+c&d;h=e-f|0;i=a[b+g>>0]|0;if(!(i<<24>>24<=-1|i<<24>>24==0)){g=1;s=20;break}do if(h>>>0>1){if(((i&-32)<<24>>24==-64?(j=a[b+(g+1)>>0]|0,(j&-64)<<24>>24==-128):0)?(k=(i&255)<<6&1984|j&63,k>>>0>127):0){h=2;g=k;break}if(h>>>0>2){if((((i&-16)<<24>>24==-32?(l=a[b+(g+1)>>0]|0,(l&-64)<<24>>24==-128):0)?(m=a[b+(g+2)>>0]|0,(m&-64)<<24>>24==-128):0)?(n=(i&255)<<12&61440|(l&255)<<6&4032|m&63,n>>>0>2047):0){h=3;g=n;break}if((((h>>>0>3&(i&-8)<<24>>24==-16?(o=a[b+(g+1)>>0]|0,(o&-64)<<24>>24==-128):0)?(p=a[b+(g+2)>>0]|0,(p&-64)<<24>>24==-128):0)?(q=a[b+(g+3)>>0]|0,(q&-64)<<24>>24==-128):0)?(s=(i&255)<<18&1835008|(o&255)<<12&258048|(p&255)<<6&4032|q&63,s>>>0>65535&s>>>0<1114112):0){g=4;s=20;break b}else s=19}else s=19}else s=19;while(0);if((s|0)==19){s=0;h=1;g=i&255|1114112}f=f+h|0}while((g|0)>=1114112);if((s|0)==20){s=0;h=g;f=f+g|0}r=r+h|0}return +(r>>>0)>+(e>>>0)*.75|0}function Xb(a){a=+a;var b=0,d=0,e=0,f=0,g=0.0,i=0.0,j=0.0,l=0.0,m=0.0;h[k>>3]=a;d=c[k>>2]|0;b=c[k+4>>2]|0;e=(b|0)<0;do if(e|b>>>0<1048576){if((d|0)==0&(b&2147483647|0)==0){a=-1.0/(a*a);break}if(e){a=(a-a)/0.0;break}else{h[k>>3]=a*18014398509481984.0;b=c[k+4>>2]|0;e=c[k>>2]|0;d=-1077;f=9;break}}else if(b>>>0<=2146435071)if((d|0)==0&0==0&(b|0)==1072693248)a=0.0;else{e=d;d=-1023;f=9}while(0);if((f|0)==9){f=b+614242|0;c[k>>2]=e;c[k+4>>2]=(f&1048575)+1072079006;l=+h[k>>3]+-1.0;a=l*(l*.5);m=l/(l+2.0);i=m*m;j=i*i;h[k>>3]=l-a;e=c[k+4>>2]|0;c[k>>2]=0;c[k+4>>2]=e;g=+h[k>>3];a=m*(a+(j*(j*(j*.15313837699209373+.22222198432149784)+.3999999999940942)+i*(j*(j*(j*.14798198605116586+.1818357216161805)+.2857142874366239)+.6666666666666735)))+(l-g-a);l=g*1.4426950407214463;j=+(d+(f>>>20)|0);i=j+l;a=i+(l+(j-i)+(a*1.4426950407214463+(g+a)*1.6751713164886512e-10))}return +a}function Yb(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0;do if(a>>>0<245){o=a>>>0<11?16:a+11&-8;a=o>>>3;i=c[5147]|0;b=i>>>a;if(b&3){b=(b&1^1)+a|0;e=b<<1;d=20628+(e<<2)|0;e=20628+(e+2<<2)|0;f=c[e>>2]|0;g=f+8|0;h=c[g>>2]|0;do if((d|0)!=(h|0)){if(h>>>0<(c[5151]|0)>>>0)ha();a=h+12|0;if((c[a>>2]|0)==(f|0)){c[a>>2]=d;c[e>>2]=h;break}else ha()}else c[5147]=i&~(1<<b);while(0);L=b<<3;c[f+4>>2]=L|3;L=f+(L|4)|0;c[L>>2]=c[L>>2]|1;L=g;return L|0}h=c[5149]|0;if(o>>>0>h>>>0){if(b){e=2<<a;e=b<<a&(e|0-e);e=(e&0-e)+-1|0;j=e>>>12&16;e=e>>>j;f=e>>>5&8;e=e>>>f;g=e>>>2&4;e=e>>>g;d=e>>>1&2;e=e>>>d;b=e>>>1&1;b=(f|j|g|d|b)+(e>>>b)|0;e=b<<1;d=20628+(e<<2)|0;e=20628+(e+2<<2)|0;g=c[e>>2]|0;j=g+8|0;f=c[j>>2]|0;do if((d|0)!=(f|0)){if(f>>>0<(c[5151]|0)>>>0)ha();a=f+12|0;if((c[a>>2]|0)==(g|0)){c[a>>2]=d;c[e>>2]=f;k=c[5149]|0;break}else ha()}else{c[5147]=i&~(1<<b);k=h}while(0);L=b<<3;h=L-o|0;c[g+4>>2]=o|3;i=g+o|0;c[g+(o|4)>>2]=h|1;c[g+L>>2]=h;if(k){f=c[5152]|0;d=k>>>3;a=d<<1;e=20628+(a<<2)|0;b=c[5147]|0;d=1<<d;if(b&d){b=20628+(a+2<<2)|0;a=c[b>>2]|0;if(a>>>0<(c[5151]|0)>>>0)ha();else{l=b;m=a}}else{c[5147]=b|d;l=20628+(a+2<<2)|0;m=e}c[l>>2]=f;c[m+12>>2]=f;c[f+8>>2]=m;c[f+12>>2]=e}c[5149]=h;c[5152]=i;L=j;return L|0}a=c[5148]|0;if(a){i=(a&0-a)+-1|0;K=i>>>12&16;i=i>>>K;J=i>>>5&8;i=i>>>J;L=i>>>2&4;i=i>>>L;b=i>>>1&2;i=i>>>b;j=i>>>1&1;j=c[20892+((J|K|L|b|j)+(i>>>j)<<2)>>2]|0;i=(c[j+4>>2]&-8)-o|0;b=j;while(1){a=c[b+16>>2]|0;if(!a){a=c[b+20>>2]|0;if(!a)break}b=(c[a+4>>2]&-8)-o|0;L=b>>>0<i>>>0;i=L?b:i;b=a;j=L?a:j}f=c[5151]|0;if(j>>>0<f>>>0)ha();h=j+o|0;if(j>>>0>=h>>>0)ha();g=c[j+24>>2]|0;d=c[j+12>>2]|0;do if((d|0)==(j|0)){b=j+20|0;a=c[b>>2]|0;if(!a){b=j+16|0;a=c[b>>2]|0;if(!a){n=0;break}}while(1){d=a+20|0;e=c[d>>2]|0;if(e){a=e;b=d;continue}d=a+16|0;e=c[d>>2]|0;if(!e)break;else{a=e;b=d}}if(b>>>0<f>>>0)ha();else{c[b>>2]=0;n=a;break}}else{e=c[j+8>>2]|0;if(e>>>0<f>>>0)ha();a=e+12|0;if((c[a>>2]|0)!=(j|0))ha();b=d+8|0;if((c[b>>2]|0)==(j|0)){c[a>>2]=d;c[b>>2]=e;n=d;break}else ha()}while(0);do if(g){a=c[j+28>>2]|0;b=20892+(a<<2)|0;if((j|0)==(c[b>>2]|0)){c[b>>2]=n;if(!n){c[5148]=c[5148]&~(1<<a);break}}else{if(g>>>0<(c[5151]|0)>>>0)ha();a=g+16|0;if((c[a>>2]|0)==(j|0))c[a>>2]=n;else c[g+20>>2]=n;if(!n)break}b=c[5151]|0;if(n>>>0<b>>>0)ha();c[n+24>>2]=g;a=c[j+16>>2]|0;do if(a)if(a>>>0<b>>>0)ha();else{c[n+16>>2]=a;c[a+24>>2]=n;break}while(0);a=c[j+20>>2]|0;if(a)if(a>>>0<(c[5151]|0)>>>0)ha();else{c[n+20>>2]=a;c[a+24>>2]=n;break}}while(0);if(i>>>0<16){L=i+o|0;c[j+4>>2]=L|3;L=j+(L+4)|0;c[L>>2]=c[L>>2]|1}else{c[j+4>>2]=o|3;c[j+(o|4)>>2]=i|1;c[j+(i+o)>>2]=i;a=c[5149]|0;if(a){f=c[5152]|0;d=a>>>3;a=d<<1;e=20628+(a<<2)|0;b=c[5147]|0;d=1<<d;if(b&d){a=20628+(a+2<<2)|0;b=c[a>>2]|0;if(b>>>0<(c[5151]|0)>>>0)ha();else{p=a;q=b}}else{c[5147]=b|d;p=20628+(a+2<<2)|0;q=e}c[p>>2]=f;c[q+12>>2]=f;c[f+8>>2]=q;c[f+12>>2]=e}c[5149]=i;c[5152]=h}L=j+8|0;return L|0}}}else if(a>>>0<=4294967231){a=a+11|0;o=a&-8;j=c[5148]|0;if(j){b=0-o|0;a=a>>>8;if(a)if(o>>>0>16777215)i=31;else{q=(a+1048320|0)>>>16&8;x=a<<q;p=(x+520192|0)>>>16&4;x=x<<p;i=(x+245760|0)>>>16&2;i=14-(p|q|i)+(x<<i>>>15)|0;i=o>>>(i+7|0)&1|i<<1}else i=0;a=c[20892+(i<<2)>>2]|0;a:do if(!a){d=0;a=0;x=86}else{f=b;d=0;g=o<<((i|0)==31?0:25-(i>>>1)|0);h=a;a=0;while(1){e=c[h+4>>2]&-8;b=e-o|0;if(b>>>0<f>>>0)if((e|0)==(o|0)){e=h;a=h;x=90;break a}else a=h;else b=f;x=c[h+20>>2]|0;h=c[h+16+(g>>>31<<2)>>2]|0;d=(x|0)==0|(x|0)==(h|0)?d:x;if(!h){x=86;break}else{f=b;g=g<<1}}}while(0);if((x|0)==86){if((d|0)==0&(a|0)==0){a=2<<i;a=j&(a|0-a);if(!a)break;a=(a&0-a)+-1|0;n=a>>>12&16;a=a>>>n;m=a>>>5&8;a=a>>>m;p=a>>>2&4;a=a>>>p;q=a>>>1&2;a=a>>>q;d=a>>>1&1;d=c[20892+((m|n|p|q|d)+(a>>>d)<<2)>>2]|0;a=0}if(!d){i=b;j=a}else{e=d;x=90}}if((x|0)==90)while(1){x=0;q=(c[e+4>>2]&-8)-o|0;d=q>>>0<b>>>0;b=d?q:b;a=d?e:a;d=c[e+16>>2]|0;if(d){e=d;x=90;continue}e=c[e+20>>2]|0;if(!e){i=b;j=a;break}else x=90}if((j|0)!=0?i>>>0<((c[5149]|0)-o|0)>>>0:0){f=c[5151]|0;if(j>>>0<f>>>0)ha();h=j+o|0;if(j>>>0>=h>>>0)ha();g=c[j+24>>2]|0;d=c[j+12>>2]|0;do if((d|0)==(j|0)){b=j+20|0;a=c[b>>2]|0;if(!a){b=j+16|0;a=c[b>>2]|0;if(!a){s=0;break}}while(1){d=a+20|0;e=c[d>>2]|0;if(e){a=e;b=d;continue}d=a+16|0;e=c[d>>2]|0;if(!e)break;else{a=e;b=d}}if(b>>>0<f>>>0)ha();else{c[b>>2]=0;s=a;break}}else{e=c[j+8>>2]|0;if(e>>>0<f>>>0)ha();a=e+12|0;if((c[a>>2]|0)!=(j|0))ha();b=d+8|0;if((c[b>>2]|0)==(j|0)){c[a>>2]=d;c[b>>2]=e;s=d;break}else ha()}while(0);do if(g){a=c[j+28>>2]|0;b=20892+(a<<2)|0;if((j|0)==(c[b>>2]|0)){c[b>>2]=s;if(!s){c[5148]=c[5148]&~(1<<a);break}}else{if(g>>>0<(c[5151]|0)>>>0)ha();a=g+16|0;if((c[a>>2]|0)==(j|0))c[a>>2]=s;else c[g+20>>2]=s;if(!s)break}b=c[5151]|0;if(s>>>0<b>>>0)ha();c[s+24>>2]=g;a=c[j+16>>2]|0;do if(a)if(a>>>0<b>>>0)ha();else{c[s+16>>2]=a;c[a+24>>2]=s;break}while(0);a=c[j+20>>2]|0;if(a)if(a>>>0<(c[5151]|0)>>>0)ha();else{c[s+20>>2]=a;c[a+24>>2]=s;break}}while(0);b:do if(i>>>0>=16){c[j+4>>2]=o|3;c[j+(o|4)>>2]=i|1;c[j+(i+o)>>2]=i;a=i>>>3;if(i>>>0<256){b=a<<1;e=20628+(b<<2)|0;d=c[5147]|0;a=1<<a;if(d&a){a=20628+(b+2<<2)|0;b=c[a>>2]|0;if(b>>>0<(c[5151]|0)>>>0)ha();else{t=a;u=b}}else{c[5147]=d|a;t=20628+(b+2<<2)|0;u=e}c[t>>2]=h;c[u+12>>2]=h;c[j+(o+8)>>2]=u;c[j+(o+12)>>2]=e;break}a=i>>>8;if(a)if(i>>>0>16777215)e=31;else{K=(a+1048320|0)>>>16&8;L=a<<K;J=(L+520192|0)>>>16&4;L=L<<J;e=(L+245760|0)>>>16&2;e=14-(J|K|e)+(L<<e>>>15)|0;e=i>>>(e+7|0)&1|e<<1}else e=0;a=20892+(e<<2)|0;c[j+(o+28)>>2]=e;c[j+(o+20)>>2]=0;c[j+(o+16)>>2]=0;b=c[5148]|0;d=1<<e;if(!(b&d)){c[5148]=b|d;c[a>>2]=h;c[j+(o+24)>>2]=a;c[j+(o+12)>>2]=h;c[j+(o+8)>>2]=h;break}a=c[a>>2]|0;c:do if((c[a+4>>2]&-8|0)!=(i|0)){e=i<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b)break;if((c[b+4>>2]&-8|0)==(i|0)){w=b;break c}else{e=e<<1;a=b}}if(d>>>0<(c[5151]|0)>>>0)ha();else{c[d>>2]=h;c[j+(o+24)>>2]=a;c[j+(o+12)>>2]=h;c[j+(o+8)>>2]=h;break b}}else w=a;while(0);a=w+8|0;b=c[a>>2]|0;L=c[5151]|0;if(b>>>0>=L>>>0&w>>>0>=L>>>0){c[b+12>>2]=h;c[a>>2]=h;c[j+(o+8)>>2]=b;c[j+(o+12)>>2]=w;c[j+(o+24)>>2]=0;break}else ha()}else{L=i+o|0;c[j+4>>2]=L|3;L=j+(L+4)|0;c[L>>2]=c[L>>2]|1}while(0);L=j+8|0;return L|0}}}else o=-1;while(0);d=c[5149]|0;if(d>>>0>=o>>>0){a=d-o|0;b=c[5152]|0;if(a>>>0>15){c[5152]=b+o;c[5149]=a;c[b+(o+4)>>2]=a|1;c[b+d>>2]=a;c[b+4>>2]=o|3}else{c[5149]=0;c[5152]=0;c[b+4>>2]=d|3;L=b+(d+4)|0;c[L>>2]=c[L>>2]|1}L=b+8|0;return L|0}a=c[5150]|0;if(a>>>0>o>>>0){K=a-o|0;c[5150]=K;L=c[5153]|0;c[5153]=L+o;c[L+(o+4)>>2]=K|1;c[L+4>>2]=o|3;L=L+8|0;return L|0}do if(!(c[5265]|0)){a=ja(30)|0;if(!(a+-1&a)){c[5267]=a;c[5266]=a;c[5268]=-1;c[5269]=-1;c[5270]=0;c[5258]=0;c[5265]=(la(0)|0)&-16^1431655768;break}else ha()}while(0);g=o+48|0;f=c[5267]|0;h=o+47|0;e=f+h|0;f=0-f|0;i=e&f;if(i>>>0<=o>>>0){L=0;return L|0}a=c[5257]|0;if((a|0)!=0?(u=c[5255]|0,w=u+i|0,w>>>0<=u>>>0|w>>>0>a>>>0):0){L=0;return L|0}d:do if(!(c[5258]&4)){d=c[5153]|0;e:do if(d){a=21036;while(1){b=c[a>>2]|0;if(b>>>0<=d>>>0?(r=a+4|0,(b+(c[r>>2]|0)|0)>>>0>d>>>0):0)break;a=c[a+8>>2]|0;if(!a){x=174;break e}}b=e-(c[5150]|0)&f;if(b>>>0<2147483647){d=ka(b|0)|0;w=(d|0)==((c[a>>2]|0)+(c[r>>2]|0)|0);a=w?b:0;if(w){if((d|0)!=(-1|0)){r=d;q=a;x=194;break d}}else x=184}else a=0}else x=174;while(0);do if((x|0)==174){e=ka(0)|0;if((e|0)!=(-1|0)){a=e;b=c[5266]|0;d=b+-1|0;if(!(d&a))b=i;else b=i-a+(d+a&0-b)|0;a=c[5255]|0;d=a+b|0;if(b>>>0>o>>>0&b>>>0<2147483647){w=c[5257]|0;if((w|0)!=0?d>>>0<=a>>>0|d>>>0>w>>>0:0){a=0;break}d=ka(b|0)|0;x=(d|0)==(e|0);a=x?b:0;if(x){r=e;q=a;x=194;break d}else x=184}else a=0}else a=0}while(0);f:do if((x|0)==184){e=0-b|0;do if(g>>>0>b>>>0&(b>>>0<2147483647&(d|0)!=(-1|0))?(v=c[5267]|0,v=h-b+v&0-v,v>>>0<2147483647):0)if((ka(v|0)|0)==(-1|0)){ka(e|0)|0;break f}else{b=v+b|0;break}while(0);if((d|0)!=(-1|0)){r=d;q=b;x=194;break d}}while(0);c[5258]=c[5258]|4;x=191}else{a=0;x=191}while(0);if((((x|0)==191?i>>>0<2147483647:0)?(y=ka(i|0)|0,z=ka(0)|0,y>>>0<z>>>0&((y|0)!=(-1|0)&(z|0)!=(-1|0))):0)?(A=z-y|0,B=A>>>0>(o+40|0)>>>0,B):0){r=y;q=B?A:a;x=194}if((x|0)==194){a=(c[5255]|0)+q|0;c[5255]=a;if(a>>>0>(c[5256]|0)>>>0)c[5256]=a;h=c[5153]|0;g:do if(h){f=21036;while(1){a=c[f>>2]|0;b=f+4|0;d=c[b>>2]|0;if((r|0)==(a+d|0)){x=204;break}e=c[f+8>>2]|0;if(!e)break;else f=e}if(((x|0)==204?(c[f+12>>2]&8|0)==0:0)?h>>>0<r>>>0&h>>>0>=a>>>0:0){c[b>>2]=d+q;L=(c[5150]|0)+q|0;K=h+8|0;K=(K&7|0)==0?0:0-K&7;J=L-K|0;c[5153]=h+K;c[5150]=J;c[h+(K+4)>>2]=J|1;c[h+(L+4)>>2]=40;c[5154]=c[5269];break}a=c[5151]|0;if(r>>>0<a>>>0){c[5151]=r;j=r}else j=a;b=r+q|0;a=21036;while(1){if((c[a>>2]|0)==(b|0)){x=212;break}a=c[a+8>>2]|0;if(!a){b=21036;break}}if((x|0)==212)if(!(c[a+12>>2]&8)){c[a>>2]=r;n=a+4|0;c[n>>2]=(c[n>>2]|0)+q;n=r+8|0;n=(n&7|0)==0?0:0-n&7;k=r+(q+8)|0;k=(k&7|0)==0?0:0-k&7;a=r+(k+q)|0;m=n+o|0;p=r+m|0;l=a-(r+n)-o|0;c[r+(n+4)>>2]=o|3;h:do if((a|0)!=(h|0)){if((a|0)==(c[5152]|0)){L=(c[5149]|0)+l|0;c[5149]=L;c[5152]=p;c[r+(m+4)>>2]=L|1;c[r+(L+m)>>2]=L;break}h=q+4|0;b=c[r+(h+k)>>2]|0;if((b&3|0)==1){i=b&-8;f=b>>>3;i:do if(b>>>0>=256){g=c[r+((k|24)+q)>>2]|0;e=c[r+(q+12+k)>>2]|0;do if((e|0)==(a|0)){d=k|16;e=r+(h+d)|0;b=c[e>>2]|0;if(!b){d=r+(d+q)|0;b=c[d>>2]|0;if(!b){I=0;break}}else d=e;while(1){e=b+20|0;f=c[e>>2]|0;if(f){b=f;d=e;continue}e=b+16|0;f=c[e>>2]|0;if(!f)break;else{b=f;d=e}}if(d>>>0<j>>>0)ha();else{c[d>>2]=0;I=b;break}}else{f=c[r+((k|8)+q)>>2]|0;if(f>>>0<j>>>0)ha();b=f+12|0;if((c[b>>2]|0)!=(a|0))ha();d=e+8|0;if((c[d>>2]|0)==(a|0)){c[b>>2]=e;c[d>>2]=f;I=e;break}else ha()}while(0);if(!g)break;b=c[r+(q+28+k)>>2]|0;d=20892+(b<<2)|0;do if((a|0)!=(c[d>>2]|0)){if(g>>>0<(c[5151]|0)>>>0)ha();b=g+16|0;if((c[b>>2]|0)==(a|0))c[b>>2]=I;else c[g+20>>2]=I;if(!I)break i}else{c[d>>2]=I;if(I)break;c[5148]=c[5148]&~(1<<b);break i}while(0);d=c[5151]|0;if(I>>>0<d>>>0)ha();c[I+24>>2]=g;a=k|16;b=c[r+(a+q)>>2]|0;do if(b)if(b>>>0<d>>>0)ha();else{c[I+16>>2]=b;c[b+24>>2]=I;break}while(0);a=c[r+(h+a)>>2]|0;if(!a)break;if(a>>>0<(c[5151]|0)>>>0)ha();else{c[I+20>>2]=a;c[a+24>>2]=I;break}}else{d=c[r+((k|8)+q)>>2]|0;e=c[r+(q+12+k)>>2]|0;b=20628+(f<<1<<2)|0;do if((d|0)!=(b|0)){if(d>>>0<j>>>0)ha();if((c[d+12>>2]|0)==(a|0))break;ha()}while(0);if((e|0)==(d|0)){c[5147]=c[5147]&~(1<<f);break}do if((e|0)==(b|0))E=e+8|0;else{if(e>>>0<j>>>0)ha();b=e+8|0;if((c[b>>2]|0)==(a|0)){E=b;break}ha()}while(0);c[d+12>>2]=e;c[E>>2]=d}while(0);a=r+((i|k)+q)|0;f=i+l|0}else f=l;a=a+4|0;c[a>>2]=c[a>>2]&-2;c[r+(m+4)>>2]=f|1;c[r+(f+m)>>2]=f;a=f>>>3;if(f>>>0<256){b=a<<1;e=20628+(b<<2)|0;d=c[5147]|0;a=1<<a;do if(!(d&a)){c[5147]=d|a;J=20628+(b+2<<2)|0;K=e}else{a=20628+(b+2<<2)|0;b=c[a>>2]|0;if(b>>>0>=(c[5151]|0)>>>0){J=a;K=b;break}ha()}while(0);c[J>>2]=p;c[K+12>>2]=p;c[r+(m+8)>>2]=K;c[r+(m+12)>>2]=e;break}a=f>>>8;do if(!a)e=0;else{if(f>>>0>16777215){e=31;break}J=(a+1048320|0)>>>16&8;K=a<<J;I=(K+520192|0)>>>16&4;K=K<<I;e=(K+245760|0)>>>16&2;e=14-(I|J|e)+(K<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}while(0);a=20892+(e<<2)|0;c[r+(m+28)>>2]=e;c[r+(m+20)>>2]=0;c[r+(m+16)>>2]=0;b=c[5148]|0;d=1<<e;if(!(b&d)){c[5148]=b|d;c[a>>2]=p;c[r+(m+24)>>2]=a;c[r+(m+12)>>2]=p;c[r+(m+8)>>2]=p;break}a=c[a>>2]|0;j:do if((c[a+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b)break;if((c[b+4>>2]&-8|0)==(f|0)){L=b;break j}else{e=e<<1;a=b}}if(d>>>0<(c[5151]|0)>>>0)ha();else{c[d>>2]=p;c[r+(m+24)>>2]=a;c[r+(m+12)>>2]=p;c[r+(m+8)>>2]=p;break h}}else L=a;while(0);a=L+8|0;b=c[a>>2]|0;K=c[5151]|0;if(b>>>0>=K>>>0&L>>>0>=K>>>0){c[b+12>>2]=p;c[a>>2]=p;c[r+(m+8)>>2]=b;c[r+(m+12)>>2]=L;c[r+(m+24)>>2]=0;break}else ha()}else{L=(c[5150]|0)+l|0;c[5150]=L;c[5153]=p;c[r+(m+4)>>2]=L|1}while(0);L=r+(n|8)|0;return L|0}else b=21036;while(1){a=c[b>>2]|0;if(a>>>0<=h>>>0?(C=c[b+4>>2]|0,D=a+C|0,D>>>0>h>>>0):0)break;b=c[b+8>>2]|0}b=a+(C+-39)|0;b=a+(C+-47+((b&7|0)==0?0:0-b&7))|0;f=h+16|0;b=b>>>0<f>>>0?h:b;a=b+8|0;d=r+8|0;d=(d&7|0)==0?0:0-d&7;L=q+-40-d|0;c[5153]=r+d;c[5150]=L;c[r+(d+4)>>2]=L|1;c[r+(q+-36)>>2]=40;c[5154]=c[5269];d=b+4|0;c[d>>2]=27;c[a>>2]=c[5259];c[a+4>>2]=c[5260];c[a+8>>2]=c[5261];c[a+12>>2]=c[5262];c[5259]=r;c[5260]=q;c[5262]=0;c[5261]=a;a=b+28|0;c[a>>2]=7;if((b+32|0)>>>0<D>>>0)do{L=a;a=a+4|0;c[a>>2]=7}while((L+8|0)>>>0<D>>>0);if((b|0)!=(h|0)){g=b-h|0;c[d>>2]=c[d>>2]&-2;c[h+4>>2]=g|1;c[b>>2]=g;a=g>>>3;if(g>>>0<256){b=a<<1;e=20628+(b<<2)|0;d=c[5147]|0;a=1<<a;if(d&a){a=20628+(b+2<<2)|0;b=c[a>>2]|0;if(b>>>0<(c[5151]|0)>>>0)ha();else{F=a;G=b}}else{c[5147]=d|a;F=20628+(b+2<<2)|0;G=e}c[F>>2]=h;c[G+12>>2]=h;c[h+8>>2]=G;c[h+12>>2]=e;break}a=g>>>8;if(a)if(g>>>0>16777215)e=31;else{K=(a+1048320|0)>>>16&8;L=a<<K;J=(L+520192|0)>>>16&4;L=L<<J;e=(L+245760|0)>>>16&2;e=14-(J|K|e)+(L<<e>>>15)|0;e=g>>>(e+7|0)&1|e<<1}else e=0;d=20892+(e<<2)|0;c[h+28>>2]=e;c[h+20>>2]=0;c[f>>2]=0;a=c[5148]|0;b=1<<e;if(!(a&b)){c[5148]=a|b;c[d>>2]=h;c[h+24>>2]=d;c[h+12>>2]=h;c[h+8>>2]=h;break}a=c[d>>2]|0;k:do if((c[a+4>>2]&-8|0)!=(g|0)){e=g<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b)break;if((c[b+4>>2]&-8|0)==(g|0)){H=b;break k}else{e=e<<1;a=b}}if(d>>>0<(c[5151]|0)>>>0)ha();else{c[d>>2]=h;c[h+24>>2]=a;c[h+12>>2]=h;c[h+8>>2]=h;break g}}else H=a;while(0);a=H+8|0;b=c[a>>2]|0;L=c[5151]|0;if(b>>>0>=L>>>0&H>>>0>=L>>>0){c[b+12>>2]=h;c[a>>2]=h;c[h+8>>2]=b;c[h+12>>2]=H;c[h+24>>2]=0;break}else ha()}}else{L=c[5151]|0;if((L|0)==0|r>>>0<L>>>0)c[5151]=r;c[5259]=r;c[5260]=q;c[5262]=0;c[5156]=c[5265];c[5155]=-1;a=0;do{L=a<<1;K=20628+(L<<2)|0;c[20628+(L+3<<2)>>2]=K;c[20628+(L+2<<2)>>2]=K;a=a+1|0}while((a|0)!=32);L=r+8|0;L=(L&7|0)==0?0:0-L&7;K=q+-40-L|0;c[5153]=r+L;c[5150]=K;c[r+(L+4)>>2]=K|1;c[r+(q+-36)>>2]=40;c[5154]=c[5269]}while(0);a=c[5150]|0;if(a>>>0>o>>>0){K=a-o|0;c[5150]=K;L=c[5153]|0;c[5153]=L+o;c[L+(o+4)>>2]=K|1;c[L+4>>2]=o|3;L=L+8|0;return L|0}}if(!(c[5135]|0))a=20584;else a=c[(ga()|0)+60>>2]|0;c[a>>2]=12;L=0;return L|0}function Zb(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;if(!a)return;b=a+-8|0;i=c[5151]|0;if(b>>>0<i>>>0)ha();d=c[a+-4>>2]|0;e=d&3;if((e|0)==1)ha();o=d&-8;q=a+(o+-8)|0;do if(!(d&1)){b=c[b>>2]|0;if(!e)return;j=-8-b|0;l=a+j|0;m=b+o|0;if(l>>>0<i>>>0)ha();if((l|0)==(c[5152]|0)){b=a+(o+-4)|0;d=c[b>>2]|0;if((d&3|0)!=3){u=l;g=m;break}c[5149]=m;c[b>>2]=d&-2;c[a+(j+4)>>2]=m|1;c[q>>2]=m;return}f=b>>>3;if(b>>>0<256){e=c[a+(j+8)>>2]|0;d=c[a+(j+12)>>2]|0;b=20628+(f<<1<<2)|0;if((e|0)!=(b|0)){if(e>>>0<i>>>0)ha();if((c[e+12>>2]|0)!=(l|0))ha()}if((d|0)==(e|0)){c[5147]=c[5147]&~(1<<f);u=l;g=m;break}if((d|0)!=(b|0)){if(d>>>0<i>>>0)ha();b=d+8|0;if((c[b>>2]|0)==(l|0))h=b;else ha()}else h=d+8|0;c[e+12>>2]=d;c[h>>2]=e;u=l;g=m;break}h=c[a+(j+24)>>2]|0;e=c[a+(j+12)>>2]|0;do if((e|0)==(l|0)){d=a+(j+20)|0;b=c[d>>2]|0;if(!b){d=a+(j+16)|0;b=c[d>>2]|0;if(!b){k=0;break}}while(1){e=b+20|0;f=c[e>>2]|0;if(f){b=f;d=e;continue}e=b+16|0;f=c[e>>2]|0;if(!f)break;else{b=f;d=e}}if(d>>>0<i>>>0)ha();else{c[d>>2]=0;k=b;break}}else{f=c[a+(j+8)>>2]|0;if(f>>>0<i>>>0)ha();b=f+12|0;if((c[b>>2]|0)!=(l|0))ha();d=e+8|0;if((c[d>>2]|0)==(l|0)){c[b>>2]=e;c[d>>2]=f;k=e;break}else ha()}while(0);if(h){b=c[a+(j+28)>>2]|0;d=20892+(b<<2)|0;if((l|0)==(c[d>>2]|0)){c[d>>2]=k;if(!k){c[5148]=c[5148]&~(1<<b);u=l;g=m;break}}else{if(h>>>0<(c[5151]|0)>>>0)ha();b=h+16|0;if((c[b>>2]|0)==(l|0))c[b>>2]=k;else c[h+20>>2]=k;if(!k){u=l;g=m;break}}d=c[5151]|0;if(k>>>0<d>>>0)ha();c[k+24>>2]=h;b=c[a+(j+16)>>2]|0;do if(b)if(b>>>0<d>>>0)ha();else{c[k+16>>2]=b;c[b+24>>2]=k;break}while(0);b=c[a+(j+20)>>2]|0;if(b)if(b>>>0<(c[5151]|0)>>>0)ha();else{c[k+20>>2]=b;c[b+24>>2]=k;u=l;g=m;break}else{u=l;g=m}}else{u=l;g=m}}else{u=b;g=o}while(0);if(u>>>0>=q>>>0)ha();b=a+(o+-4)|0;d=c[b>>2]|0;if(!(d&1))ha();if(!(d&2)){if((q|0)==(c[5153]|0)){t=(c[5150]|0)+g|0;c[5150]=t;c[5153]=u;c[u+4>>2]=t|1;if((u|0)!=(c[5152]|0))return;c[5152]=0;c[5149]=0;return}if((q|0)==(c[5152]|0)){t=(c[5149]|0)+g|0;c[5149]=t;c[5152]=u;c[u+4>>2]=t|1;c[u+t>>2]=t;return}g=(d&-8)+g|0;f=d>>>3;do if(d>>>0>=256){h=c[a+(o+16)>>2]|0;b=c[a+(o|4)>>2]|0;do if((b|0)==(q|0)){d=a+(o+12)|0;b=c[d>>2]|0;if(!b){d=a+(o+8)|0;b=c[d>>2]|0;if(!b){p=0;break}}while(1){e=b+20|0;f=c[e>>2]|0;if(f){b=f;d=e;continue}e=b+16|0;f=c[e>>2]|0;if(!f)break;else{b=f;d=e}}if(d>>>0<(c[5151]|0)>>>0)ha();else{c[d>>2]=0;p=b;break}}else{d=c[a+o>>2]|0;if(d>>>0<(c[5151]|0)>>>0)ha();e=d+12|0;if((c[e>>2]|0)!=(q|0))ha();f=b+8|0;if((c[f>>2]|0)==(q|0)){c[e>>2]=b;c[f>>2]=d;p=b;break}else ha()}while(0);if(h){b=c[a+(o+20)>>2]|0;d=20892+(b<<2)|0;if((q|0)==(c[d>>2]|0)){c[d>>2]=p;if(!p){c[5148]=c[5148]&~(1<<b);break}}else{if(h>>>0<(c[5151]|0)>>>0)ha();b=h+16|0;if((c[b>>2]|0)==(q|0))c[b>>2]=p;else c[h+20>>2]=p;if(!p)break}d=c[5151]|0;if(p>>>0<d>>>0)ha();c[p+24>>2]=h;b=c[a+(o+8)>>2]|0;do if(b)if(b>>>0<d>>>0)ha();else{c[p+16>>2]=b;c[b+24>>2]=p;break}while(0);b=c[a+(o+12)>>2]|0;if(b)if(b>>>0<(c[5151]|0)>>>0)ha();else{c[p+20>>2]=b;c[b+24>>2]=p;break}}}else{e=c[a+o>>2]|0;d=c[a+(o|4)>>2]|0;b=20628+(f<<1<<2)|0;if((e|0)!=(b|0)){if(e>>>0<(c[5151]|0)>>>0)ha();if((c[e+12>>2]|0)!=(q|0))ha()}if((d|0)==(e|0)){c[5147]=c[5147]&~(1<<f);break}if((d|0)!=(b|0)){if(d>>>0<(c[5151]|0)>>>0)ha();b=d+8|0;if((c[b>>2]|0)==(q|0))n=b;else ha()}else n=d+8|0;c[e+12>>2]=d;c[n>>2]=e}while(0);c[u+4>>2]=g|1;c[u+g>>2]=g;if((u|0)==(c[5152]|0)){c[5149]=g;return}}else{c[b>>2]=d&-2;c[u+4>>2]=g|1;c[u+g>>2]=g}b=g>>>3;if(g>>>0<256){d=b<<1;f=20628+(d<<2)|0;e=c[5147]|0;b=1<<b;if(e&b){b=20628+(d+2<<2)|0;d=c[b>>2]|0;if(d>>>0<(c[5151]|0)>>>0)ha();else{r=b;s=d}}else{c[5147]=e|b;r=20628+(d+2<<2)|0;s=f}c[r>>2]=u;c[s+12>>2]=u;c[u+8>>2]=s;c[u+12>>2]=f;return}b=g>>>8;if(b)if(g>>>0>16777215)f=31;else{r=(b+1048320|0)>>>16&8;s=b<<r;q=(s+520192|0)>>>16&4;s=s<<q;f=(s+245760|0)>>>16&2;f=14-(q|r|f)+(s<<f>>>15)|0;f=g>>>(f+7|0)&1|f<<1}else f=0;b=20892+(f<<2)|0;c[u+28>>2]=f;c[u+20>>2]=0;c[u+16>>2]=0;d=c[5148]|0;e=1<<f;a:do if(d&e){b=c[b>>2]|0;b:do if((c[b+4>>2]&-8|0)!=(g|0)){f=g<<((f|0)==31?0:25-(f>>>1)|0);while(1){e=b+16+(f>>>31<<2)|0;d=c[e>>2]|0;if(!d)break;if((c[d+4>>2]&-8|0)==(g|0)){t=d;break b}else{f=f<<1;b=d}}if(e>>>0<(c[5151]|0)>>>0)ha();else{c[e>>2]=u;c[u+24>>2]=b;c[u+12>>2]=u;c[u+8>>2]=u;break a}}else t=b;while(0);b=t+8|0;d=c[b>>2]|0;s=c[5151]|0;if(d>>>0>=s>>>0&t>>>0>=s>>>0){c[d+12>>2]=u;c[b>>2]=u;c[u+8>>2]=d;c[u+12>>2]=t;c[u+24>>2]=0;break}else ha()}else{c[5148]=d|e;c[b>>2]=u;c[u+24>>2]=b;c[u+12>>2]=u;c[u+8>>2]=u}while(0);u=(c[5155]|0)+-1|0;c[5155]=u;if(!u)b=21044;else return;while(1){b=c[b>>2]|0;if(!b)break;else b=b+8|0}c[5155]=-1;return}function _b(){}function $b(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;d=b-d-(c>>>0>a>>>0|0)>>>0;return (C=d,a-c>>>0|0)|0}function ac(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;f=b+e|0;if((e|0)>=20){d=d&255;h=b&3;i=d|d<<8|d<<16|d<<24;g=f&~3;if(h){h=b+4-h|0;while((b|0)<(h|0)){a[b>>0]=d;b=b+1|0}}while((b|0)<(g|0)){c[b>>2]=i;b=b+4|0}}while((b|0)<(f|0)){a[b>>0]=d;b=b+1|0}return b-e|0}function bc(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){C=b>>>c;return a>>>c|(b&(1<<c)-1)<<32-c}C=0;return b>>>c-32|0}function cc(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){C=b<<c|(a&(1<<c)-1<<32-c)>>>32-c;return a<<c}C=a<<c-32;return 0}function dc(b,d,e){b=b|0;d=d|0;e=e|0;var f=0;if((e|0)>=4096)return ma(b|0,d|0,e|0)|0;f=b|0;if((b&3)==(d&3)){while(b&3){if(!e)return f|0;a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}while((e|0)>=4){c[b>>2]=c[d>>2];b=b+4|0;d=d+4|0;e=e-4|0}}while((e|0)>0){a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}return f|0}function ec(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;c=a+c>>>0;return (C=b+d+(c>>>0<a>>>0|0)>>>0,c|0)|0}function fc(b,c,d){b=b|0;c=c|0;d=d|0;var e=0;if((c|0)<(b|0)&(b|0)<(c+d|0)){e=b;c=c+d|0;b=b+d|0;while((d|0)>0){b=b-1|0;c=c-1|0;d=d-1|0;a[b>>0]=a[c>>0]|0}b=e}else dc(b,c,d)|0;return b|0}function gc(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){C=b>>c;return a>>>c|(b&(1<<c)-1)<<32-c}C=(b|0)<0?-1:0;return b>>c-32|0}function hc(b){b=b|0;var c=0;c=a[m+(b&255)>>0]|0;if((c|0)<8)return c|0;c=a[m+(b>>8&255)>>0]|0;if((c|0)<8)return c+8|0;c=a[m+(b>>16&255)>>0]|0;if((c|0)<8)return c+16|0;return (a[m+(b>>>24)>>0]|0)+24|0}function ic(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=a&65535;e=b&65535;c=_(e,f)|0;d=a>>>16;a=(c>>>16)+(_(e,d)|0)|0;e=b>>>16;b=_(e,f)|0;return (C=(a>>>16)+(_(e,d)|0)+(((a&65535)+b|0)>>>16)|0,a+b<<16|c&65535|0)|0}function jc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;j=b>>31|((b|0)<0?-1:0)<<1;i=((b|0)<0?-1:0)>>31|((b|0)<0?-1:0)<<1;f=d>>31|((d|0)<0?-1:0)<<1;e=((d|0)<0?-1:0)>>31|((d|0)<0?-1:0)<<1;h=$b(j^a,i^b,j,i)|0;g=C;a=f^j;b=e^i;return $b((oc(h,g,$b(f^c,e^d,f,e)|0,C,0)|0)^a,C^b,a,b)|0}function kc(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,j=0,k=0,l=0;f=i;i=i+16|0;j=f|0;h=b>>31|((b|0)<0?-1:0)<<1;g=((b|0)<0?-1:0)>>31|((b|0)<0?-1:0)<<1;l=e>>31|((e|0)<0?-1:0)<<1;k=((e|0)<0?-1:0)>>31|((e|0)<0?-1:0)<<1;a=$b(h^a,g^b,h,g)|0;b=C;oc(a,b,$b(l^d,k^e,l,k)|0,C,j)|0;e=$b(c[j>>2]^h,c[j+4>>2]^g,h,g)|0;d=C;i=f;return (C=d,e)|0}function lc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=a;f=c;c=ic(e,f)|0;a=C;return (C=(_(b,f)|0)+(_(d,e)|0)+a|a&0,c|0|0)|0}function mc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return oc(a,b,c,d,0)|0}function nc(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0;g=i;i=i+16|0;f=g|0;oc(a,b,d,e,f)|0;i=g;return (C=c[f+4>>2]|0,c[f>>2]|0)|0}function oc(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;l=a;j=b;k=j;h=d;n=e;i=n;if(!k){g=(f|0)!=0;if(!i){if(g){c[f>>2]=(l>>>0)%(h>>>0);c[f+4>>2]=0}n=0;f=(l>>>0)/(h>>>0)>>>0;return (C=n,f)|0}else{if(!g){n=0;f=0;return (C=n,f)|0}c[f>>2]=a|0;c[f+4>>2]=b&0;n=0;f=0;return (C=n,f)|0}}g=(i|0)==0;do if(h){if(!g){g=(aa(i|0)|0)-(aa(k|0)|0)|0;if(g>>>0<=31){m=g+1|0;i=31-g|0;b=g-31>>31;h=m;a=l>>>(m>>>0)&b|k<<i;b=k>>>(m>>>0)&b;g=0;i=l<<i;break}if(!f){n=0;f=0;return (C=n,f)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;n=0;f=0;return (C=n,f)|0}g=h-1|0;if(g&h){i=(aa(h|0)|0)+33-(aa(k|0)|0)|0;p=64-i|0;m=32-i|0;j=m>>31;o=i-32|0;b=o>>31;h=i;a=m-1>>31&k>>>(o>>>0)|(k<<m|l>>>(i>>>0))&b;b=b&k>>>(i>>>0);g=l<<p&j;i=(k<<p|l>>>(o>>>0))&j|l<<m&i-33>>31;break}if(f){c[f>>2]=g&l;c[f+4>>2]=0}if((h|0)==1){o=j|b&0;p=a|0|0;return (C=o,p)|0}else{p=hc(h|0)|0;o=k>>>(p>>>0)|0;p=k<<32-p|l>>>(p>>>0)|0;return (C=o,p)|0}}else{if(g){if(f){c[f>>2]=(k>>>0)%(h>>>0);c[f+4>>2]=0}o=0;p=(k>>>0)/(h>>>0)>>>0;return (C=o,p)|0}if(!l){if(f){c[f>>2]=0;c[f+4>>2]=(k>>>0)%(i>>>0)}o=0;p=(k>>>0)/(i>>>0)>>>0;return (C=o,p)|0}g=i-1|0;if(!(g&i)){if(f){c[f>>2]=a|0;c[f+4>>2]=g&k|b&0}o=0;p=k>>>((hc(i|0)|0)>>>0);return (C=o,p)|0}g=(aa(i|0)|0)-(aa(k|0)|0)|0;if(g>>>0<=30){b=g+1|0;i=31-g|0;h=b;a=k<<i|l>>>(b>>>0);b=k>>>(b>>>0);g=0;i=l<<i;break}if(!f){o=0;p=0;return (C=o,p)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;o=0;p=0;return (C=o,p)|0}while(0);if(!h){k=i;j=0;i=0}else{m=d|0|0;l=n|e&0;k=ec(m|0,l|0,-1,-1)|0;d=C;j=i;i=0;do{e=j;j=g>>>31|j<<1;g=i|g<<1;e=a<<1|e>>>31|0;n=a>>>31|b<<1|0;$b(k,d,e,n)|0;p=C;o=p>>31|((p|0)<0?-1:0)<<1;i=o&1;a=$b(e,n,o&m,(((p|0)<0?-1:0)>>31|((p|0)<0?-1:0)<<1)&l)|0;b=C;h=h-1|0}while((h|0)!=0);k=j;j=0}h=0;if(f){c[f>>2]=a;c[f+4>>2]=b}o=(g|0)>>>31|(k|h)<<1|(h<<1|g>>>31)&0|j;p=(g<<1|0>>>31)&-2|i;return (C=o,p)|0}function pc(a,b,c){a=a|0;b=b|0;c=c|0;ra[a&1](b|0,c|0)}function qc(a,b,c){a=a|0;b=b|0;c=c|0;return sa[a&1](b|0,c|0)|0}function rc(a,b){a=a|0;b=b|0;ba(0)}function sc(a,b){a=a|0;b=b|0;ba(1);return 0}

// EMSCRIPTEN_END_FUNCS
var ra=[rc,Mb];var sa=[sc,Lb];return{_i64Subtract:$b,_free:Zb,_i64Add:ec,_memmove:fc,_memset:ac,_malloc:Yb,_encode:Ca,_memcpy:dc,_bitshift64Lshr:bc,_bitshift64Shl:cc,runPostSets:_b,stackAlloc:ta,stackSave:ua,stackRestore:va,establishStackSpace:wa,setThrew:xa,setTempRet0:Aa,getTempRet0:Ba,dynCall_vii:pc,dynCall_iii:qc}})


// EMSCRIPTEN_END_ASM
(b.I,b.J,buffer),Ja=b._i64Subtract=Y._i64Subtract,pa=b._free=Y._free;b.runPostSets=Y.runPostSets;var Pa=b._i64Add=Y._i64Add,Qa=b._memmove=Y._memmove,La=b._memset=Y._memset,ia=b._malloc=Y._malloc;b._encode=Y._encode;var Oa=b._memcpy=Y._memcpy,Ma=b._bitshift64Lshr=Y._bitshift64Lshr,Na=b._bitshift64Shl=Y._bitshift64Shl;b.dynCall_vii=Y.dynCall_vii;b.dynCall_iii=Y.dynCall_iii;A.n=Y.stackAlloc;A.G=Y.stackSave;A.F=Y.stackRestore;A.W=Y.establishStackSpace;A.P=Y.setTempRet0;
A.N=Y.getTempRet0;
if(W)if("function"===typeof b.locateFile?W=b.locateFile(W):b.memoryInitializerPrefixURL&&(W=b.memoryInitializerPrefixURL+W),v||x){var Ta=b.readBinary(W);P.set(Ta,A.C)}else{var Va=function(){Browser.T(W,Ua,function(){throw"could not load memory initializer "+W;})};U++;b.monitorRunDependencies&&b.monitorRunDependencies(U);var Ua=function(a){a.byteLength&&(a=new Uint8Array(a));P.set(a,A.C);U--;b.monitorRunDependencies&&b.monitorRunDependencies(U);0==U&&(null!==Ha&&(clearInterval(Ha),Ha=null),V&&(a=V,
V=null,a()))},Z=b.memoryInitializerRequest;if(Z){var Wa=function(){200!==Z.status&&0!==Z.status?(console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+Z.status+", retrying "+W),Va()):Ua(Z.response)};Z.response?setTimeout(Wa,0):Z.addEventListener("load",Wa)}else Va()}function y(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}y.prototype=Error();y.prototype.constructor=y;
var Xa=null,V=function Ya(){b.calledRun||Za();b.calledRun||(V=Ya)};
b.callMain=b.U=function(a){function c(){for(var a=0;3>a;a++)e.push(0)}assert(0==U,"cannot call main when async dependencies remain! (listen on __ATMAIN__)");assert(0==Aa.length,"cannot call main when preRun functions remain to be called");a=a||[];T||(T=!0,S(Ba));var d=a.length+1,e=[O(Ga(b.thisProgram),"i8",0)];c();for(var h=0;h<d-1;h+=1)e.push(O(Ga(a[h]),"i8",0)),c();e.push(0);e=O(e,"i32",0);try{var l=b._main(d,e,0);$a(l,!0)}catch(k){if(!(k instanceof y))if("SimulateInfiniteLoop"==k)b.noExitRuntime=
!0;else throw k&&"object"===typeof k&&k.stack&&b.A("exception thrown: "+[k,k.stack]),k;}finally{}};
function Za(a){function c(){if(!b.calledRun&&(b.calledRun=!0,!F)){T||(T=!0,S(Ba));S(Ca);if(b.onRuntimeInitialized)b.onRuntimeInitialized();b._main&&ab&&b.callMain(a);if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();Ea.unshift(c)}S(Ea)}}a=a||b.arguments;null===Xa&&(Xa=Date.now());if(!(0<U)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)Fa();S(Aa);0<U||b.calledRun||(b.setStatus?(b.setStatus("Running..."),
setTimeout(function(){setTimeout(function(){b.setStatus("")},1);c()},1)):c())}}b.run=b.run=Za;function $a(a,c){if(!c||!b.noExitRuntime){if(!b.noExitRuntime&&(F=!0,z=void 0,S(Da),b.onExit))b.onExit(a);v?(process.stdout.once("drain",function(){process.exit(a)}),console.log(" "),setTimeout(function(){process.exit(a)},500)):x&&"function"===typeof quit&&quit(a);throw new y(a);}}b.exit=b.exit=$a;var bb=[];
function E(a){void 0!==a?(b.print(a),b.A(a),a=JSON.stringify(a)):a="";F=!0;var c="abort("+a+") at "+qa()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";bb&&bb.forEach(function(d){c=d(c,a)});throw c;}b.abort=b.abort=E;if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();var ab=!0;b.noInitialRun&&(ab=!1);Za();

