# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.3.0] - 2021-12-13

### Added

- Updated to include support for Unicode 14

## [1.2.0] - 2021-01-29

### Updated

- Refactored to increase speed

## [1.1.0] - 2020-09-14

### Added

- Updated to include support for Unicode 13

## [1.0.0] - 2020-09-13

- Forked from work by @JLHwung on original `Grapheme-Splitter` library: https://github.com/JLHwung/grapheme-splitter/tree/next
- Converted to Typescript
- Added development and build tooling
