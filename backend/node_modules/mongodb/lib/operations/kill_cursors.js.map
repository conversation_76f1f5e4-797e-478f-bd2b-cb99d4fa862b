{"version": 3, "file": "kill_cursors.js", "sourceRoot": "", "sources": ["../../src/operations/kill_cursors.ts"], "names": [], "mappings": ";;;AACA,oCAA6C;AAI7C,2CAKqB;AAYrB,MAAa,oBAAqB,SAAQ,qCAAyB;IAGjE,YAAY,QAAc,EAAE,EAAoB,EAAE,MAAc,EAAE,OAAyB;QACzF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,eAAe,CACb,MAAc,EACd,OAAkC,EAClC,QAAwB;QAExB,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;YAC1B,OAAO,QAAQ,CACb,IAAI,yBAAiB,CAAC,2DAA2D,CAAC,CACnF,CAAC;SACH;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC;QACvC,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,gEAAgE;YAChE,wFAAwF;YACxF,OAAO,QAAQ,CACb,IAAI,yBAAiB,CAAC,yDAAyD,CAAC,CACjF,CAAC;SACH;QAED,MAAM,kBAAkB,GAAuB;YAC7C,WAAW;YACX,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;SACzB,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7E,CAAC;CACF;AArCD,oDAqCC;AAED,IAAA,yBAAa,EAAC,oBAAoB,EAAE,CAAC,kBAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC"}