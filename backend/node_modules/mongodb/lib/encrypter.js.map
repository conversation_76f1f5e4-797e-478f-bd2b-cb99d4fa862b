{"version": 3, "file": "encrypter.js", "sourceRoot": "", "sources": ["../src/encrypter.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;AAEvD,2CAAkD;AAElD,mCAAiF;AACjF,iDAAsE;AACtE,mCAAoE;AAEpE,IAAI,kBAA0F,CAAC;AAE/F,gBAAgB;AAChB,MAAM,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAQjD,gBAAgB;AAChB,MAAa,SAAS;IAMpB,YAAY,MAAmB,EAAE,GAAW,EAAE,OAA2B;QACvE,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,EAAE;YAC9C,MAAM,IAAI,iCAAyB,CAAC,2CAA2C,CAAC,CAAC;SAClF;QACD,2HAA2H;QAC3H,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;QAE7B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,oBAAoB,CAAC;QAC1E,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,EAAE;YAC9E,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC;SAChD;aAAM,IAAI,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,EAAE;YACxD,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SACtF;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,SAAS,CAAC;SACnD;aAAM,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YACpC,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC;SAChD;aAAM;YACL,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SACtF;QAED,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,OAAO,CAAC,cAAc,CAAC,YAAY,GAAG;gBACpC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,aAAa,EAAE,OAAO,CAAC,aAAa;aACrC,CAAC;SACH;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IAC9E,CAAC;IAED,iBAAiB,CAAC,MAAmB,EAAE,GAAW,EAAE,OAA2B;QAC7E,0DAA0D;QAC1D,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3C,IAAI,cAAc,IAAI,IAAI,EAAE;YAC1B,MAAM,aAAa,GAAuB,EAAE,CAAC;YAE7C,KAAK,MAAM,GAAG,IAAI;gBAChB,GAAG,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACtC,GAAG,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC;aAC7B,EAAE;gBACb,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACvF,SAAS;gBACX,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;aAC5D;YAED,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC;YAE9B,cAAc,GAAG,IAAI,0BAAW,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc,CAAC;YAEvC,KAAK,MAAM,SAAS,IAAI,+BAAmB,EAAE;gBAC3C,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;oBAClD,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;iBACxC;aACF;YAED,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gBAC/C,cAAc,EAAE,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,0DAA0D;QAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,eAAe,IAAI,cAAc,IAAI,IAAI,EAAE;YAClD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,MAAM,cAAc,CAAC,OAAO,EAAE,CAAC;SAChC;IACH,CAAC;IAED,KAAK,CAAC,MAAmB,EAAE,KAAc,EAAE,QAAkB;QAC3D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;YACvC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAI,cAAc,IAAI,IAAI,IAAI,MAAM,KAAK,cAAc,EAAE;gBACvD,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAC9B,GAAG,EAAE,CAAC,QAAQ,EAAE,EAChB,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CACzB,CAAC;gBACF,OAAO;aACR;YACD,QAAQ,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,kBAAkB;QACvB,MAAM,uBAAuB,GAAG,IAAA,kCAA0B,GAAE,CAAC;QAC7D,IAAI,uBAAuB,IAAI,IAAI,EAAE;YACnC,MAAM,IAAI,mCAA2B,CACnC,8DAA8D;gBAC5D,wEAAwE,CAC3E,CAAC;SACH;QACD,kBAAkB,GAAG,uBAAuB,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC;IAChG,CAAC;CACF;AA9GD,8BA8GC"}