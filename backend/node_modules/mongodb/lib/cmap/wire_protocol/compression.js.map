{"version": 3, "file": "compression.js", "sourceRoot": "", "sources": ["../../../src/cmap/wire_protocol/compression.ts"], "names": [], "mappings": ";;;AAAA,+BAAiC;AACjC,6BAA6B;AAE7B,+CAAuD;AACvD,qCAAuF;AACvF,uCAAiF;AAEjF,cAAc;AACD,QAAA,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;IACtC,IAAI,EAAE,CAAC;IACP,MAAM,EAAE,CAAC;IACT,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;CACC,CAAC,CAAC;AAQC,QAAA,sBAAsB,GAAG,IAAI,GAAG,CAAC;IAC5C,gCAAoB;IACpB,WAAW;IACX,cAAc;IACd,UAAU;IACV,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,iBAAiB;IACjB,gBAAgB;IAChB,QAAQ;CACT,CAAC,CAAC;AAEH,MAAM,sBAAsB,GAAG,CAAC,CAAC;AAEjC,MAAM,WAAW,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvD,MAAM,WAAW,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAEvD,IAAI,IAAsB,CAAC;AAC3B,IAAI,MAAM,GAAqB,IAAI,CAAC;AACpC,SAAS,UAAU;IACjB,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,MAAM,YAAY,GAAG,IAAA,gBAAS,GAAE,CAAC;QACjC,IAAI,cAAc,IAAI,YAAY,EAAE;YAClC,MAAM,YAAY,CAAC,YAAY,CAAC;SACjC;QACD,MAAM,GAAG,YAAY,CAAC;KACvB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,8DAA8D;AACvD,KAAK,UAAU,QAAQ,CAC5B,OAA2E,EAC3E,kBAA0B;IAE1B,MAAM,WAAW,GAAG,EAAsB,CAAC;IAC3C,QAAQ,OAAO,CAAC,gBAAgB,EAAE;QAChC,KAAK,QAAQ,CAAC,CAAC;YACb,MAAM,KAAN,MAAM,GAAK,UAAU,EAAE,EAAC;YACxB,OAAO,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;SAC5C;QACD,KAAK,MAAM,CAAC,CAAC;YACX,QAAQ,EAAE,CAAC;YACX,IAAI,cAAc,IAAI,IAAI,EAAE;gBAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC;aAC5B;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;SAClE;QACD,KAAK,MAAM,CAAC,CAAC;YACX,IAAI,OAAO,CAAC,oBAAoB,EAAE;gBAChC,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,oBAAoB,CAAC;aAClD;YACD,OAAO,WAAW,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;SACrD;QACD,OAAO,CAAC,CAAC;YACP,MAAM,IAAI,iCAAyB,CACjC,sBAAsB,OAAO,CAAC,gBAAgB,qBAAqB,CACpE,CAAC;SACH;KACF;AACH,CAAC;AA7BD,4BA6BC;AAED,kDAAkD;AAC3C,KAAK,UAAU,UAAU,CAAC,YAAoB,EAAE,cAAsB;IAC3E,IACE,YAAY,KAAK,kBAAU,CAAC,MAAM;QAClC,YAAY,KAAK,kBAAU,CAAC,IAAI;QAChC,YAAY,KAAK,kBAAU,CAAC,IAAI;QAChC,YAAY,KAAK,kBAAU,CAAC,IAAI,EAChC;QACA,MAAM,IAAI,+BAAuB,CAC/B,2FAA2F,YAAY,GAAG,CAC3G,CAAC;KACH;IAED,QAAQ,YAAY,EAAE;QACpB,KAAK,kBAAU,CAAC,MAAM,CAAC,CAAC;YACtB,MAAM,KAAN,MAAM,GAAK,UAAU,EAAE,EAAC;YACxB,OAAO,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;SAC9D;QACD,KAAK,kBAAU,CAAC,IAAI,CAAC,CAAC;YACpB,QAAQ,EAAE,CAAC;YACX,IAAI,cAAc,IAAI,IAAI,EAAE;gBAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC;aAC5B;YACD,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;SACxC;QACD,KAAK,kBAAU,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO,WAAW,CAAC,cAAc,CAAC,CAAC;SACpC;QACD,OAAO,CAAC,CAAC;YACP,OAAO,cAAc,CAAC;SACvB;KACF;AACH,CAAC;AA/BD,gCA+BC;AAED;;GAEG;AACH,SAAS,QAAQ;IACf,IAAI,CAAC,IAAI,EAAE;QACT,IAAI,GAAG,IAAA,qBAAc,GAAE,CAAC;KACzB;AACH,CAAC"}