{"version": 3, "file": "find_cursor.js", "sourceRoot": "", "sources": ["../../src/cursor/find_cursor.ts"], "names": [], "mappings": ";;;AAAA,kCAA8C;AAC9C,oCAA+E;AAI/E,+CAAwE;AACxE,uEAAyF;AACzF,6CAAqE;AAGrE,kCAAoE;AACpE,oCAA+F;AAC/F,uDAAwE;AAExE,gBAAgB;AAChB,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,gBAAgB;AAChB,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAC3C,gBAAgB;AAChB,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AAE7C,uCAAuC;AAC1B,QAAA,KAAK,GAAG;IACnB,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,SAAS;IACT,SAAS;CACD,CAAC;AAEX,cAAc;AACd,MAAa,UAA0B,SAAQ,gCAAuB;IAQpE,gBAAgB;IAChB,YACE,MAAmB,EACnB,SAA2B,EAC3B,SAAmB,EAAE,EACrB,UAAuB,EAAE;QAEzB,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAElC,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;QAE9B,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACrD;IACH,CAAC;IAED,KAAK;QACH,MAAM,aAAa,GAAG,IAAA,oBAAY,EAAC,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QAC5D,OAAO,aAAa,CAAC,OAAO,CAAC;QAC7B,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAChE,GAAG,aAAa;SACjB,CAAC,CAAC;IACL,CAAC;IAEQ,GAAG,CAAI,SAA8B;QAC5C,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAkB,CAAC;IAC/C,CAAC;IAED,gBAAgB;IAChB,WAAW,CAAC,OAAsB,EAAE,QAAmC;QACrE,MAAM,aAAa,GAAG,IAAI,oBAAa,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAChF,GAAG,IAAI,CAAC,aAAa,CAAC;YACtB,GAAG,IAAI,CAAC,aAAa;YACrB,OAAO;SACR,CAAC,CAAC;QAEH,IAAA,oCAAgB,EAAC,IAAI,CAAC,MAAM,EAAE,aAAa,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC7D,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;YAElD,gFAAgF;YAChF,uDAAuD;YACvD,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;aACxD;iBAAM;gBACL,IAAI,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aACzE;YAED,kBAAkB;YAClB,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;IACP,QAAQ,CAAC,SAAiB,EAAE,QAA4B;QAC/D,yEAAyE;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,WAAW,EAAE;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC;YACxC,SAAS;gBACP,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,WAAW,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;YAE1F,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CACxB,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,WAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAClE,CAAC;gBACF,OAAO;aACR;SACF;QAED,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC1C,IAAI,GAAG;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;YAE9B,2FAA2F;YAC3F,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;aAC5E;YAED,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK,CAAC,OAAsB;QAChC,IAAA,uBAAe,EACb,kKAAkK,CACnK,CAAC;QACF,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAChC,MAAM,IAAI,iCAAyB,CAAC,kCAAkC,CAAC,CAAC;SACzE;QACD,OAAO,IAAA,oCAAgB,EACrB,IAAI,CAAC,MAAM,EACX,IAAI,sBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAChD,GAAG,IAAI,CAAC,aAAa,CAAC;YACtB,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,OAAO;SACX,CAAC,CACH,CAAC;IACJ,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,OAAO,CAAC,SAAgC;QAC5C,OAAO,IAAA,oCAAgB,EACrB,IAAI,CAAC,MAAM,EACX,IAAI,oBAAa,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YAC1D,GAAG,IAAI,CAAC,aAAa,CAAC;YACtB,GAAG,IAAI,CAAC,aAAa;YACrB,OAAO,EAAE,SAAS,IAAI,IAAI;SAC3B,CAAC,CACH,CAAC;IACJ,CAAC;IAED,2BAA2B;IAC3B,MAAM,CAAC,MAAgB;QACrB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,IAAU;QACb,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAa;QACf,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAa;QACf,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAC,KAAc;QACtB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,KAAc;QACzB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,IAAY,EAAE,KAA2C;QACxE,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnB,MAAM,IAAI,iCAAyB,CAAC,GAAG,IAAI,gCAAgC,CAAC,CAAC;SAC9E;QAED,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE7B,wCAAwC;QACxC,QAAQ,KAAK,EAAE;YACb,KAAK,SAAS;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,KAA0B,CAAC;gBACzD,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,KAAgB,CAAC;gBAC/C,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,KAA0B,CAAC;gBACtD,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,KAAiB,CAAC;gBAC5C,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAe,CAAC;gBAChD,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,KAAiB,CAAC;gBAC5C,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,KAA0B,CAAC,CAAC;gBAClE,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,CAAC,OAAO,CAAC,GAAG,KAAiB,CAAC;gBAClC,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAgB,CAAC;gBACjD,MAAM;YAER,KAAK,aAAa;gBAChB,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,KAAgB,CAAC;gBACpD,MAAM;YAER;gBACE,MAAM,IAAI,iCAAyB,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;SAC1E;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,KAAa;QACnB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,KAAa;QAC1B,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,8CAA8C,CAAC,CAAC;SACrF;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACM,SAAS,CAAC,KAAa;QAC9B,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,yCAAyC,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,OAAO,CAAgC,KAAe;QACpD,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;QACvC,OAAO,IAAgC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACH,IAAI,CAAC,IAAmB,EAAE,SAAyB;QACjD,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;YAChC,MAAM,IAAI,gCAAwB,CAAC,0CAA0C,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,KAAK,GAAG,IAAI;QACvB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,qDAAqD,CAAC,CAAC;SAC5F;QAED,oFAAoF;QACpF,IAAI,CAAC,KAAK,EAAE;YACV,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC;YACzC,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,KAAuB;QAC/B,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAa;QACjB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;YAChC,MAAM,IAAI,gCAAwB,CAAC,wCAAwC,CAAC,CAAC;SAC9E;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,uCAAuC,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,KAAa;QAChB,IAAA,qCAAmB,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;YAChC,MAAM,IAAI,gCAAwB,CAAC,uCAAuC,CAAC,CAAC;SAC7E;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,iCAAyB,CAAC,sCAAsC,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAzaD,gCAyaC"}