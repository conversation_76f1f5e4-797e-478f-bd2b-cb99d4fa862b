"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2022 = void 0;
const es2021_1 = require("./es2021");
const es2022_array_1 = require("./es2022.array");
const es2022_error_1 = require("./es2022.error");
const es2022_intl_1 = require("./es2022.intl");
const es2022_object_1 = require("./es2022.object");
const es2022_regexp_1 = require("./es2022.regexp");
const es2022_string_1 = require("./es2022.string");
exports.es2022 = {
    libs: [
        es2021_1.es2021,
        es2022_array_1.es2022_array,
        es2022_error_1.es2022_error,
        es2022_intl_1.es2022_intl,
        es2022_object_1.es2022_object,
        es2022_regexp_1.es2022_regexp,
        es2022_string_1.es2022_string,
    ],
    variables: [],
};
