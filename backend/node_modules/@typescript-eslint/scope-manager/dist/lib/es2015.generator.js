"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2015_generator = void 0;
const base_config_1 = require("./base-config");
const es2015_iterable_1 = require("./es2015.iterable");
exports.es2015_generator = {
    libs: [es2015_iterable_1.es2015_iterable],
    variables: [
        ['Generator', base_config_1.TYPE],
        ['GeneratorFunction', base_config_1.TYPE],
        ['GeneratorFunctionConstructor', base_config_1.TYPE],
    ],
};
