"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2024_sharedmemory = void 0;
const base_config_1 = require("./base-config");
const es2020_bigint_1 = require("./es2020.bigint");
exports.es2024_sharedmemory = {
    libs: [es2020_bigint_1.es2020_bigint],
    variables: [
        ['Atomics', base_config_1.TYPE],
        ['SharedArrayBuffer', base_config_1.TYPE],
        ['SharedArrayBufferConstructor', base_config_1.TYPE],
    ],
};
