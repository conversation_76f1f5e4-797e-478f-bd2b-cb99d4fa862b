"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_weakref = void 0;
const base_config_1 = require("./base-config");
const es2015_symbol_wellknown_1 = require("./es2015.symbol.wellknown");
exports.esnext_weakref = {
    libs: [es2015_symbol_wellknown_1.es2015_symbol_wellknown],
    variables: [
        ['WeakRef', base_config_1.TYPE_VALUE],
        ['WeakRefConstructor', base_config_1.TYPE],
        ['FinalizationRegistry', base_config_1.TYPE_VALUE],
        ['FinalizationRegistryConstructor', base_config_1.TYPE],
    ],
};
