import type { TSESLint } from '@typescript-eslint/utils';
export type Options = ['fields' | 'getters'];
export type MessageIds = 'preferFieldStyle' | 'preferFieldStyleSuggestion' | 'preferGetterStyle' | 'preferGetterStyleSuggestion';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=class-literal-property-style.d.ts.map