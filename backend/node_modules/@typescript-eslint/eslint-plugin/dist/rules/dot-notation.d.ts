import type { TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: import("@typescript-eslint/utils/ts-eslint").RuleModule<"useBrackets" | "useDot", [{
    allowIndexSignaturePropertyAccess?: boolean;
    allowKeywords?: boolean;
    allowPattern?: string;
    allowPrivateClassPropertyAccess?: boolean;
    allowProtectedClassPropertyAccess?: boolean;
}], unknown, {
    MemberExpression(node: TSESTree.MemberExpression): void;
}>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"useBrackets" | "useDot", [{
    allowIndexSignaturePropertyAccess?: boolean;
    allowKeywords?: boolean;
    allowPattern?: string;
    allowPrivateClassPropertyAccess?: boolean;
    allowProtectedClassPropertyAccess?: boolean;
}], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=dot-notation.d.ts.map