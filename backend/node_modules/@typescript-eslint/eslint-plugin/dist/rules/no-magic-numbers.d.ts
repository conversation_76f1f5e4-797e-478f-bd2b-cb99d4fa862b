import type { TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: import("@typescript-eslint/utils/ts-eslint").RuleModule<"noMagic", [{
    detectObjects?: boolean;
    enforceConst?: boolean;
    ignore?: (number | string)[];
    ignoreArrayIndexes?: boolean;
    ignoreEnums?: boolean;
    ignoreNumericLiteralTypes?: boolean;
    ignoreReadonlyClassProperties?: boolean;
    ignoreTypeIndexes?: boolean;
}], unknown, {
    Literal(node: TSESTree.Literal): void;
}>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"noMagic", [{
    detectObjects?: boolean;
    enforceConst?: boolean;
    ignore?: (number | string)[];
    ignoreArrayIndexes?: boolean;
    ignoreEnums?: boolean;
    ignoreNumericLiteralTypes?: boolean;
    ignoreReadonlyClassProperties?: boolean;
    ignoreTypeIndexes?: boolean;
}], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-magic-numbers.d.ts.map