export type Options = [
    {
        ignoreParameters?: boolean;
        ignoreProperties?: boolean;
    }
];
export type MessageIds = 'noInferrableType';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"noInferrableType", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-inferrable-types.d.ts.map