{"version": 3, "file": "Linter.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/Linter.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AACxE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,SAAS,IAAI,aAAa,EAAE,MAAM,aAAa,CAAC;AAC9D,OAAO,KAAK,EACV,qBAAqB,EACrB,aAAa,EACb,kBAAkB,EAClB,OAAO,EACP,UAAU,EACX,MAAM,QAAQ,CAAC;AAChB,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE/C,MAAM,MAAM,iBAAiB,CAC3B,UAAU,SAAS,MAAM,GAAG,MAAM,EAClC,OAAO,SAAS,SAAS,OAAO,EAAE,GAAG,EAAE,IACrC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,GAC1D,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;AAGlD,OAAO,OAAO,UAAU;IACtB;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IAEzB;;;OAGG;gBACS,MAAM,CAAC,EAAE,MAAM,CAAC,aAAa;IAEzC;;;;OAIG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,iBAAiB,GAAG,IAAI;IAE5E;;;;OAIG;IACH,UAAU,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EACtE,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,kBAAkB,GACtE,IAAI;IAEP;;;OAGG;IACH,WAAW,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EACvE,aAAa,EAAE,MAAM,CACnB,MAAM,EACJ,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,GACtC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAC1C,GACA,IAAI;IAEP;;;OAGG;IACH,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;IAE7D;;;OAGG;IACH,aAAa,IAAI,UAAU;IAE3B;;;;;;;;OAQG;IACH,MAAM,CACJ,gBAAgB,EAAE,MAAM,GAAG,UAAU,EACrC,MAAM,EAAE,MAAM,CAAC,UAAU,EACzB,iBAAiB,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,aAAa,GAChD,MAAM,CAAC,WAAW,EAAE;IAMvB;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IAEhC;;;;;;OAMG;IACH,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,MAAM,CAAC,UAAU,EACzB,OAAO,EAAE,MAAM,CAAC,UAAU,GACzB,MAAM,CAAC,SAAS;CACpB;AAED,kBAAU,MAAM,CAAC;IACf,UAAiB,aAAa;QAC5B;;;WAGG;QACH,UAAU,CAAC,EAAE,mBAAmB,CAAC;QAEjC;;WAEG;QACH,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,KAAY,mBAAmB,GAAG,UAAU,GAAG,MAAM,CAAC;IACtD,KAAY,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;IAC/D,KAAY,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IACvD,KAAY,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,CAAC;IACrE,KAAY,wBAAwB,GAAG,YAAY,CAAC,wBAAwB,CAAC;IAC7E,KAAY,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IACvD,KAAY,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;IACjD,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,KAAY,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,CAAC;IACnE,KAAY,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;IACnD,KAAY,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;IAC7C,KAAY,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC;IAEzD,wDAAwD;IACxD,KAAY,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;IAC1C,KAAY,UAAU,GAClB,aAAa,CAAC,MAAM,GACpB,UAAU,CAAC,MAAM,GACjB,UAAU,CAAC,WAAW,CAAC;IAC3B,mEAAmE;IACnE,KAAY,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;IAE1D,UAAiB,aAAa;QAC5B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAC5B;;WAEG;QACH,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB;;WAEG;QACH,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC;QAC9D;;;;;;WAMG;QACH,WAAW,CAAC,EAAE,aAAa,CAAC,WAAW,CAAC;QACxC;;;WAGG;QACH,UAAU,CAAC,EAAE,aAAa,CAAC,UAAU,CAAC;QACtC;;WAEG;QACH,6BAA6B,CAAC,EAAE,OAAO,GAAG,cAAc,CAAC;KAC1D;IAED,UAAiB,UAAW,SAAQ,aAAa;QAC/C;;WAEG;QACH,GAAG,CAAC,EAAE,OAAO,CAAC;KACf;IAED,UAAiB,cAAc;QAC7B,IAAI,EAAE,MAAM,CAAC;QACb,GAAG,EAAE,OAAO,CAAC;QACb,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,WAAW;QAC1B;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QACf;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,KAAK,CAAC,EAAE,IAAI,CAAC;QACb;;WAEG;QACH,GAAG,CAAC,EAAE,OAAO,CAAC;QACd;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;WAEG;QACH,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QACtB;;WAEG;QACH,QAAQ,EAAE,QAAQ,CAAC;QACnB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QACtB;;WAEG;QACH,WAAW,CAAC,EAAE,cAAc,EAAE,CAAC;KAChC;IAED,UAAiB,SAAS;QACxB;;WAEG;QACH,KAAK,EAAE,OAAO,CAAC;QACf;;WAEG;QACH,QAAQ,EAAE,WAAW,EAAE,CAAC;QACxB;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;KAChB;IAED,kDAAkD;IAClD,KAAY,YAAY,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAEpD,iDAAiD;IACjD,KAAY,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC;IAEnD,4DAA4D;IAC5D,KAAY,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC;IAEtD,UAAiB,WAAW;QAC1B;;WAEG;QACH,OAAO,CAAC,EAAE,aAAa,CAAC;QACxB;;WAEG;QACH,aAAa,CAAC,EAAE,aAAa,CAAC;KAC/B;IAGD,KAAY,iBAAiB,GAAG,MAAM,CACpC,MAAM,EACN,qBAAqB,GAAG,aAAa,CACtC,CAAC;IACF,KAAY,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAExD,UAAiB,MAAM;QACrB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;QAC/C;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC3C;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;QAClB;;WAEG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;QAC3D;;WAEG;QACH,KAAK,CAAC,EAAE,iBAAiB,CAAC;KAC3B;CACF;2BAOqC,OAAO,UAAU;AALvD;;;;GAIG;AACH,cAAM,MAAO,SAAQ,WAAmC;CAAG;AAE3D,OAAO,EAAE,MAAM,EAAE,CAAC"}