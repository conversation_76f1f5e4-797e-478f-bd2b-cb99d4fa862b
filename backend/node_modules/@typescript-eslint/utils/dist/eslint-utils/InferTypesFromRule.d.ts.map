{"version": 3, "file": "InferTypesFromRule.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/InferTypesFromRule.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAEnE;;GAEG;AACH,MAAM,MAAM,wBAAwB,CAAC,CAAC,IACpC,CAAC,SAAS,UAAU,CAAC,MAAM,WAAW,EAAE,MAAM,OAAO,CAAC,GAClD,OAAO,GACP,CAAC,SAAS,kBAAkB,CAAC,MAAM,WAAW,EAAE,MAAM,OAAO,CAAC,GAC5D,OAAO,GACP,OAAO,CAAC;AAEhB;;GAEG;AACH,MAAM,MAAM,2BAA2B,CAAC,CAAC,IACvC,CAAC,SAAS,UAAU,CAAC,MAAM,UAAU,EAAE,MAAM,SAAS,CAAC,GACnD,UAAU,GACV,CAAC,SAAS,kBAAkB,CAAC,MAAM,UAAU,EAAE,MAAM,SAAS,CAAC,GAC7D,UAAU,GACV,OAAO,CAAC"}