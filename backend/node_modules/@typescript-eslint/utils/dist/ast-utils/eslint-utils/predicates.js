"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.isNotSemicolonToken = exports.isSemicolonToken = exports.isNotOpeningParenToken = exports.isOpeningParenToken = exports.isNotOpeningBracketToken = exports.isOpeningBracketToken = exports.isNotOpeningBraceToken = exports.isOpeningBraceToken = exports.isNotCommentToken = exports.isCommentToken = exports.isNotCommaToken = exports.isCommaToken = exports.isNotColonToken = exports.isColonToken = exports.isNotClosingParenToken = exports.isClosingParenToken = exports.isNotClosingBracketToken = exports.isClosingBracketToken = exports.isNotClosingBraceToken = exports.isClosingBraceToken = exports.isNotArrowToken = exports.isArrowToken = void 0;
const eslintUtils = __importStar(require("@eslint-community/eslint-utils"));
exports.isArrowToken = eslintUtils.isArrowToken;
exports.isNotArrowToken = eslintUtils.isNotArrowToken;
exports.isClosingBraceToken = eslintUtils.isClosingBraceToken;
exports.isNotClosingBraceToken = eslintUtils.isNotClosingBraceToken;
exports.isClosingBracketToken = eslintUtils.isClosingBracketToken;
exports.isNotClosingBracketToken = eslintUtils.isNotClosingBracketToken;
exports.isClosingParenToken = eslintUtils.isClosingParenToken;
exports.isNotClosingParenToken = eslintUtils.isNotClosingParenToken;
exports.isColonToken = eslintUtils.isColonToken;
exports.isNotColonToken = eslintUtils.isNotColonToken;
exports.isCommaToken = eslintUtils.isCommaToken;
exports.isNotCommaToken = eslintUtils.isNotCommaToken;
exports.isCommentToken = eslintUtils.isCommentToken;
exports.isNotCommentToken = eslintUtils.isNotCommentToken;
exports.isOpeningBraceToken = eslintUtils.isOpeningBraceToken;
exports.isNotOpeningBraceToken = eslintUtils.isNotOpeningBraceToken;
exports.isOpeningBracketToken = eslintUtils.isOpeningBracketToken;
exports.isNotOpeningBracketToken = eslintUtils.isNotOpeningBracketToken;
exports.isOpeningParenToken = eslintUtils.isOpeningParenToken;
exports.isNotOpeningParenToken = eslintUtils.isNotOpeningParenToken;
exports.isSemicolonToken = eslintUtils.isSemicolonToken;
exports.isNotSemicolonToken = eslintUtils.isNotSemicolonToken;
