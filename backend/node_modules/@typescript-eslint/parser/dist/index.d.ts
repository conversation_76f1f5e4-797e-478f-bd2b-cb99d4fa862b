export { parse, parseForESLint, type ParserOptions } from './parser';
export { clearCaches, createProgram, type ParserServices, type ParserServicesWithoutTypeInformation, type ParserServicesWithTypeInformation, withoutProjectParserOptions, } from '@typescript-eslint/typescript-estree';
export declare const version: string;
export declare const meta: {
    name: string;
    version: string;
};
//# sourceMappingURL=index.d.ts.map