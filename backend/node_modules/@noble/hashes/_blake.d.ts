/**
 * Internal blake variable.
 * For BLAKE2b, the two extra permutations for rounds 10 and 11 are SIGMA[10..11] = SIGMA[0..1].
 */
export declare const BSIGMA: Uint8Array;
export type Num4 = {
    a: number;
    b: number;
    c: number;
    d: number;
};
export declare function G1s(a: number, b: number, c: number, d: number, x: number): Num4;
export declare function G2s(a: number, b: number, c: number, d: number, x: number): Num4;
//# sourceMappingURL=_blake.d.ts.map