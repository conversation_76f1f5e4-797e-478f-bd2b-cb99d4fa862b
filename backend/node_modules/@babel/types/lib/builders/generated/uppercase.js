"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.JSXIdentifier = exports.JSXFragment = exports.JSXExpressionContainer = exports.JSXEmptyExpression = exports.JSXElement = exports.JSXClosingFragment = exports.JSXClosingElement = exports.JSXAttribute = exports.IntersectionTypeAnnotation = exports.InterpreterDirective = exports.InterfaceTypeAnnotation = exports.InterfaceExtends = exports.InterfaceDeclaration = exports.InferredPredicate = exports.IndexedAccessType = exports.ImportSpecifier = exports.ImportNamespaceSpecifier = exports.ImportExpression = exports.ImportDefaultSpecifier = exports.ImportDeclaration = exports.ImportAttribute = exports.Import = exports.IfStatement = exports.Identifier = exports.GenericTypeAnnotation = exports.FunctionTypeParam = exports.FunctionTypeAnnotation = exports.FunctionExpression = exports.FunctionDeclaration = exports.ForStatement = exports.ForOfStatement = exports.ForInStatement = exports.File = exports.ExpressionStatement = exports.ExportSpecifier = exports.ExportNamespaceSpecifier = exports.ExportNamedDeclaration = exports.ExportDefaultSpecifier = exports.ExportDefaultDeclaration = exports.ExportAllDeclaration = exports.ExistsTypeAnnotation = exports.EnumSymbolBody = exports.EnumStringMember = exports.EnumStringBody = exports.EnumNumberMember = exports.EnumNumberBody = exports.EnumDefaultedMember = exports.EnumDeclaration = exports.EnumBooleanMember = exports.EnumBooleanBody = exports.EmptyTypeAnnotation = exports.EmptyStatement = exports.DoWhileStatement = exports.DoExpression = exports.DirectiveLiteral = exports.Directive = exports.Decorator = exports.DeclaredPredicate = exports.DeclareVariable = exports.DeclareTypeAlias = exports.DeclareOpaqueType = exports.DeclareModuleExports = exports.DeclareModule = exports.DeclareInterface = exports.DeclareFunction = exports.DeclareExportDeclaration = exports.DeclareExportAllDeclaration = exports.DeclareClass = exports.DecimalLiteral = exports.DebuggerStatement = exports.ContinueStatement = exports.ConditionalExpression = exports.ClassProperty = exports.ClassPrivateProperty = exports.ClassPrivateMethod = exports.ClassMethod = exports.ClassImplements = exports.ClassExpression = exports.ClassDeclaration = exports.ClassBody = exports.ClassAccessorProperty = exports.CatchClause = exports.CallExpression = exports.BreakStatement = exports.BooleanTypeAnnotation = exports.BooleanLiteralTypeAnnotation = exports.BooleanLiteral = exports.BlockStatement = exports.BindExpression = exports.BinaryExpression = exports.BigIntLiteral = exports.AwaitExpression = exports.AssignmentPattern = exports.AssignmentExpression = exports.ArrowFunctionExpression = exports.ArrayTypeAnnotation = exports.ArrayPattern = exports.ArrayExpression = exports.ArgumentPlaceholder = exports.AnyTypeAnnotation = void 0;
exports.TSNumberKeyword = exports.TSNullKeyword = exports.TSNonNullExpression = exports.TSNeverKeyword = exports.TSNamespaceExportDeclaration = exports.TSNamedTupleMember = exports.TSModuleDeclaration = exports.TSModuleBlock = exports.TSMethodSignature = exports.TSMappedType = exports.TSLiteralType = exports.TSIntrinsicKeyword = exports.TSIntersectionType = exports.TSInterfaceDeclaration = exports.TSInterfaceBody = exports.TSInstantiationExpression = exports.TSInferType = exports.TSIndexedAccessType = exports.TSIndexSignature = exports.TSImportType = exports.TSImportEqualsDeclaration = exports.TSFunctionType = exports.TSExternalModuleReference = exports.TSExpressionWithTypeArguments = exports.TSExportAssignment = exports.TSEnumMember = exports.TSEnumDeclaration = exports.TSEnumBody = exports.TSDeclareMethod = exports.TSDeclareFunction = exports.TSConstructorType = exports.TSConstructSignatureDeclaration = exports.TSConditionalType = exports.TSCallSignatureDeclaration = exports.TSBooleanKeyword = exports.TSBigIntKeyword = exports.TSAsExpression = exports.TSArrayType = exports.TSAnyKeyword = exports.SymbolTypeAnnotation = exports.SwitchStatement = exports.SwitchCase = exports.Super = exports.StringTypeAnnotation = exports.StringLiteralTypeAnnotation = exports.StringLiteral = exports.StaticBlock = exports.SpreadProperty = exports.SpreadElement = exports.SequenceExpression = exports.ReturnStatement = exports.RestProperty = exports.RestElement = exports.RegexLiteral = exports.RegExpLiteral = exports.RecordExpression = exports.QualifiedTypeIdentifier = exports.Program = exports.PrivateName = exports.Placeholder = exports.PipelineTopicExpression = exports.PipelinePrimaryTopicReference = exports.PipelineBareFunction = exports.ParenthesizedExpression = exports.OptionalMemberExpression = exports.OptionalIndexedAccessType = exports.OptionalCallExpression = exports.OpaqueType = exports.ObjectTypeSpreadProperty = exports.ObjectTypeProperty = exports.ObjectTypeInternalSlot = exports.ObjectTypeIndexer = exports.ObjectTypeCallProperty = exports.ObjectTypeAnnotation = exports.ObjectProperty = exports.ObjectPattern = exports.ObjectMethod = exports.ObjectExpression = exports.NumericLiteral = exports.NumberTypeAnnotation = exports.NumberLiteralTypeAnnotation = exports.NumberLiteral = exports.NullableTypeAnnotation = exports.NullLiteralTypeAnnotation = exports.NullLiteral = exports.Noop = exports.NewExpression = exports.ModuleExpression = exports.MixedTypeAnnotation = exports.MetaProperty = exports.MemberExpression = exports.LogicalExpression = exports.LabeledStatement = exports.JSXText = exports.JSXSpreadChild = exports.JSXSpreadAttribute = exports.JSXOpeningFragment = exports.JSXOpeningElement = exports.JSXNamespacedName = exports.JSXMemberExpression = void 0;
exports.YieldExpression = exports.WithStatement = exports.WhileStatement = exports.VoidTypeAnnotation = exports.Variance = exports.VariableDeclarator = exports.VariableDeclaration = exports.V8IntrinsicIdentifier = exports.UpdateExpression = exports.UnionTypeAnnotation = exports.UnaryExpression = exports.TypeofTypeAnnotation = exports.TypeParameterInstantiation = exports.TypeParameterDeclaration = exports.TypeParameter = exports.TypeCastExpression = exports.TypeAnnotation = exports.TypeAlias = exports.TupleTypeAnnotation = exports.TupleExpression = exports.TryStatement = exports.TopicReference = exports.ThrowStatement = exports.ThisTypeAnnotation = exports.ThisExpression = exports.TemplateLiteral = exports.TemplateElement = exports.TaggedTemplateExpression = exports.TSVoidKeyword = exports.TSUnknownKeyword = exports.TSUnionType = exports.TSUndefinedKeyword = exports.TSTypeReference = exports.TSTypeQuery = exports.TSTypePredicate = exports.TSTypeParameterInstantiation = exports.TSTypeParameterDeclaration = exports.TSTypeParameter = exports.TSTypeOperator = exports.TSTypeLiteral = exports.TSTypeAssertion = exports.TSTypeAnnotation = exports.TSTypeAliasDeclaration = exports.TSTupleType = exports.TSThisType = exports.TSTemplateLiteralType = exports.TSSymbolKeyword = exports.TSStringKeyword = exports.TSSatisfiesExpression = exports.TSRestType = exports.TSQualifiedName = exports.TSPropertySignature = exports.TSParenthesizedType = exports.TSParameterProperty = exports.TSOptionalType = exports.TSObjectKeyword = void 0;
var b = require("./lowercase.js");
var _deprecationWarning = require("../../utils/deprecationWarning.js");
function alias(lowercase) {
  {
    return b[lowercase];
  }
}
const ArrayExpression = exports.ArrayExpression = alias("arrayExpression"),
  AssignmentExpression = exports.AssignmentExpression = alias("assignmentExpression"),
  BinaryExpression = exports.BinaryExpression = alias("binaryExpression"),
  InterpreterDirective = exports.InterpreterDirective = alias("interpreterDirective"),
  Directive = exports.Directive = alias("directive"),
  DirectiveLiteral = exports.DirectiveLiteral = alias("directiveLiteral"),
  BlockStatement = exports.BlockStatement = alias("blockStatement"),
  BreakStatement = exports.BreakStatement = alias("breakStatement"),
  CallExpression = exports.CallExpression = alias("callExpression"),
  CatchClause = exports.CatchClause = alias("catchClause"),
  ConditionalExpression = exports.ConditionalExpression = alias("conditionalExpression"),
  ContinueStatement = exports.ContinueStatement = alias("continueStatement"),
  DebuggerStatement = exports.DebuggerStatement = alias("debuggerStatement"),
  DoWhileStatement = exports.DoWhileStatement = alias("doWhileStatement"),
  EmptyStatement = exports.EmptyStatement = alias("emptyStatement"),
  ExpressionStatement = exports.ExpressionStatement = alias("expressionStatement"),
  File = exports.File = alias("file"),
  ForInStatement = exports.ForInStatement = alias("forInStatement"),
  ForStatement = exports.ForStatement = alias("forStatement"),
  FunctionDeclaration = exports.FunctionDeclaration = alias("functionDeclaration"),
  FunctionExpression = exports.FunctionExpression = alias("functionExpression"),
  Identifier = exports.Identifier = alias("identifier"),
  IfStatement = exports.IfStatement = alias("ifStatement"),
  LabeledStatement = exports.LabeledStatement = alias("labeledStatement"),
  StringLiteral = exports.StringLiteral = alias("stringLiteral"),
  NumericLiteral = exports.NumericLiteral = alias("numericLiteral"),
  NullLiteral = exports.NullLiteral = alias("nullLiteral"),
  BooleanLiteral = exports.BooleanLiteral = alias("booleanLiteral"),
  RegExpLiteral = exports.RegExpLiteral = alias("regExpLiteral"),
  LogicalExpression = exports.LogicalExpression = alias("logicalExpression"),
  MemberExpression = exports.MemberExpression = alias("memberExpression"),
  NewExpression = exports.NewExpression = alias("newExpression"),
  Program = exports.Program = alias("program"),
  ObjectExpression = exports.ObjectExpression = alias("objectExpression"),
  ObjectMethod = exports.ObjectMethod = alias("objectMethod"),
  ObjectProperty = exports.ObjectProperty = alias("objectProperty"),
  RestElement = exports.RestElement = alias("restElement"),
  ReturnStatement = exports.ReturnStatement = alias("returnStatement"),
  SequenceExpression = exports.SequenceExpression = alias("sequenceExpression"),
  ParenthesizedExpression = exports.ParenthesizedExpression = alias("parenthesizedExpression"),
  SwitchCase = exports.SwitchCase = alias("switchCase"),
  SwitchStatement = exports.SwitchStatement = alias("switchStatement"),
  ThisExpression = exports.ThisExpression = alias("thisExpression"),
  ThrowStatement = exports.ThrowStatement = alias("throwStatement"),
  TryStatement = exports.TryStatement = alias("tryStatement"),
  UnaryExpression = exports.UnaryExpression = alias("unaryExpression"),
  UpdateExpression = exports.UpdateExpression = alias("updateExpression"),
  VariableDeclaration = exports.VariableDeclaration = alias("variableDeclaration"),
  VariableDeclarator = exports.VariableDeclarator = alias("variableDeclarator"),
  WhileStatement = exports.WhileStatement = alias("whileStatement"),
  WithStatement = exports.WithStatement = alias("withStatement"),
  AssignmentPattern = exports.AssignmentPattern = alias("assignmentPattern"),
  ArrayPattern = exports.ArrayPattern = alias("arrayPattern"),
  ArrowFunctionExpression = exports.ArrowFunctionExpression = alias("arrowFunctionExpression"),
  ClassBody = exports.ClassBody = alias("classBody"),
  ClassExpression = exports.ClassExpression = alias("classExpression"),
  ClassDeclaration = exports.ClassDeclaration = alias("classDeclaration"),
  ExportAllDeclaration = exports.ExportAllDeclaration = alias("exportAllDeclaration"),
  ExportDefaultDeclaration = exports.ExportDefaultDeclaration = alias("exportDefaultDeclaration"),
  ExportNamedDeclaration = exports.ExportNamedDeclaration = alias("exportNamedDeclaration"),
  ExportSpecifier = exports.ExportSpecifier = alias("exportSpecifier"),
  ForOfStatement = exports.ForOfStatement = alias("forOfStatement"),
  ImportDeclaration = exports.ImportDeclaration = alias("importDeclaration"),
  ImportDefaultSpecifier = exports.ImportDefaultSpecifier = alias("importDefaultSpecifier"),
  ImportNamespaceSpecifier = exports.ImportNamespaceSpecifier = alias("importNamespaceSpecifier"),
  ImportSpecifier = exports.ImportSpecifier = alias("importSpecifier"),
  ImportExpression = exports.ImportExpression = alias("importExpression"),
  MetaProperty = exports.MetaProperty = alias("metaProperty"),
  ClassMethod = exports.ClassMethod = alias("classMethod"),
  ObjectPattern = exports.ObjectPattern = alias("objectPattern"),
  SpreadElement = exports.SpreadElement = alias("spreadElement"),
  Super = exports.Super = alias("super"),
  TaggedTemplateExpression = exports.TaggedTemplateExpression = alias("taggedTemplateExpression"),
  TemplateElement = exports.TemplateElement = alias("templateElement"),
  TemplateLiteral = exports.TemplateLiteral = alias("templateLiteral"),
  YieldExpression = exports.YieldExpression = alias("yieldExpression"),
  AwaitExpression = exports.AwaitExpression = alias("awaitExpression"),
  Import = exports.Import = alias("import"),
  BigIntLiteral = exports.BigIntLiteral = alias("bigIntLiteral"),
  ExportNamespaceSpecifier = exports.ExportNamespaceSpecifier = alias("exportNamespaceSpecifier"),
  OptionalMemberExpression = exports.OptionalMemberExpression = alias("optionalMemberExpression"),
  OptionalCallExpression = exports.OptionalCallExpression = alias("optionalCallExpression"),
  ClassProperty = exports.ClassProperty = alias("classProperty"),
  ClassAccessorProperty = exports.ClassAccessorProperty = alias("classAccessorProperty"),
  ClassPrivateProperty = exports.ClassPrivateProperty = alias("classPrivateProperty"),
  ClassPrivateMethod = exports.ClassPrivateMethod = alias("classPrivateMethod"),
  PrivateName = exports.PrivateName = alias("privateName"),
  StaticBlock = exports.StaticBlock = alias("staticBlock"),
  ImportAttribute = exports.ImportAttribute = alias("importAttribute"),
  AnyTypeAnnotation = exports.AnyTypeAnnotation = alias("anyTypeAnnotation"),
  ArrayTypeAnnotation = exports.ArrayTypeAnnotation = alias("arrayTypeAnnotation"),
  BooleanTypeAnnotation = exports.BooleanTypeAnnotation = alias("booleanTypeAnnotation"),
  BooleanLiteralTypeAnnotation = exports.BooleanLiteralTypeAnnotation = alias("booleanLiteralTypeAnnotation"),
  NullLiteralTypeAnnotation = exports.NullLiteralTypeAnnotation = alias("nullLiteralTypeAnnotation"),
  ClassImplements = exports.ClassImplements = alias("classImplements"),
  DeclareClass = exports.DeclareClass = alias("declareClass"),
  DeclareFunction = exports.DeclareFunction = alias("declareFunction"),
  DeclareInterface = exports.DeclareInterface = alias("declareInterface"),
  DeclareModule = exports.DeclareModule = alias("declareModule"),
  DeclareModuleExports = exports.DeclareModuleExports = alias("declareModuleExports"),
  DeclareTypeAlias = exports.DeclareTypeAlias = alias("declareTypeAlias"),
  DeclareOpaqueType = exports.DeclareOpaqueType = alias("declareOpaqueType"),
  DeclareVariable = exports.DeclareVariable = alias("declareVariable"),
  DeclareExportDeclaration = exports.DeclareExportDeclaration = alias("declareExportDeclaration"),
  DeclareExportAllDeclaration = exports.DeclareExportAllDeclaration = alias("declareExportAllDeclaration"),
  DeclaredPredicate = exports.DeclaredPredicate = alias("declaredPredicate"),
  ExistsTypeAnnotation = exports.ExistsTypeAnnotation = alias("existsTypeAnnotation"),
  FunctionTypeAnnotation = exports.FunctionTypeAnnotation = alias("functionTypeAnnotation"),
  FunctionTypeParam = exports.FunctionTypeParam = alias("functionTypeParam"),
  GenericTypeAnnotation = exports.GenericTypeAnnotation = alias("genericTypeAnnotation"),
  InferredPredicate = exports.InferredPredicate = alias("inferredPredicate"),
  InterfaceExtends = exports.InterfaceExtends = alias("interfaceExtends"),
  InterfaceDeclaration = exports.InterfaceDeclaration = alias("interfaceDeclaration"),
  InterfaceTypeAnnotation = exports.InterfaceTypeAnnotation = alias("interfaceTypeAnnotation"),
  IntersectionTypeAnnotation = exports.IntersectionTypeAnnotation = alias("intersectionTypeAnnotation"),
  MixedTypeAnnotation = exports.MixedTypeAnnotation = alias("mixedTypeAnnotation"),
  EmptyTypeAnnotation = exports.EmptyTypeAnnotation = alias("emptyTypeAnnotation"),
  NullableTypeAnnotation = exports.NullableTypeAnnotation = alias("nullableTypeAnnotation"),
  NumberLiteralTypeAnnotation = exports.NumberLiteralTypeAnnotation = alias("numberLiteralTypeAnnotation"),
  NumberTypeAnnotation = exports.NumberTypeAnnotation = alias("numberTypeAnnotation"),
  ObjectTypeAnnotation = exports.ObjectTypeAnnotation = alias("objectTypeAnnotation"),
  ObjectTypeInternalSlot = exports.ObjectTypeInternalSlot = alias("objectTypeInternalSlot"),
  ObjectTypeCallProperty = exports.ObjectTypeCallProperty = alias("objectTypeCallProperty"),
  ObjectTypeIndexer = exports.ObjectTypeIndexer = alias("objectTypeIndexer"),
  ObjectTypeProperty = exports.ObjectTypeProperty = alias("objectTypeProperty"),
  ObjectTypeSpreadProperty = exports.ObjectTypeSpreadProperty = alias("objectTypeSpreadProperty"),
  OpaqueType = exports.OpaqueType = alias("opaqueType"),
  QualifiedTypeIdentifier = exports.QualifiedTypeIdentifier = alias("qualifiedTypeIdentifier"),
  StringLiteralTypeAnnotation = exports.StringLiteralTypeAnnotation = alias("stringLiteralTypeAnnotation"),
  StringTypeAnnotation = exports.StringTypeAnnotation = alias("stringTypeAnnotation"),
  SymbolTypeAnnotation = exports.SymbolTypeAnnotation = alias("symbolTypeAnnotation"),
  ThisTypeAnnotation = exports.ThisTypeAnnotation = alias("thisTypeAnnotation"),
  TupleTypeAnnotation = exports.TupleTypeAnnotation = alias("tupleTypeAnnotation"),
  TypeofTypeAnnotation = exports.TypeofTypeAnnotation = alias("typeofTypeAnnotation"),
  TypeAlias = exports.TypeAlias = alias("typeAlias"),
  TypeAnnotation = exports.TypeAnnotation = alias("typeAnnotation"),
  TypeCastExpression = exports.TypeCastExpression = alias("typeCastExpression"),
  TypeParameter = exports.TypeParameter = alias("typeParameter"),
  TypeParameterDeclaration = exports.TypeParameterDeclaration = alias("typeParameterDeclaration"),
  TypeParameterInstantiation = exports.TypeParameterInstantiation = alias("typeParameterInstantiation"),
  UnionTypeAnnotation = exports.UnionTypeAnnotation = alias("unionTypeAnnotation"),
  Variance = exports.Variance = alias("variance"),
  VoidTypeAnnotation = exports.VoidTypeAnnotation = alias("voidTypeAnnotation"),
  EnumDeclaration = exports.EnumDeclaration = alias("enumDeclaration"),
  EnumBooleanBody = exports.EnumBooleanBody = alias("enumBooleanBody"),
  EnumNumberBody = exports.EnumNumberBody = alias("enumNumberBody"),
  EnumStringBody = exports.EnumStringBody = alias("enumStringBody"),
  EnumSymbolBody = exports.EnumSymbolBody = alias("enumSymbolBody"),
  EnumBooleanMember = exports.EnumBooleanMember = alias("enumBooleanMember"),
  EnumNumberMember = exports.EnumNumberMember = alias("enumNumberMember"),
  EnumStringMember = exports.EnumStringMember = alias("enumStringMember"),
  EnumDefaultedMember = exports.EnumDefaultedMember = alias("enumDefaultedMember"),
  IndexedAccessType = exports.IndexedAccessType = alias("indexedAccessType"),
  OptionalIndexedAccessType = exports.OptionalIndexedAccessType = alias("optionalIndexedAccessType"),
  JSXAttribute = exports.JSXAttribute = alias("jsxAttribute"),
  JSXClosingElement = exports.JSXClosingElement = alias("jsxClosingElement"),
  JSXElement = exports.JSXElement = alias("jsxElement"),
  JSXEmptyExpression = exports.JSXEmptyExpression = alias("jsxEmptyExpression"),
  JSXExpressionContainer = exports.JSXExpressionContainer = alias("jsxExpressionContainer"),
  JSXSpreadChild = exports.JSXSpreadChild = alias("jsxSpreadChild"),
  JSXIdentifier = exports.JSXIdentifier = alias("jsxIdentifier"),
  JSXMemberExpression = exports.JSXMemberExpression = alias("jsxMemberExpression"),
  JSXNamespacedName = exports.JSXNamespacedName = alias("jsxNamespacedName"),
  JSXOpeningElement = exports.JSXOpeningElement = alias("jsxOpeningElement"),
  JSXSpreadAttribute = exports.JSXSpreadAttribute = alias("jsxSpreadAttribute"),
  JSXText = exports.JSXText = alias("jsxText"),
  JSXFragment = exports.JSXFragment = alias("jsxFragment"),
  JSXOpeningFragment = exports.JSXOpeningFragment = alias("jsxOpeningFragment"),
  JSXClosingFragment = exports.JSXClosingFragment = alias("jsxClosingFragment"),
  Noop = exports.Noop = alias("noop"),
  Placeholder = exports.Placeholder = alias("placeholder"),
  V8IntrinsicIdentifier = exports.V8IntrinsicIdentifier = alias("v8IntrinsicIdentifier"),
  ArgumentPlaceholder = exports.ArgumentPlaceholder = alias("argumentPlaceholder"),
  BindExpression = exports.BindExpression = alias("bindExpression"),
  Decorator = exports.Decorator = alias("decorator"),
  DoExpression = exports.DoExpression = alias("doExpression"),
  ExportDefaultSpecifier = exports.ExportDefaultSpecifier = alias("exportDefaultSpecifier"),
  RecordExpression = exports.RecordExpression = alias("recordExpression"),
  TupleExpression = exports.TupleExpression = alias("tupleExpression"),
  DecimalLiteral = exports.DecimalLiteral = alias("decimalLiteral"),
  ModuleExpression = exports.ModuleExpression = alias("moduleExpression"),
  TopicReference = exports.TopicReference = alias("topicReference"),
  PipelineTopicExpression = exports.PipelineTopicExpression = alias("pipelineTopicExpression"),
  PipelineBareFunction = exports.PipelineBareFunction = alias("pipelineBareFunction"),
  PipelinePrimaryTopicReference = exports.PipelinePrimaryTopicReference = alias("pipelinePrimaryTopicReference"),
  TSParameterProperty = exports.TSParameterProperty = alias("tsParameterProperty"),
  TSDeclareFunction = exports.TSDeclareFunction = alias("tsDeclareFunction"),
  TSDeclareMethod = exports.TSDeclareMethod = alias("tsDeclareMethod"),
  TSQualifiedName = exports.TSQualifiedName = alias("tsQualifiedName"),
  TSCallSignatureDeclaration = exports.TSCallSignatureDeclaration = alias("tsCallSignatureDeclaration"),
  TSConstructSignatureDeclaration = exports.TSConstructSignatureDeclaration = alias("tsConstructSignatureDeclaration"),
  TSPropertySignature = exports.TSPropertySignature = alias("tsPropertySignature"),
  TSMethodSignature = exports.TSMethodSignature = alias("tsMethodSignature"),
  TSIndexSignature = exports.TSIndexSignature = alias("tsIndexSignature"),
  TSAnyKeyword = exports.TSAnyKeyword = alias("tsAnyKeyword"),
  TSBooleanKeyword = exports.TSBooleanKeyword = alias("tsBooleanKeyword"),
  TSBigIntKeyword = exports.TSBigIntKeyword = alias("tsBigIntKeyword"),
  TSIntrinsicKeyword = exports.TSIntrinsicKeyword = alias("tsIntrinsicKeyword"),
  TSNeverKeyword = exports.TSNeverKeyword = alias("tsNeverKeyword"),
  TSNullKeyword = exports.TSNullKeyword = alias("tsNullKeyword"),
  TSNumberKeyword = exports.TSNumberKeyword = alias("tsNumberKeyword"),
  TSObjectKeyword = exports.TSObjectKeyword = alias("tsObjectKeyword"),
  TSStringKeyword = exports.TSStringKeyword = alias("tsStringKeyword"),
  TSSymbolKeyword = exports.TSSymbolKeyword = alias("tsSymbolKeyword"),
  TSUndefinedKeyword = exports.TSUndefinedKeyword = alias("tsUndefinedKeyword"),
  TSUnknownKeyword = exports.TSUnknownKeyword = alias("tsUnknownKeyword"),
  TSVoidKeyword = exports.TSVoidKeyword = alias("tsVoidKeyword"),
  TSThisType = exports.TSThisType = alias("tsThisType"),
  TSFunctionType = exports.TSFunctionType = alias("tsFunctionType"),
  TSConstructorType = exports.TSConstructorType = alias("tsConstructorType"),
  TSTypeReference = exports.TSTypeReference = alias("tsTypeReference"),
  TSTypePredicate = exports.TSTypePredicate = alias("tsTypePredicate"),
  TSTypeQuery = exports.TSTypeQuery = alias("tsTypeQuery"),
  TSTypeLiteral = exports.TSTypeLiteral = alias("tsTypeLiteral"),
  TSArrayType = exports.TSArrayType = alias("tsArrayType"),
  TSTupleType = exports.TSTupleType = alias("tsTupleType"),
  TSOptionalType = exports.TSOptionalType = alias("tsOptionalType"),
  TSRestType = exports.TSRestType = alias("tsRestType"),
  TSNamedTupleMember = exports.TSNamedTupleMember = alias("tsNamedTupleMember"),
  TSUnionType = exports.TSUnionType = alias("tsUnionType"),
  TSIntersectionType = exports.TSIntersectionType = alias("tsIntersectionType"),
  TSConditionalType = exports.TSConditionalType = alias("tsConditionalType"),
  TSInferType = exports.TSInferType = alias("tsInferType"),
  TSParenthesizedType = exports.TSParenthesizedType = alias("tsParenthesizedType"),
  TSTypeOperator = exports.TSTypeOperator = alias("tsTypeOperator"),
  TSIndexedAccessType = exports.TSIndexedAccessType = alias("tsIndexedAccessType"),
  TSMappedType = exports.TSMappedType = alias("tsMappedType"),
  TSTemplateLiteralType = exports.TSTemplateLiteralType = alias("tsTemplateLiteralType"),
  TSLiteralType = exports.TSLiteralType = alias("tsLiteralType"),
  TSExpressionWithTypeArguments = exports.TSExpressionWithTypeArguments = alias("tsExpressionWithTypeArguments"),
  TSInterfaceDeclaration = exports.TSInterfaceDeclaration = alias("tsInterfaceDeclaration"),
  TSInterfaceBody = exports.TSInterfaceBody = alias("tsInterfaceBody"),
  TSTypeAliasDeclaration = exports.TSTypeAliasDeclaration = alias("tsTypeAliasDeclaration"),
  TSInstantiationExpression = exports.TSInstantiationExpression = alias("tsInstantiationExpression"),
  TSAsExpression = exports.TSAsExpression = alias("tsAsExpression"),
  TSSatisfiesExpression = exports.TSSatisfiesExpression = alias("tsSatisfiesExpression"),
  TSTypeAssertion = exports.TSTypeAssertion = alias("tsTypeAssertion"),
  TSEnumBody = exports.TSEnumBody = alias("tsEnumBody"),
  TSEnumDeclaration = exports.TSEnumDeclaration = alias("tsEnumDeclaration"),
  TSEnumMember = exports.TSEnumMember = alias("tsEnumMember"),
  TSModuleDeclaration = exports.TSModuleDeclaration = alias("tsModuleDeclaration"),
  TSModuleBlock = exports.TSModuleBlock = alias("tsModuleBlock"),
  TSImportType = exports.TSImportType = alias("tsImportType"),
  TSImportEqualsDeclaration = exports.TSImportEqualsDeclaration = alias("tsImportEqualsDeclaration"),
  TSExternalModuleReference = exports.TSExternalModuleReference = alias("tsExternalModuleReference"),
  TSNonNullExpression = exports.TSNonNullExpression = alias("tsNonNullExpression"),
  TSExportAssignment = exports.TSExportAssignment = alias("tsExportAssignment"),
  TSNamespaceExportDeclaration = exports.TSNamespaceExportDeclaration = alias("tsNamespaceExportDeclaration"),
  TSTypeAnnotation = exports.TSTypeAnnotation = alias("tsTypeAnnotation"),
  TSTypeParameterInstantiation = exports.TSTypeParameterInstantiation = alias("tsTypeParameterInstantiation"),
  TSTypeParameterDeclaration = exports.TSTypeParameterDeclaration = alias("tsTypeParameterDeclaration"),
  TSTypeParameter = exports.TSTypeParameter = alias("tsTypeParameter");
const NumberLiteral = exports.NumberLiteral = b.numberLiteral,
  RegexLiteral = exports.RegexLiteral = b.regexLiteral,
  RestProperty = exports.RestProperty = b.restProperty,
  SpreadProperty = exports.SpreadProperty = b.spreadProperty;

//# sourceMappingURL=uppercase.js.map
